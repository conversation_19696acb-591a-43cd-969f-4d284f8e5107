{% extends 'base.html' %}
{% load static %}

{% block title %}إحصائيات الإجازات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
        border: 2px solid #e3e6f0;
        border-radius: 15px;
        color: #333;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
        pointer-events: none;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
    }

    .stats-card.primary {
        border-left: 4px solid #4e73df;
    }

    .stats-card.success {
        border-left: 4px solid #1cc88a;
    }

    .stats-card.info {
        border-left: 4px solid #36b9cc;
    }

    .stats-card.warning {
        border-left: 4px solid #f6c23e;
    }

    .stats-card.danger {
        border-left: 4px solid #e74a3b;
    }
    
    .stats-icon {
        font-size: 2.5rem;
        opacity: 0.7;
        color: #5a5c69;
    }
    
    .chart-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .chart-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }
    
    .chart-header {
        background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
        color: #333;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e3e6f0;
    }

    .chart-type-btn {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .chart-type-btn:hover {
        background-color: #f8f9fc;
        transform: translateX(5px);
    }

    .chart-container {
        position: relative;
        height: 400px;
        padding: 20px;
    }

    .chart-container canvas {
        cursor: pointer;
    }
    
    .table-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }
    
    .table-header {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    
    .animated-counter {
        font-size: 2.5rem;
        font-weight: bold;
        color: #333;
    }

    .stats-label {
        color: #5a5c69;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }
    
    .progress-animated {
        background: linear-gradient(90deg, #4e73df, #36b9cc, #1cc88a);
        background-size: 200% 100%;
        animation: gradient 2s ease infinite;
    }
    
    @keyframes gradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    
    .department-badge {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 0.5rem 1rem;
        font-weight: 500;
    }
    
    .employee-rank {
        background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-2">
                <i class="fas fa-chart-bar me-3"></i>
                إحصائيات الإجازات والمغادرات
            </h1>
            <p class="mb-0 opacity-75">
                <i class="fas fa-calendar-alt me-2"></i>
                تقرير شامل لعام {{ current_year }}
            </p>
        </div>
        <div>
            <a href="{% url 'leaves:leave_reports' %}" class="btn btn-light btn-lg">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للتقارير
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card primary h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            إجمالي الموظفين
                        </div>
                        <div class="animated-counter">{{ total_employees }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card success h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            إجمالي الإجازات
                        </div>
                        <div class="animated-counter">{{ total_leaves }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-check stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card info h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            إجمالي أيام الإجازات
                        </div>
                        <div class="animated-counter">{{ total_leave_days }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card warning h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            متوسط الأيام لكل موظف
                        </div>
                        <div class="animated-counter">{{ avg_days_per_employee }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Departure Statistics -->
<div class="row mb-4">
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card stats-card danger h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            إجمالي المغادرات
                        </div>
                        <div class="animated-counter">{{ total_departures }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-sign-out-alt stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card stats-card h-100" style="border-left: 4px solid #ff6b6b;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            المغادرات الشخصية
                        </div>
                        <div class="animated-counter">{{ personal_departures }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-clock stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card stats-card h-100" style="border-left: 4px solid #74b9ff;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            المغادرات الرسمية
                        </div>
                        <div class="animated-counter">{{ official_departures }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-briefcase stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Leave Types Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card chart-card h-100">
            <div class="chart-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات الإجازات حسب النوع
                </h6>
                <div class="dropdown">
                    <button class="btn btn-sm btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item chart-type-btn" href="#" data-chart="leaveType" data-type="doughnut">رسم حلقي</a></li>
                        <li><a class="dropdown-item chart-type-btn" href="#" data-chart="leaveType" data-type="pie">رسم دائري</a></li>
                        <li><a class="dropdown-item chart-type-btn" href="#" data-chart="leaveType" data-type="polarArea">رسم قطبي</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="downloadChart('leaveTypeChart', 'إحصائيات_الإجازات_حسب_النوع')">تحميل كصورة</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="leaveTypeChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card chart-card h-100">
            <div class="chart-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-chart-bar me-2"></i>
                    الإجازات حسب الشهر
                </h6>
                <div class="dropdown">
                    <button class="btn btn-sm btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item chart-type-btn" href="#" data-chart="monthly" data-type="bar">رسم شريطي</a></li>
                        <li><a class="dropdown-item chart-type-btn" href="#" data-chart="monthly" data-type="line">رسم خطي</a></li>
                        <li><a class="dropdown-item chart-type-btn" href="#" data-chart="monthly" data-type="radar">رسم رادار</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="downloadChart('monthlyChart', 'الإجازات_حسب_الشهر')">تحميل كصورة</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="monthlyChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leave Types Statistics Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card table-card">
            <div class="card-header table-header py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-list me-2"></i>
                    تفاصيل الإجازات حسب النوع
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>نوع الإجازة</th>
                                <th class="text-center">عدد الإجازات</th>
                                <th class="text-center">إجمالي الأيام</th>
                                <th class="text-center">متوسط الأيام</th>
                                <th class="text-center">النسبة المئوية</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stat in leave_type_stats %}
                            <tr>
                                <td>
                                    <span class="badge department-badge">
                                        {{ stat.leave_type.get_name_display }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <strong>{{ stat.count }}</strong>
                                </td>
                                <td class="text-center">
                                    <strong>{{ stat.total_days }}</strong>
                                </td>
                                <td class="text-center">
                                    {{ stat.avg_days }}
                                </td>
                                <td class="text-center">
                                    {% if total_leave_days > 0 %}
                                        {% widthratio stat.total_days total_leave_days 100 %}%
                                    {% else %}
                                        0%
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Department Statistics -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card table-card">
            <div class="card-header table-header py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-building me-2"></i>
                    إحصائيات الأقسام
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>القسم</th>
                                <th class="text-center">عدد الموظفين</th>
                                <th class="text-center">عدد الإجازات</th>
                                <th class="text-center">إجمالي الأيام</th>
                                <th class="text-center">متوسط الأيام لكل موظف</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stat in department_stats %}
                            <tr>
                                <td>
                                    <span class="badge department-badge">
                                        {{ stat.department.name }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <strong>{{ stat.employee_count }}</strong>
                                </td>
                                <td class="text-center">
                                    {{ stat.leave_count }}
                                </td>
                                <td class="text-center">
                                    {{ stat.total_days }}
                                </td>
                                <td class="text-center">
                                    {{ stat.avg_per_employee }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Employees -->
{% if top_employees %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card table-card">
            <div class="card-header table-header py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-trophy me-2"></i>
                    الموظفون الأكثر استخداماً للإجازات (أعلى 10)
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الترتيب</th>
                                <th>الرقم الوزاري</th>
                                <th>اسم الموظف</th>
                                <th class="text-center">إجمالي أيام الإجازات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee_data in top_employees %}
                            <tr>
                                <td>
                                    <div class="employee-rank">
                                        {{ forloop.counter }}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ employee_data.employee.ministry_number }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{% url 'employees:employee_detail' employee_data.employee.pk %}" class="text-decoration-none">
                                        {{ employee_data.employee.full_name }}
                                    </a>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-primary fs-6">
                                        {{ employee_data.total_days }} يوم
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Chart data
    const leaveTypeLabels = [
        {% for stat in leave_type_stats %}
            '{{ stat.leave_type.get_name_display }}'{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];

    const leaveTypeValues = [
        {% for stat in leave_type_stats %}
            {{ stat.total_days }}{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];

    const monthlyLabels = [
        {% for stat in monthly_stats %}
            '{{ stat.month_name_ar }}'{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];

    const monthlyValues = [
        {% for stat in monthly_stats %}
            {{ stat.count }}{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];

    // Chart instances
    let leaveTypeChart, monthlyChart;

    // Chart colors
    const chartColors = [
        'rgba(78, 115, 223, 0.8)',   // سنوية - أزرق
        'rgba(28, 200, 138, 0.8)',   // مرضية - أخضر
        'rgba(54, 185, 204, 0.8)',   // عرضية - سماوي
        'rgba(246, 194, 62, 0.8)',   // حج - أصفر
        'rgba(231, 74, 59, 0.8)',    // أخرى - أحمر
        'rgba(133, 135, 150, 0.8)'   // إضافية - رمادي
    ];

    const chartBorderColors = [
        'rgba(78, 115, 223, 1)',
        'rgba(28, 200, 138, 1)',
        'rgba(54, 185, 204, 1)',
        'rgba(246, 194, 62, 1)',
        'rgba(231, 74, 59, 1)',
        'rgba(133, 135, 150, 1)'
    ];

    // Create Leave Type Chart
    function createLeaveTypeChart(type = 'doughnut') {
        const ctx = document.getElementById('leaveTypeChart').getContext('2d');

        if (leaveTypeChart) {
            leaveTypeChart.destroy();
        }

        const config = {
            type: type,
            data: {
                labels: leaveTypeLabels,
                datasets: [{
                    data: leaveTypeValues,
                    backgroundColor: chartColors,
                    borderColor: chartBorderColors,
                    borderWidth: 2,
                    hoverOffset: 15
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: type === 'doughnut' ? '60%' : 0,
                animation: {
                    animateScale: true,
                    animateRotate: true,
                    duration: 1500,
                    easing: 'easeOutQuart'
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12,
                                family: 'Tajawal, sans-serif'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#fff',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${label}: ${value} يوم (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        };

        leaveTypeChart = new Chart(ctx, config);
    }

    // Create Monthly Chart
    function createMonthlyChart(type = 'bar') {
        const ctx = document.getElementById('monthlyChart').getContext('2d');

        if (monthlyChart) {
            monthlyChart.destroy();
        }

        const config = {
            type: type,
            data: {
                labels: monthlyLabels,
                datasets: [{
                    label: 'عدد الإجازات',
                    data: monthlyValues,
                    backgroundColor: function(context) {
                        if (type === 'radar') {
                            return 'rgba(78, 115, 223, 0.2)';
                        }
                        const chart = context.chart;
                        const {ctx, chartArea} = chart;
                        if (!chartArea) {
                            return 'rgba(78, 115, 223, 0.8)';
                        }
                        const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                        gradient.addColorStop(0, 'rgba(78, 115, 223, 0.8)');
                        gradient.addColorStop(1, 'rgba(54, 185, 204, 0.8)');
                        return gradient;
                    },
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 2,
                    borderRadius: type === 'bar' ? 8 : 0,
                    borderSkipped: false,
                    tension: type === 'line' ? 0.4 : 0,
                    fill: type === 'radar' ? true : false,
                    pointBackgroundColor: type === 'radar' ? 'rgba(78, 115, 223, 1)' : undefined,
                    pointBorderColor: type === 'radar' ? '#fff' : undefined,
                    pointHoverBackgroundColor: type === 'radar' ? '#fff' : undefined,
                    pointHoverBorderColor: type === 'radar' ? 'rgba(78, 115, 223, 1)' : undefined
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeOutQuart'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#fff',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return `عدد الإجازات: ${context.raw}`;
                            }
                        }
                    }
                },
                scales: type === 'radar' ? {
                    r: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1,
                            font: {
                                family: 'Tajawal, sans-serif'
                            }
                        }
                    }
                } : {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1,
                            font: {
                                family: 'Tajawal, sans-serif'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Tajawal, sans-serif'
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                }
            }
        };

        monthlyChart = new Chart(ctx, config);
    }

    // Initialize charts
    createLeaveTypeChart('doughnut');
    createMonthlyChart('bar');

    // Chart type change handlers
    document.querySelectorAll('.chart-type-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const chartType = this.getAttribute('data-chart');
            const type = this.getAttribute('data-type');

            if (chartType === 'leaveType') {
                createLeaveTypeChart(type);
            } else if (chartType === 'monthly') {
                createMonthlyChart(type);
            }

            // Update button text to show active type
            const dropdown = this.closest('.dropdown');
            const button = dropdown.querySelector('.dropdown-toggle');
            button.innerHTML = `<i class="fas fa-cog"></i> ${this.textContent}`;
        });
    });

    // Download chart function
    function downloadChart(chartId, filename) {
        const canvas = document.getElementById(chartId);
        const url = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = filename + '.png';
        link.href = url;
        link.click();
    }

    // Make download function global
    window.downloadChart = downloadChart;

    // Animate counters
    function animateCounters() {
        const counters = document.querySelectorAll('.animated-counter');
        counters.forEach(counter => {
            const target = parseInt(counter.textContent);
            const increment = target / 100;
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    counter.textContent = target;
                    clearInterval(timer);
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 20);
        });
    }

    // Start counter animation after page load
    setTimeout(animateCounters, 500);



    // Animate counters
    const counters = document.querySelectorAll('.animated-counter');
    counters.forEach(counter => {
        const target = parseInt(counter.textContent);
        let current = 0;
        const increment = target / 50;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 30);
    });
});
</script>
{% endblock %}
