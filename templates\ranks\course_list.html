{% extends 'base.html' %}
{% load static %}

{% block title %}أسماء الدورات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-graduation-cap me-2"></i>أسماء الدورات</h2>
    <div>
        <a href="{% url 'ranks:course_create' %}" class="btn btn-success me-2">
            <i class="fas fa-plus"></i> إضافة دورة جديدة
        </a>
        <a href="{% url 'ranks:employee_course_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للدورات
        </a>
    </div>
</div>

<!-- Courses List -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">جميع الدورات</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr class="text-center">
                        <th><i class="fas fa-graduation-cap me-1"></i> اسم الدورة</th>
                        <th><i class="fas fa-info-circle me-1"></i> الوصف</th>
                        <th><i class="fas fa-clock me-1"></i> عدد الساعات</th>
                        <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for course in courses %}
                    <tr class="text-center">
                        <td class="text-start">
                            <strong>{{ course.name }}</strong>
                        </td>
                        <td class="text-start">{{ course.description|default:"-" }}</td>
                        <td>
                            {% if course.hours > 0 %}
                                <span class="badge bg-info">{{ course.hours }} ساعة</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'ranks:course_detail' course.pk %}" class="btn btn-info btn-sm">
                                <i class="fas fa-eye"></i> معاينة
                            </a>
                            <a href="{% url 'ranks:course_update' course.pk %}" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'ranks:course_delete' course.pk %}" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="text-center">لا توجد دورات مضافة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>


<!-- Delete Course Modal -->
<div class="modal fade" id="deleteCourseModal" tabindex="-1" aria-labelledby="deleteCourseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCourseModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف الدورة: <strong id="deleteCourseNameModal"></strong>؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    تحذير: سيتم حذف جميع سجلات الموظفين المرتبطة بهذه الدورة أيضاً.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteCourseForm" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete course modal
    document.querySelectorAll('.delete-course').forEach(button => {
        button.addEventListener('click', function() {
            const courseId = this.getAttribute('data-id');
            const courseName = this.getAttribute('data-name');
            
            document.getElementById('deleteCourseNameModal').textContent = courseName;
            document.getElementById('deleteCourseForm').action = `/ranks/courses/${courseId}/delete/`;
            
            new bootstrap.Modal(document.getElementById('deleteCourseModal')).show();
        });
    });

    // Add Bootstrap classes to form fields
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(element) {
        element.classList.add('form-control');
    });
});
</script>
{% endblock %}
