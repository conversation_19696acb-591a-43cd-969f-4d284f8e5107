#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت اختبار الاتصال بقاعدة بيانات MySQL
MySQL Connection Test Script
"""

import os
import sys
import django
from django.db import connection
from django.core.management import execute_from_command_line

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        print("اختبار الاتصال بقاعدة بيانات MySQL...")
        print("=" * 50)
        
        # اختبار الاتصال
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
        if result:
            print("✓ تم الاتصال بقاعدة البيانات بنجاح!")
            
            # عرض معلومات قاعدة البيانات
            with connection.cursor() as cursor:
                cursor.execute("SELECT DATABASE()")
                db_name = cursor.fetchone()[0]
                print(f"✓ اسم قاعدة البيانات: {db_name}")
                
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                print(f"✓ إصدار MySQL: {version}")
                
                cursor.execute("SELECT @@character_set_database")
                charset = cursor.fetchone()[0]
                print(f"✓ ترميز قاعدة البيانات: {charset}")
                
                cursor.execute("SELECT @@collation_database")
                collation = cursor.fetchone()[0]
                print(f"✓ ترتيب قاعدة البيانات: {collation}")
            
            return True
            
    except Exception as e:
        print(f"✗ فشل الاتصال بقاعدة البيانات: {str(e)}")
        print("\nتأكد من:")
        print("1. تثبيت MySQL Server")
        print("2. تشغيل خدمة MySQL")
        print("3. إنشاء قاعدة البيانات والمستخدم")
        print("4. صحة معلومات الاتصال في settings.py")
        return False

def check_mysql_tables():
    """فحص الجداول الموجودة في قاعدة البيانات"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            if tables:
                print(f"\n✓ عدد الجداول الموجودة: {len(tables)}")
                print("الجداول:")
                for table in tables[:10]:  # عرض أول 10 جداول
                    print(f"  - {table[0]}")
                if len(tables) > 10:
                    print(f"  ... و {len(tables) - 10} جدول آخر")
            else:
                print("\n! لا توجد جداول في قاعدة البيانات")
                print("  قم بتشغيل: python manage.py migrate")
                
    except Exception as e:
        print(f"✗ خطأ في فحص الجداول: {str(e)}")

if __name__ == "__main__":
    print("بدء اختبار الاتصال بـ MySQL...")
    
    if test_database_connection():
        print("\n" + "=" * 50)
        check_mysql_tables()
        print("\n" + "=" * 50)
        print("✓ اختبار الاتصال مكتمل بنجاح!")
    else:
        print("\n" + "=" * 50)
        print("✗ فشل اختبار الاتصال!")
        sys.exit(1)
