{% extends 'base.html' %}
{% load static %}

{% block title %}حذف الدورة - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>حذف الدورة</h2>
    <a href="{% url 'ranks:course_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة لأسماء الدورات
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 bg-danger text-white">
        <h6 class="m-0 font-weight-bold">
            <i class="fas fa-exclamation-triangle me-2"></i>
            تأكيد حذف الدورة
        </h6>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle"></i> تحذير!</h5>
            <p>هل أنت متأكد من رغبتك في حذف الدورة التالية؟</p>

            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>تفاصيل الدورة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم الدورة:</strong>
                                <span class="text-primary">{{ course.name }}</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الوصف:</strong>
                                <span class="text-muted">{{ course.description|default:"لا يوجد وصف" }}</span>
                            </p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-calendar-plus me-1"></i>
                                <strong>تاريخ الإضافة:</strong> {{ course.created_at|date:"d/m/Y - H:i" }}
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-calendar-edit me-1"></i>
                                <strong>آخر تحديث:</strong> {{ course.updated_at|date:"d/m/Y - H:i" }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="alert alert-warning mt-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير مهم:</strong> سيتم حذف جميع سجلات الموظفين المرتبطة بهذه الدورة أيضاً.
                <br><small class="text-muted">هذا الإجراء لا يمكن التراجع عنه!</small>
            </div>
        </div>

        <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
            <a href="{% url 'ranks:course_list' %}" class="btn btn-secondary btn-lg me-3">
                <i class="fas fa-arrow-left me-2"></i> العودة للقائمة
            </a>
            <form method="post" style="display: inline;" onsubmit="return confirmDelete()">
                {% csrf_token %}
                <button type="submit" class="btn btn-danger btn-lg">
                    <i class="fas fa-trash-alt me-2"></i> تأكيد الحذف نهائياً
                </button>
            </form>
        </div>

        <script>
        function confirmDelete() {
            return confirm('هل أنت متأكد من رغبتك في حذف هذه الدورة نهائياً؟\n\nسيتم حذف جميع سجلات الموظفين المرتبطة بها!\n\nلا يمكن التراجع عن هذا الإجراء!');
        }
        </script>
    </div>
</div>
{% endblock %}
