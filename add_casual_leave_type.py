#!/usr/bin/env python
"""
Script to add casual leave type to the database
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

from leaves.models import LeaveType

def add_casual_leave_type():
    """Add casual leave type if it doesn't exist"""
    try:
        # Check if casual leave type already exists
        casual_leave, created = LeaveType.objects.get_or_create(
            name='casual',
            defaults={
                'description': 'إجازة عرضية للموظفين',
                'max_days_per_year': 7  # Default 7 days per year
            }
        )
        
        if created:
            print(f"✅ تم إنشاء نوع الإجازة العرضية بنجاح: {casual_leave}")
        else:
            print(f"ℹ️ نوع الإجازة العرضية موجود مسبقاً: {casual_leave}")
            
        return casual_leave
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء نوع الإجازة العرضية: {e}")
        return None

if __name__ == "__main__":
    print("🔄 بدء إضافة نوع الإجازة العرضية...")
    casual_leave = add_casual_leave_type()
    
    if casual_leave:
        print("✅ تم الانتهاء بنجاح!")
        print(f"📋 تفاصيل الإجازة العرضية:")
        print(f"   - الاسم: {casual_leave.name}")
        print(f"   - العرض: {casual_leave.get_name_display()}")
        print(f"   - الوصف: {casual_leave.description}")
        print(f"   - الحد الأقصى: {casual_leave.max_days_per_year} أيام/سنة")
    else:
        print("❌ فشل في إضافة نوع الإجازة العرضية!")
        sys.exit(1)
