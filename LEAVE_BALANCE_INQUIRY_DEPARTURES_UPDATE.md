# إضافة المغادرات الخاصة لصفحة استعلام رصيد الإجازات
# Add Personal Departures to Leave Balance Inquiry Page

## ✅ التحديث المنفذ

### 🎯 الهدف:
إضافة عمود "المغادرات الخاصة" إلى جدول ملخص رصيد الإجازات في صفحة استعلام رصيد الإجازات، وتحديث حساب الرصيد المتبقي ليشمل المغادرات الخاصة.

## 🔧 التغييرات المطبقة

### **1. تحديث `home/views.py` - دالة `get_employee_leave_balance`:**

#### **أ. إضافة حساب المغادرات الخاصة:**
```python
# قبل التحديث
used_days = used_leaves['total_days'] or 0
remaining = balance.initial_balance - used_days

balance_data.append({
    'type_name': leave_type.get_name_display(),
    'initial_balance': balance.initial_balance,
    'used_balance': used_days,
    'remaining_balance': remaining if remaining >= 0 else 0
})

# بعد التحديث
used_days = used_leaves['total_days'] or 0

# حساب المغادرات الخاصة للإجازة السنوية فقط
departure_days = 0
if leave_type.name == 'annual':
    from leaves.models import Departure
    departures = Departure.objects.filter(
        employee=employee,
        date__year=current_year,
        status='approved',
        departure_type='personal'  # المغادرات الخاصة فقط
    )
    
    for departure in departures:
        departure_days += departure.calculate_duration_days()

remaining = balance.initial_balance - used_days - departure_days

balance_data.append({
    'type_name': leave_type.get_name_display(),
    'initial_balance': balance.initial_balance,
    'used_balance': used_days,
    'departure_days': departure_days,
    'remaining_balance': remaining if remaining >= 0 else 0
})
```

#### **ب. تحديث الحالات الافتراضية:**
```python
# إضافة departure_days: 0 لجميع الحالات الافتراضية
balance_data.append({
    'type_name': leave_type.get_name_display(),
    'initial_balance': 0,
    'used_balance': 0,
    'departure_days': 0,  # ← جديد
    'remaining_balance': 0
})
```

### **2. تحديث `templates/home/<USER>

#### **أ. تحديث رأس الجدول:**
```html
<!-- قبل التحديث -->
<tr>
    <th rowspan="2" class="text-center align-middle">نوع الإجازة</th>
    <th colspan="3" class="text-center">رصيد الإجازات</th>
</tr>
<tr>
    <th class="bg-success text-white text-center">الرصيد الأولي</th>
    <th class="bg-danger text-white text-center">الإجازات المستخدمة</th>
    <th class="bg-info text-white text-center">الرصيد المتبقي</th>
</tr>

<!-- بعد التحديث -->
<tr>
    <th rowspan="2" class="text-center align-middle">نوع الإجازة</th>
    <th colspan="4" class="text-center">رصيد الإجازات</th>
</tr>
<tr>
    <th class="bg-success text-white text-center">الرصيد الأولي</th>
    <th class="bg-danger text-white text-center">الإجازات المستخدمة</th>
    <th class="bg-warning text-dark text-center">المغادرات الخاصة</th>
    <th class="bg-info text-white text-center">الرصيد المتبقي</th>
</tr>
```

#### **ب. تحديث JavaScript لعرض البيانات:**
```javascript
// قبل التحديث
tableBodyHtml += `
    <tr class="text-center">
        <td class="text-start"><strong>${balance.type_name}</strong></td>
        <td class="bg-success bg-opacity-25">${balance.initial_balance}</td>
        <td class="bg-danger bg-opacity-25">${balance.used_balance}</td>
        <td class="bg-info bg-opacity-25">${balance.remaining_balance}</td>
    </tr>
`;

// بعد التحديث
// تحديد ما إذا كان نوع الإجازة سنوية لعرض المغادرات
const isAnnualLeave = balance.type_name.includes('سنوية') || balance.type_name.includes('السنوية');
const departureDisplay = isAnnualLeave ? balance.departure_days || 0 : '-';

tableBodyHtml += `
    <tr class="text-center">
        <td class="text-start"><strong>${balance.type_name}</strong></td>
        <td class="bg-success bg-opacity-25">${balance.initial_balance}</td>
        <td class="bg-danger bg-opacity-25">${balance.used_balance}</td>
        <td class="bg-warning bg-opacity-25">${departureDisplay}</td>
        <td class="bg-info bg-opacity-25">${balance.remaining_balance}</td>
    </tr>
`;
```

## 📊 النتائج المحققة

### **الجدول المحدث:**
| نوع الإجازة | الرصيد الأولي | الإجازات المستخدمة | **المغادرات الخاصة** | الرصيد المتبقي |
|-------------|---------------|-------------------|---------------------|----------------|
| **الإجازة السنوية** | 30 | 10 | **3.5** | 16.5 |
| **الإجازة المرضية** | 15 | 5 | **-** | 10 |
| **الإجازة العرضية** | 7 | 2 | **-** | 5 |

### **خصائص العمود الجديد:**
- ✅ **اللون**: 🟡 أصفر (bg-warning)
- ✅ **العرض**: للإجازة السنوية فقط
- ✅ **القيمة**: عدد أيام المغادرات الخاصة
- ✅ **للإجازات الأخرى**: يظهر "-" بدلاً من الرقم

### **حساب الرصيد المتبقي:**
```
الرصيد المتبقي = الرصيد الأولي - الإجازات المستخدمة - المغادرات الخاصة
```

## 🎨 التصميم البصري

### **ألوان الأعمدة:**
| العمود | اللون | الكلاس |
|--------|--------|--------|
| **الرصيد الأولي** | 🟢 أخضر | `bg-success` |
| **الإجازات المستخدمة** | 🔴 أحمر | `bg-danger` |
| **المغادرات الخاصة** | 🟡 أصفر | `bg-warning` |
| **الرصيد المتبقي** | 🔵 أزرق | `bg-info` |

### **عرض المغادرات:**
- **للإجازة السنوية**: يظهر عدد الأيام (مثل: 3.5)
- **للإجازات الأخرى**: يظهر "-" للدلالة على عدم الانطباق

## 🔍 الفرق بين قبل وبعد التحديث

### **قبل التحديث:**
- ❌ **3 أعمدة فقط**: الرصيد، المستخدم، المتبقي
- ❌ **لا يظهر المغادرات**: المغادرات مخفية
- ❌ **حساب غير دقيق**: الرصيد المتبقي لا يشمل المغادرات

### **بعد التحديث:**
- ✅ **4 أعمدة**: الرصيد، المستخدم، المغادرات الخاصة، المتبقي
- ✅ **المغادرات ظاهرة**: للإجازة السنوية فقط
- ✅ **حساب دقيق**: الرصيد المتبقي يشمل المغادرات الخاصة

## 📋 أمثلة عملية

### **مثال موظف لديه:**
- **رصيد إجازة سنوية**: 30 يوم
- **إجازات مستخدمة**: 12 يوم
- **مغادرات خاصة**: 4.5 يوم
- **مغادرات رسمية**: 6 أيام (لا تُحسب)

#### **النتيجة في الجدول:**
| نوع الإجازة | الرصيد الأولي | الإجازات المستخدمة | المغادرات الخاصة | الرصيد المتبقي |
|-------------|---------------|-------------------|------------------|----------------|
| **الإجازة السنوية** | 30 | 12 | **4.5** | **13.5** |

#### **الحساب:**
```
الرصيد المتبقي = 30 - 12 - 4.5 = 13.5 يوم
```

## 🧪 للاختبار

### **خطوات التحقق:**

#### **1. افتح صفحة الاستعلام:**
```
http://localhost:8000/leave-balance-inquiry/
```

#### **2. ابحث عن موظف:**
- أدخل الرقم الوزاري والرقم الوطني
- اضغط "استعلام"

#### **3. تحقق من الجدول:**
- ✅ يجب أن يظهر 4 أعمدة
- ✅ عمود "المغادرات الخاصة" بلون أصفر
- ✅ للإجازة السنوية: رقم المغادرات
- ✅ للإجازات الأخرى: "-"

#### **4. تحقق من الحساب:**
- ✅ الرصيد المتبقي = الأولي - المستخدم - المغادرات الخاصة

### **النتائج المتوقعة:**
```
✅ عمود "المغادرات الخاصة" يظهر بلون أصفر
✅ المغادرات تظهر للإجازة السنوية فقط
✅ الرصيد المتبقي محسوب بشكل صحيح
✅ المغادرات الرسمية لا تُحسب
✅ التصميم متطابق مع صفحة التقارير
```

## 🔧 الفوائد من التحديث

### **1. الشفافية:**
- الموظف يرى تفاصيل كاملة عن استخدام إجازته
- وضوح تأثير المغادرات على الرصيد

### **2. الدقة:**
- حساب دقيق للرصيد المتبقي
- تطابق مع صفحة التقارير الرسمية

### **3. التوافق:**
- نفس منطق الحساب في جميع الصفحات
- تصميم موحد عبر النظام

## 📋 ملاحظات مهمة

### **1. المغادرات المحسوبة:**
- **المغادرات الخاصة**: تُحسب وتُطرح
- **المغادرات الرسمية**: لا تُحسب

### **2. أنواع الإجازات:**
- **الإجازة السنوية**: تتأثر بالمغادرات الخاصة
- **الإجازات الأخرى**: لا تتأثر بالمغادرات

### **3. التوافق:**
- نفس منطق صفحة التقارير
- نفس طريقة الحساب

## الملفات المحدثة

1. **`home/views.py`**:
   - تحديث دالة `get_employee_leave_balance`
   - إضافة حساب المغادرات الخاصة
   - تحديث البيانات المرسلة للـ frontend

2. **`templates/home/<USER>
   - إضافة عمود "المغادرات الخاصة" في رأس الجدول
   - تحديث JavaScript لعرض البيانات الجديدة
   - تحديث التصميم ليطابق صفحة التقارير

## الخلاصة

✅ **تم إضافة عمود "المغادرات الخاصة" بنجاح**
✅ **الحساب يطابق صفحة التقارير تماماً**
✅ **المغادرات الخاصة فقط تُحسب وتُطرح**
✅ **التصميم موحد ومتطابق**
✅ **الشفافية والدقة في عرض البيانات**

**صفحة استعلام رصيد الإجازات الآن تعرض المغادرات الخاصة بنفس طريقة صفحة التقارير! 🎉**
