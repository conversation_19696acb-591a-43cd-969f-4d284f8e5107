#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريپت تبديل إعدادات قاعدة البيانات إلى MySQL
Switch Database Settings to MySQL Script
"""

import os
import sys
from pathlib import Path

def switch_to_mysql():
    """تبديل إعدادات Django لاستخدام MySQL"""
    print("تبديل إعدادات قاعدة البيانات إلى MySQL...")
    print("=" * 50)
    
    try:
        settings_file = Path("hr_system/settings.py")
        
        if not settings_file.exists():
            print("✗ لم يتم العثور على ملف settings.py")
            return False
        
        # قراءة الملف الحالي
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # النص الحالي (SQLite)
        sqlite_config = """# إعدادات قاعدة بيانات - مؤقتاً SQLite حتى إعداد MySQL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# إعدادات MySQL (سيتم تفعيلها بعد إعداد MySQL)
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'hr_system_db',
#         'USER': 'hr_user',
#         'PASSWORD': 'hr_password_2024',
#         'HOST': 'localhost',
#         'PORT': '3306',
#         'OPTIONS': {
#             'charset': 'utf8mb4',
#             'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
#             'autocommit': True,
#         },
#     }
# }"""
        
        # النص الجديد (MySQL)
        mysql_config = """# إعدادات قاعدة بيانات MySQL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'hr_system_db',
        'USER': 'hr_user',
        'PASSWORD': 'hr_password_2024',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'autocommit': True,
        },
    }
}

# إعدادات SQLite الاحتياطية (للرجوع إليها عند الحاجة)
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR / 'db.sqlite3',
#     }
# }"""
        
        # استبدال الإعدادات
        if sqlite_config in content:
            new_content = content.replace(sqlite_config, mysql_config)
            
            # كتابة الملف المحدث
            with open(settings_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✓ تم تحديث إعدادات قاعدة البيانات إلى MySQL")
            return True
        else:
            print("! لم يتم العثور على إعدادات SQLite المتوقعة")
            print("قد تكون الإعدادات محدثة بالفعل أو مختلفة")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في تحديث الإعدادات: {str(e)}")
        return False

def test_mysql_import():
    """اختبار استيراد مكتبة MySQL"""
    try:
        import MySQLdb
        print("✓ مكتبة MySQLdb متوفرة")
        return True
    except ImportError:
        try:
            import mysql.connector
            print("✓ مكتبة mysql.connector متوفرة")
            return True
        except ImportError:
            print("✗ لم يتم العثور على مكتبات MySQL")
            print("قم بتثبيت mysqlclient:")
            print("pip install mysqlclient")
            return False

def main():
    """الدالة الرئيسية"""
    print("سكريپت تبديل قاعدة البيانات إلى MySQL")
    print("=" * 40)
    
    # اختبار مكتبات MySQL
    print("1. فحص مكتبات MySQL...")
    if not test_mysql_import():
        print("\nيرجى تثبيت مكتبة mysqlclient أولاً:")
        print("venv\\Scripts\\python.exe -m pip install mysqlclient")
        return False
    
    # تبديل الإعدادات
    print("\n2. تحديث إعدادات Django...")
    if not switch_to_mysql():
        return False
    
    print("\n" + "=" * 40)
    print("✓ تم تبديل الإعدادات إلى MySQL بنجاح!")
    print("=" * 40)
    
    print("\nالخطوات التالية:")
    print("1. تأكد من تشغيل خدمة MySQL")
    print("2. تأكد من إنشاء قاعدة البيانات والمستخدم")
    print("3. اختبر الاتصال: python test_mysql_connection.py")
    print("4. طبق المخططات: python manage.py migrate")
    print("5. استورد البيانات: python manage.py loaddata backup_data.json")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n✗ فشل في تبديل الإعدادات!")
        sys.exit(1)
    else:
        print("\n✓ تم تبديل الإعدادات بنجاح!")
