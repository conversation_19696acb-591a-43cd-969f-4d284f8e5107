{% extends 'base.html' %}
{% load static %}

{% block title %}الدرجات الوظيفية{% endblock %}

{% block extra_css %}
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .dashboard-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        background: white;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        background: linear-gradient(45deg, #667eea, #764ba2);
    }

    .table-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-top: 2rem;
    }

    .btn-action {
        border-radius: 10px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }

    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .search-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1><i class="fas fa-cogs"></i> إدارة الدرجات الوظيفية</h1>
        <p class="lead">إدارة وتنظيم الدرجات الوظيفية في النظام</p>

        <!-- Action Buttons -->
        <div class="mt-4">
            <a href="{% url 'employees:job_grade_create' %}" class="btn btn-success btn-lg me-3">
                <i class="fas fa-plus"></i> إضافة درجة جديدة
            </a>
            <a href="{% url 'employees:employee_grades_list' %}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-arrow-left"></i> العودة للدرجات الوظيفية
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-6 col-md-6 mb-4">
            <div class="dashboard-card card-primary fade-in" style="animation-delay: 0.1s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-xs text-uppercase">إجمالي الدرجات</span>
                            <div class="h3 text-dark">{{ total_count|default:0 }}</div>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-cogs"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-md-6 mb-4">
            <div class="dashboard-card card-success fade-in" style="animation-delay: 0.2s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-xs text-uppercase">الدرجات المعروضة</span>
                            <div class="h3 text-dark">{{ page_obj.object_list|length|default:0 }}</div>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-list"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="search-container fade-in" style="animation-delay: 0.3s;">
        <form method="get" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" 
                           class="form-control" 
                           name="search" 
                           value="{{ search_query }}" 
                           placeholder="البحث في الدرجات الوظيفية...">
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary flex-fill">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="{% url 'employees:job_grades_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> مسح
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Job Grades Table -->
    <div class="table-container fade-in" style="animation-delay: 0.4s;">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
                <i class="fas fa-list"></i>
                قائمة الدرجات الوظيفية
                {% if search_query %}
                <small class="text-muted">(نتائج البحث عن: "{{ search_query }}")</small>
                {% endif %}
            </h5>
            <div class="text-muted small">
                إجمالي النتائج: {{ total_count }}
            </div>
        </div>

        {% if page_obj.object_list %}
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="jobGradesTable">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="30%">اسم الدرجة</th>
                        <th width="45%">الوصف</th>
                        <th width="15%">تاريخ الإنشاء</th>
                        <th width="5%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for job_grade in page_obj.object_list %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>
                            <strong class="text-primary">{{ job_grade.name }}</strong>
                        </td>
                        <td>
                            {% if job_grade.description %}
                                {{ job_grade.description|truncatewords:10 }}
                            {% else %}
                                <span class="text-muted">لا يوجد وصف</span>
                            {% endif %}
                        </td>
                        <td>
                            <i class="fas fa-calendar-alt text-muted me-1"></i>
                            {{ job_grade.created_at|date:"Y/m/d" }}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'employees:job_grade_update' job_grade.pk %}"
                                   class="btn btn-outline-primary btn-sm"
                                   title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'employees:job_grade_delete' job_grade.pk %}"
                                   class="btn btn-outline-danger btn-sm"
                                   title="حذف"
                                   onclick="return confirm('هل أنت متأكد من حذف هذه الدرجة؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد درجات وظيفية</h5>
            <p class="text-muted">
                {% if search_query %}
                    لم يتم العثور على نتائج للبحث "{{ search_query }}"
                {% else %}
                    لم يتم إضافة أي درجات وظيفية بعد
                {% endif %}
            </p>
            <a href="{% url 'employees:job_grade_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة درجة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable if there are records
    {% if page_obj.object_list %}
    $('#jobGradesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
        },
        "pageLength": 10,
        "ordering": true,
        "searching": false,
        "paging": false,
        "info": false
    });
    {% endif %}

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
