{% extends 'base.html' %}
{% load static %}

{% block title %}بيانات الموظفين - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<style>
    .form-group {
        margin-bottom: 1rem;
    }
    .form-group label {
        font-weight: bold;
    }
    .delete-confirmation {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }
    .filters-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin-bottom: 0;
    }
    .filter-control {
        font-size: 0.95rem;
        padding: 8px 12px;
        height: auto;
        border: 1px solid #ced4da;
    }

    .filter-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* تحسين مظهر الأزرار */
    .btn-filter {
        padding: 8px 12px;
        height: 100%;
    }

    /* Filter section styling */
    .filter-select {
        font-size: 13px;
    }

    .select2-container--default .select2-selection--single {
        height: 31px;
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 29px;
        font-size: 13px;
        color: #5a5c69;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 29px;
    }

    .select2-dropdown {
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
    }

    .form-label.small {
        font-weight: 600;
        margin-bottom: 5px;
    }

    #filterInfo {
        background-color: #f8f9fc;
        padding: 8px 12px;
        border-radius: 0.35rem;
        border-left: 4px solid #4e73df;
    }

    /* Active filter styling */
    .select2-container.filter-active .select2-selection--single {
        background-color: #e3f2fd !important;
        border-color: #2196f3 !important;
        box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25) !important;
    }

    .select2-container.filter-active .select2-selection__rendered {
        color: #1976d2 !important;
        font-weight: 600 !important;
    }

    /* Filter section animations */
    .filter-select {
        transition: all 0.3s ease;
    }

    .select2-container {
        transition: all 0.3s ease;
    }
    
    /* Enhanced Select2 styling for filters */
    .select2-container--bootstrap-5 .select2-selection--single {
        height: 31px !important;
        border: 1px solid #d1d3e2 !important;
        border-radius: 0.35rem !important;
        font-size: 13px !important;
    }
    
    .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
        line-height: 29px !important;
        padding-left: 8px !important;
        padding-right: 20px !important;
        color: #5a5c69 !important;
    }
    
    .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
        height: 29px !important;
        right: 3px !important;
    }
    
    .select2-dropdown {
        border: 1px solid #d1d3e2 !important;
        border-radius: 0.35rem !important;
        font-size: 13px !important;
    }
    
    .select2-search--dropdown .select2-search__field {
        border: 1px solid #d1d3e2 !important;
        border-radius: 0.25rem !important;
        padding: 4px 8px !important;
        font-size: 13px !important;
    }
    
    .select2-results__option {
        padding: 6px 12px !important;
        font-size: 13px !important;
    }
    
    .select2-results__option--highlighted {
        background-color: #4e73df !important;
        color: white !important;
    }

    /* Multi-line table headers */
    #employeesTable thead th {
        text-align: center;
        vertical-align: middle;
        padding: 12px 8px;
        line-height: 1.2;
        font-size: 13px;
        font-weight: 600;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
    }

    #employeesTable thead th i {
        display: block;
        margin-bottom: 4px;
        font-size: 16px;
    }

    #employeesTable tbody td {
        vertical-align: middle;
        padding: 10px 8px;
        font-size: 13px;
        border: 1px solid #dee2e6;
    }

    /* Responsive table adjustments */
    @media (max-width: 768px) {
        #employeesTable thead th {
            font-size: 11px;
            padding: 8px 4px;
        }
        
        #employeesTable thead th i {
            font-size: 14px;
        }
        
        #employeesTable tbody td {
            font-size: 11px;
            padding: 6px 4px;
        }
    }

    /* تحسين مظهر فلتر عرض الأرقام */
    #perPageSelect {
        background-color: #fff;
        border: 2px solid #007bff;
        border-radius: 6px;
        font-weight: 600;
        color: #007bff;
        padding: 8px 12px;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.15);
        transition: all 0.3s ease;
    }

    #perPageSelect:focus {
        border-color: #0056b3;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        outline: none;
    }

    #perPageSelect:hover {
        background-color: #f8f9fa;
        border-color: #0056b3;
    }

    /* تحسين مظهر label عرض */
    .card-header label[for="perPageSelect"] {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white !important;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
        border: none;
        margin-bottom: 0;
    }

    /* تحسين عرض البيانات على أسطر متعددة */
    .table td {
        vertical-align: top;
        line-height: 1.5;
        padding: 12px 8px;
        white-space: normal;
        word-wrap: break-word;
        word-break: break-word;
    }
    
    /* تحسين مظهر البحث */
    #searchInputMain {
        border-left: none;
        border-right: none;
        transition: all 0.3s ease;
    }
    
    #searchInputMain:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        z-index: 3;
        position: relative;
    }
    
    #searchButton {
        border-left: none;
        transition: all 0.3s ease;
        z-index: 2;
    }
    
    #searchButton:hover {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }
    
    #searchButton:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
    
    .input-group-text {
        background-color: #f8f9fa;
        border-color: #ced4da;
        border-right: none;
    }
    
    /* تحسين مظهر زر مسح البحث */
    .input-group .btn-outline-secondary {
        border-left: none;
        color: #6c757d;
    }
    
    .input-group .btn-outline-secondary:hover {
        background-color: #6c757d;
        color: white;
        border-color: #6c757d;
    }
    
    /* تأثير البحث النشط */
    .search-active #searchInputMain {
        background-color: #fff3cd;
        border-color: #ffc107;
    }
    
    .search-active .input-group-text {
        background-color: #ffc107;
        color: #212529;
        border-color: #ffc107;
    }

    /* تحسين مظهر أزرار الفلاتر */
    .btn-filter-action {
        min-width: 80px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-filter-action:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>بيانات الموظفين</h2>
    <div>
        <a href="{% url 'employees:employee_create' %}" class="btn btn-primary">
            <i class="fas fa-user-plus"></i> إضافة موظف جديد
        </a>
        <a href="{% url 'employees:add_qualification' %}" class="btn btn-success">
            <i class="fas fa-graduation-cap"></i> إضافة مؤهل علمي جديد
        </a>
        <a href="{% url 'employees:employee_import_export' %}" class="btn btn-info">
            <i class="fas fa-file-import"></i> استيراد / تصدير
        </a>

        <a href="{% url 'employees:retired_employees_list' %}" class="btn btn-info">
            <i class="fas fa-user-clock"></i> المتقاعدين
        </a>
        <a href="{% url 'employees:external_transfers_list' %}" class="btn btn-warning">
            <i class="fas fa-exchange-alt"></i> المنقولين خارجي
        </a>
        <a href="{% url 'employees:internal_transfers_list' %}" class="btn btn-primary">
            <i class="fas fa-arrows-alt"></i> تفاصيل حركات النقل
        </a>
        <a href="{% url 'employment:service_purchase_list' %}" class="btn btn-success">
            <i class="fas fa-shopping-cart"></i> شراء الخدمات
        </a>
        <a href="{% url 'employees:employee_grades_list' %}" class="btn btn-warning">
            <i class="fas fa-medal"></i> الدرجات الوظيفية
        </a>
        <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary">
            <i class="fas fa-sync"></i> تحديث
        </a>
    </div>
</div>

{% if is_delete %}
<div class="row mb-4">
    <div class="col-md-6 mx-auto">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="m-0"><i class="fas fa-exclamation-triangle me-2"></i> تأكيد الحذف</h5>
            </div>
            <div class="card-body">
                <h5 class="card-title">هل أنت متأكد من حذف هذا الموظف؟</h5>
                <div class="alert alert-light">
                    <p>
                        <strong>الرقم الوزاري:</strong> {{ employee_to_delete.ministry_number }}<br>
                        <strong>الاسم الرباعي:</strong> {{ employee_to_delete.full_name }}<br>
                        <strong>القسم:</strong> {{ employee_to_delete.school }}
                    </p>
                    <p class="mb-0 text-danger"><i class="fas fa-exclamation-circle"></i> هذا الإجراء لا يمكن التراجع عنه.</p>
                </div>

                <form method="post" action="{% url 'employees:employee_delete' employee_to_delete.pk %}" class="mt-3">
                    {% csrf_token %}
                    <div class="d-grid gap-2 d-md-flex justify-content-center">
                        <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> تأكيد الحذف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Filters Section -->
<div class="card shadow mb-3">
    <div class="card-header py-2">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-filter"></i> فلاتر البحث
        </h6>
    </div>
    <div class="card-body py-3">
        <form method="GET" id="employeeFilterForm">
            <!-- Hidden search input to sync with main search -->
            <input type="hidden" name="search" value="{{ search_query|default:'' }}">

            <div class="row g-3">
                <!-- Specialization Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-book text-info"></i> التخصص
                    </label>
                    <select class="form-select form-select-sm select2-filter" name="specialization" data-placeholder="جميع التخصصات">
                        <option value="">جميع التخصصات</option>
                        {% for spec in specializations %}
                        <option value="{{ spec }}" {% if specialization_filter == spec %}selected{% endif %}>{{ spec }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Appointment Type Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-file-signature text-warning"></i> صفة التعيين
                    </label>
                    <select class="form-select form-select-sm select2-filter" name="appointment_type" data-placeholder="جميع الصفات">
                        <option value="">جميع الصفات</option>
                        {% for apt in appointment_types %}
                        <option value="{{ apt.id }}" {% if appointment_type_filter == apt.id|stringformat:"s" %}selected{% endif %}>{{ apt.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Department Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-building text-success"></i> القسم
                    </label>
                    <select class="form-select form-select-sm select2-filter" name="department" data-placeholder="جميع الأقسام">
                        <option value="">جميع الأقسام</option>
                        {% for dept in unique_departments %}
                        <option value="{{ dept }}" {% if department_filter == dept %}selected{% endif %}>{{ dept }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Position Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-briefcase text-secondary"></i> المسمى الوظيفي
                    </label>
                    <select class="form-select form-select-sm select2-filter" name="position" data-placeholder="جميع المسميات">
                        <option value="">جميع المسميات</option>
                        {% for pos in positions %}
                        <option value="{{ pos.id }}" {% if position_filter == pos.id|stringformat:"s" %}selected{% endif %}>{{ pos.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Gender Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-venus-mars text-danger"></i> الجنس
                    </label>
                    <select class="form-select form-select-sm" name="gender">
                        <option value="">الكل</option>
                        <option value="male" {% if gender_filter == "male" %}selected{% endif %}>ذكر</option>
                        <option value="female" {% if gender_filter == "female" %}selected{% endif %}>أنثى</option>
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="col-md-2">
                    <label class="form-label small d-block">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-sm btn-filter-action">
                            <i class="fas fa-filter"></i> تطبيق
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm btn-filter-action" id="resetEmployeeFilters">
                            <i class="fas fa-undo"></i> إعادة ضبط
                        </button>
                    </div>
                </div>
            </div>
        </form>

        <!-- Filter Results Info -->
        <div class="row mt-2">
            <div class="col-12">
                <small class="text-muted" id="filterInfo">
                    <i class="fas fa-info-circle"></i>
                    عرض جميع الموظفين
                </small>
            </div>
        </div>
    </div>
</div>

{% if is_update or is_import_export %}
<div class="row">
    <!-- نموذج إضافة/تعديل الموظف -->
    <div class="col-md-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    {% if is_update %}تعديل بيانات {{ employee.full_name }}
                    {% elif is_import_export %}استيراد / تصدير بيانات الموظفين
                    {% endif %}
                </h6>
            </div>
            <div class="card-body">
{% endif %}

                {% if is_import_export %}
                <div class="import-export-section">
                    <h5>استيراد بيانات الموظفين</h5>
                    <div class="alert alert-info mb-3">
                        <p>يمكنك استيراد بيانات الموظفين من ملف Excel. يجب أن يحتوي الملف على الأعمدة المطلوبة.</p>
                    </div>

                    <form method="post" enctype="multipart/form-data" action="{% url 'employees:employee_import_export' %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="{{ import_form.excel_file.id_for_label }}" class="form-label">ملف Excel</label>
                            {{ import_form.excel_file }}
                            {% if import_form.excel_file.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in import_form.excel_file.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 mb-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-import"></i> استيراد
                            </button>
                        </div>
                    </form>

                    <h5>تصدير بيانات الموظفين</h5>
                    <div class="alert alert-info mb-3">
                        <p>يمكنك تصدير بيانات الموظفين إلى ملف Excel أو PDF.</p>
                    </div>

                    <div class="d-grid gap-2 d-md-flex">
                        <a href="{% url 'employees:employee_import_export' %}?export=excel" class="btn btn-success me-md-2">
                            <i class="fas fa-file-excel"></i> تصدير إلى Excel
                        </a>
                        <a href="{% url 'employees:employee_import_export' %}?export=pdf" class="btn btn-danger">
                            <i class="fas fa-file-pdf"></i> تصدير إلى PDF
                        </a>
                    </div>
                </div>
                {% else %}





                </form>
                {% endif %}


    <!-- قائمة الموظفين -->
    <div class="{% if is_update or is_import_export %}col-md-7{% elif is_delete %}col-md-12{% else %}col-md-12{% endif %}">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">جميع الموظفين</h6>
                <div class="d-flex align-items-center gap-2">
                    <!-- Per page selector -->
                    <div class="d-flex align-items-center">
                        <label for="perPageSelect" class="form-label mb-0 me-2 text-nowrap text-white fw-bold">عرض:</label>
                        <select id="perPageSelect" class="form-select" style="width: auto; min-width: 80px; font-size: 14px; font-weight: 600;">
                            <option value="10" {% if per_page == '10' %}selected{% endif %}>10</option>
                            <option value="25" {% if per_page == '25' %}selected{% endif %}>25</option>
                            <option value="50" {% if per_page == '50' %}selected{% endif %}>50</option>
                            <option value="100" {% if per_page == '100' %}selected{% endif %}>100</option>
                            <option value="all" {% if per_page == 'all' or not per_page %}selected{% endif %}>الكل</option>
                        </select>
                    </div>

                    <!-- Search input -->
                    <form method="GET" class="d-inline-block">
                        <div class="input-group" style="width: 320px;">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" id="searchInputMain" name="search" class="form-control form-control-sm"
                                   placeholder="ابحث بالاسم، الرقم الوزاري، التخصص..." value="{{ search_query|default:'' }}" autocomplete="off">
                            <button type="submit" class="btn btn-outline-primary btn-sm" id="searchButton">
                                <i class="fas fa-search"></i>
                            </button>
                            {% if search_query %}
                            <a href="{% url 'employees:employee_list' %}" class="btn btn-outline-secondary btn-sm" title="مسح البحث">
                                <i class="fas fa-times"></i>
                            </a>
                            {% endif %}
                        </div>
                        <!-- Hidden inputs to preserve other filters -->
                        {% if specialization_filter %}
                            <input type="hidden" name="specialization" value="{{ specialization_filter }}">
                        {% endif %}
                        {% if appointment_type_filter %}
                            <input type="hidden" name="appointment_type" value="{{ appointment_type_filter }}">
                        {% endif %}
                        {% if department_filter %}
                            <input type="hidden" name="department" value="{{ department_filter }}">
                        {% endif %}
                        {% if position_filter %}
                            <input type="hidden" name="position" value="{{ position_filter }}">
                        {% endif %}
                        {% if gender_filter %}
                            <input type="hidden" name="gender" value="{{ gender_filter }}">
                        {% endif %}
                        {% if per_page != '10' %}
                            <input type="hidden" name="per_page" value="{{ per_page }}">
                        {% endif %}
                    </form>

                    <!-- Employee counter -->
                    <span class="badge bg-info" id="employeeCounter">
                        {% if total_employees %}
                            {{ total_employees }}
                        {% elif page_obj %}
                            {{ page_obj.paginator.count }}
                        {% else %}
                            {{ employees.count }}
                        {% endif %}
                        موظف
                    </span>
                </div>
            </div>
            <div class="card-body">
                <!-- Loading indicator -->
                <div id="loading-indicator" class="d-none text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2 text-muted">جاري تحديث البيانات...</p>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="employeesTable">
                        <thead class="table-light">
                            <tr>
                                <th width="3%" style="vertical-align: middle;"><i class="fas fa-hashtag text-primary me-1"></i><br>الرقم</th>
                                <th style="vertical-align: middle;"><i class="fas fa-id-card me-1"></i><i class="fas fa-sort-numeric-up text-success ms-1" title="مرتب تصاعدياً"></i><br>الرقم الوزاري</th>
                                <th style="vertical-align: middle;"><i class="fas fa-id-badge me-1"></i><br>الرقم الوطني</th>
                                <th style="vertical-align: middle;"><i class="fas fa-user me-1"></i><br>الاسم الرباعي</th>
                                <th style="vertical-align: middle;"><i class="fas fa-venus-mars me-1"></i><br>الجنس</th>
                                <th style="vertical-align: middle;"><i class="fas fa-birthday-cake me-1"></i><br>تاريخ الميلاد</th>
                                <th style="vertical-align: middle;"><i class="fas fa-graduation-cap me-1"></i><br>المؤهل العلمي<br>(بكالوريس / دبلوم)</th>
                                <th style="vertical-align: middle;"><i class="fas fa-certificate me-1"></i><br>المؤهل العلمي<br>(دبلوم بعد البكالوريس)</th>
                                <th style="vertical-align: middle;"><i class="fas fa-user-graduate me-1"></i><br>المؤهل العلمي<br>(ماجستير)</th>
                                <th style="vertical-align: middle;"><i class="fas fa-award me-1"></i><br>المؤهل العلمي<br>(دكتوراه)</th>
                                <th style="vertical-align: middle;"><i class="fas fa-book me-1"></i><br>التخصص</th>
                                <th style="vertical-align: middle;"><i class="fas fa-calendar-check me-1"></i><br>تاريخ التعيين</th>
                                <th style="vertical-align: middle;"><i class="fas fa-file-signature me-1"></i><br>صفة التعيين</th>
                                <th style="vertical-align: middle;"><i class="fas fa-building me-1"></i><br>القسم</th>
                                <th style="vertical-align: middle;"><i class="fas fa-briefcase me-1"></i><br>المسمى الوظيفي</th>
                                <th style="vertical-align: middle;"><i class="fas fa-phone me-1"></i><br>رقم الهاتف</th>
                                <th style="vertical-align: middle;"><i class="fas fa-map-marker-alt me-1"></i><br>العنوان</th>
                                <th style="vertical-align: middle;"><i class="fas fa-cogs me-1"></i><br>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% include 'employees/employee_table_rows.html' %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <div class="card-footer">
                <nav aria-label="Employee pagination">
                    <ul class="pagination justify-content-center mb-0">
                        <!-- Pagination will be updated via AJAX -->
                    </ul>
                </nav>

                <div class="text-center mt-2">
                    <small class="text-muted">
                        <span id="pagination-info">
                            {% if page_obj %}
                                عرض {{ page_obj.start_index }} - {{ page_obj.end_index }} من {{ page_obj.paginator.count }} موظف
                                {% if page_obj.has_other_pages %}
                                | صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                {% endif %}
                            {% else %}
                                عرض جميع الموظفين ({{ total_employees }} موظف)
                            {% endif %}
                        </span>
                        <br>
                        <i class="fas fa-sort-numeric-up text-success me-1"></i>
                        البيانات مرتبة حسب الرقم الوزاري من الأصغر للأكبر
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
console.log('Script loaded');

// Global functions for AJAX operations
function loadEmployeesAjax(page = 1) {
    // Use the filter AJAX function with page parameter
    performFilterAjaxWithPage(page);
}

function performFilterAjaxWithPage(page = 1) {
    console.log('performFilterAjaxWithPage called, page:', page);

    // Show loading
    $('#loading-indicator').removeClass('d-none');

    // Get all filter data
    const formData = $('#employeeFilterForm').serialize();
    console.log('Filter data:', formData);
    console.log('Form exists:', $('#employeeFilterForm').length);
    console.log('Search input value:', $('#employeeFilterForm input[name="search"]').val());

    // Make AJAX request
    $.ajax({
        url: '/employees/ajax/',
        type: 'GET',
        data: formData + '&page=' + page + '&per_page=' + ($('#perPageSelect').val() || '50'),
        success: function(response) {
            console.log('Filter AJAX Success:', response);

            // Update table
            $('#employeesTable tbody').html(response.html);

            // Update counter
            $('#employeeCounter').text(response.total_count + ' موظف');

            // Hide loading
            $('#loading-indicator').addClass('d-none');

            // Update pagination if needed
            if (response.num_pages > 1) {
                $('.pagination').show();
                updatePagination(response);
            } else {
                $('.pagination').hide();
            }
        },
        error: function(xhr, status, error) {
            console.log('Filter AJAX Error:', error);
            console.log('Response:', xhr.responseText);

            // Hide loading
            $('#loading-indicator').addClass('d-none');

            // Show error
            $('#employeesTable tbody').html('<tr><td colspan="18" class="text-center text-danger">حدث خطأ في تحميل البيانات</td></tr>');
        }
    });
}

// Update filter info for AJAX
function updateFilterInfoAjax() {
    var activeFilters = [];

    // Check search input
    var searchValue = $('input[name="search"]').val();
    if (searchValue) {
        activeFilters.push('البحث: "' + searchValue + '"');
    }

    // Check Select2 filters
    $('.select2-filter').each(function() {
        var $this = $(this);
        var value = $this.val();
        var label = $this.prev('label').text().replace(/[^\u0600-\u06FF\s]/g, '').trim();

        if (value) {
            var selectedText = $this.find('option:selected').text();
            if (selectedText && selectedText !== 'جميع التخصصات' && selectedText !== 'جميع الصفات' &&
                selectedText !== 'جميع الأقسام' && selectedText !== 'جميع المسميات' && selectedText !== 'الكل') {
                activeFilters.push(label + ': ' + selectedText);
            }
        }
    });

    var infoText = '';
    if (activeFilters.length > 0) {
        infoText = '<i class="fas fa-filter text-primary"></i> مُطبق: ' + activeFilters.join(' | ');
    } else {
        infoText = '<i class="fas fa-info-circle"></i> عرض جميع الموظفين';
    }

    $('#filterInfo').html(infoText);
}

// Update employee counter
function updateEmployeeCounter(response) {
    let counterText = '';
    if (response.num_pages > 1) {
        counterText = `عرض ${response.start_index} - ${response.end_index} من ${response.total_count} موظف`;
    } else {
        counterText = `${response.total_count} موظف`;
    }
    $('#employeeCounter').text(counterText);
}

// Update pagination info
function updatePaginationInfo(response) {
    let infoText = '';
    if (response.num_pages > 1) {
        infoText = `عرض ${response.start_index} - ${response.end_index} من ${response.total_count} موظف | صفحة ${response.number} من ${response.num_pages}`;
    } else {
        infoText = `عرض جميع الموظفين (${response.total_count} موظف)`;
    }
    $('#pagination-info').text(infoText);
}

// Update pagination
function updatePagination(response) {
    const pagination = $('.pagination');

    // Always show pagination container
    pagination.parent().show();

    if (response.num_pages <= 1) {
        pagination.hide();
        return;
    }

    pagination.show();
    let paginationHtml = '';

    // Previous button
    if (response.has_previous) {
        paginationHtml += `<li class="page-item">
            <a class="page-link" href="#" data-page="${response.number - 1}">السابق</a>
        </li>`;
    } else {
        paginationHtml += `<li class="page-item disabled">
            <span class="page-link">السابق</span>
        </li>`;
    }

    // Page numbers - show max 5 pages around current page
    let startPage = Math.max(1, response.number - 2);
    let endPage = Math.min(response.num_pages, response.number + 2);

    // Show first page if not in range
    if (startPage > 1) {
        paginationHtml += `<li class="page-item">
            <a class="page-link" href="#" data-page="1">1</a>
        </li>`;
        if (startPage > 2) {
            paginationHtml += `<li class="page-item disabled">
                <span class="page-link">...</span>
            </li>`;
        }
    }

    // Show page numbers in range
    for (let i = startPage; i <= endPage; i++) {
        if (i === response.number) {
            paginationHtml += `<li class="page-item active">
                <span class="page-link">${i}</span>
            </li>`;
        } else {
            paginationHtml += `<li class="page-item">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>`;
        }
    }

    // Show last page if not in range
    if (endPage < response.num_pages) {
        if (endPage < response.num_pages - 1) {
            paginationHtml += `<li class="page-item disabled">
                <span class="page-link">...</span>
            </li>`;
        }
        paginationHtml += `<li class="page-item">
            <a class="page-link" href="#" data-page="${response.num_pages}">${response.num_pages}</a>
        </li>`;
    }

    // Next button
    if (response.has_next) {
        paginationHtml += `<li class="page-item">
            <a class="page-link" href="#" data-page="${response.number + 1}">التالي</a>
        </li>`;
    } else {
        paginationHtml += `<li class="page-item disabled">
            <span class="page-link">التالي</span>
        </li>`;
    }

    pagination.html(paginationHtml);
}

$(document).ready(function() {
    console.log('Document ready - initializing filters');

    // Initialize Select2 for filters with search capability
    $('.select2-filter').select2({
        theme: 'bootstrap-5',
        placeholder: function() {
            return $(this).data('placeholder') || $(this).find('option:first').text();
        },
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: true,
        language: {
            noResults: function() {
                return "لا توجد نتائج مطابقة";
            },
            searching: function() {
                return "جاري البحث...";
            },
            inputTooShort: function() {
                return "ابدأ بكتابة للبحث...";
            },
            loadingMore: function() {
                return "جاري تحميل المزيد...";
            }
        },
        minimumInputLength: 0,
        escapeMarkup: function(markup) {
            return markup;
        },
        templateResult: function(data) {
            if (data.loading) {
                return data.text;
            }
            return data.text;
        },
        templateSelection: function(data) {
            return data.text;
        },
        // Enable search for all dropdowns
        minimumResultsForSearch: 0
    });

    // Bind filter events after Select2 initialization
    bindFilterEvents();

    // Initialize filter info on page load
    updateFilterInfo();
    
    // Initialize search active state
    if ($('#searchInputMain').val().trim()) {
        $('#searchInputMain').closest('form').addClass('search-active');
    }

    // Search functionality with delay using AJAX
    let searchTimeout;
    $('#searchInputMain').on('keyup input', function(e) {
        console.log('Search input event triggered:', e.type, $(this).val());
        clearTimeout(searchTimeout);
        const searchValue = $(this).val();

        // Update search active state
        const $form = $('#searchInputMain').closest('form');
        if (searchValue.trim()) {
            $form.addClass('search-active');
        } else {
            $form.removeClass('search-active');
        }

        // Update the filter form search input to sync both search inputs
        $('#employeeFilterForm input[name="search"]').val(searchValue);

        // If Enter key is pressed, search immediately
        if (e.key === 'Enter') {
            e.preventDefault();
            clearTimeout(searchTimeout);
            console.log('Enter pressed, loading AJAX immediately');
            performAjaxSearch();
            return;
        }

        // Delay search to avoid too many requests
        console.log('Setting timeout for search');
        searchTimeout = setTimeout(function() {
            console.log('Timeout triggered, loading AJAX');
            performAjaxSearch();
        }, 500); // Wait 500ms after user stops typing
    });

    // Simple AJAX search function
    function performAjaxSearch() {
        console.log('performAjaxSearch called');

        // Get search value and update filter form
        const searchValue = $('#searchInputMain').val();
        $('#employeeFilterForm input[name="search"]').val(searchValue);

        // Call the global AJAX function
        loadEmployeesAjax();
    }
    
    // Search form submit handler - prevent form submission and use AJAX
    $('#searchInputMain').closest('form').on('submit', function(e) {
        e.preventDefault(); // Always prevent form submission
        console.log('Form submitted, using AJAX');

        performAjaxSearch();

        return false;
    });
    
    // Search button click handler - use AJAX instead of form submit
    $('#searchButton').on('click', function(e) {
        e.preventDefault(); // Prevent form submission
        console.log('Search button clicked');

        performAjaxSearch();

        return false;
    });
    


    // Filter functionality for Select2 filters
    $('.select2-filter').on('change', function() {
        // Add visual feedback for active filters
        var $container = $(this).next('.select2-container');
        if ($(this).val()) {
            $container.addClass('filter-active');
        } else {
            $container.removeClass('filter-active');
        }
        
        // Update filter info
        updateFilterInfo();
    });
    
    // Legacy filter functionality for non-Select2 filters
    $('.filter-select').on('change', function() {
        // Add visual feedback for active filters
        var $container = $(this).next('.select2-container');
        if ($(this).val()) {
            $container.addClass('filter-active');
        } else {
            $container.removeClass('filter-active');
        }
        updateFilterInfo();
    });

    // Reset filters
    $('#resetEmployeeFilters').on('click', function() {
        console.log('Reset button clicked');

        // Reset all form fields
        $('#employeeFilterForm')[0].reset();

        // Reset Select2 fields
        $('.select2-filter').val(null).trigger('change');

        // Clear both search inputs
        $('input[name="search"]').val('');
        $('#searchInputMain').val('');

        // Remove search active state
        $('#searchInputMain').closest('form').removeClass('search-active');

        // Remove visual feedback
        $('.select2-container').removeClass('filter-active');

        // Reload data with AJAX
        performFilterAjax();
    });
    
    // Update filter info function
    function updateFilterInfo() {
        var activeFilters = [];
        
        // Check search input
        var searchValue = $('#searchInputMain').val();
        if (searchValue) {
            activeFilters.push('البحث: ' + searchValue);
        }
        
        // Check Select2 filters
        $('.select2-filter').each(function() {
            var $this = $(this);
            var value = $this.val();
            var label = $this.prev('label').text().replace(/[^\u0600-\u06FF\s]/g, '').trim();
            
            if (value) {
                var selectedText = $this.find('option:selected').text();
                if (selectedText && selectedText !== 'جميع التخصصات' && selectedText !== 'جميع الصفات' && 
                    selectedText !== 'جميع الأقسام' && selectedText !== 'جميع المسميات' && selectedText !== 'الكل') {
                    activeFilters.push(label + ': ' + selectedText);
                }
            }
        });
        
        // Update filter info display
        var $filterInfo = $('#filterInfo');
        if (activeFilters.length > 0) {
            $filterInfo.html('<i class="fas fa-filter text-primary"></i> الفلاتر النشطة: ' + activeFilters.join(' | '));
            $filterInfo.removeClass('text-muted').addClass('text-primary');
        } else {
            $filterInfo.html('<i class="fas fa-info-circle"></i> عرض جميع الموظفين');
            $filterInfo.removeClass('text-primary').addClass('text-muted');
        }
    }

    // Apply all filters function
    function applyAllFilters() {
        var searchValue = $('#searchInputMain').val().toLowerCase();
        var specializationFilter = $('#specializationFilter').val();
        var appointmentTypeFilter = $('#appointmentTypeFilter').val();
        var departmentFilter = $('#departmentFilter').val();
        var positionFilter = $('#positionFilter').val();
        var genderFilter = $('#genderFilter').val();

        var visibleCount = 0;
        var totalCount = 0;

        $('#employeesTable tbody tr').each(function() {
            var $row = $(this);
            var rowText = $row.text().toLowerCase();
            var show = true;
            totalCount++;

            // Skip empty row
            if ($row.find('td').length === 1) {
                $row.hide();
                totalCount--;
                return;
            }

            // Search filter
            if (searchValue && rowText.indexOf(searchValue) === -1) {
                show = false;
            }

            // Specialization filter
            if (specializationFilter && $row.find('td:eq(7)').text().trim() !== specializationFilter) {
                show = false;
            }

            // Appointment type filter
            if (appointmentTypeFilter && $row.find('td:eq(9)').text().trim() !== appointmentTypeFilter) {
                show = false;
            }

            // Department filter
            if (departmentFilter && $row.find('td:eq(10)').text().trim() !== departmentFilter) {
                show = false;
            }

            // Position filter - check if position text contains the filter value
            if (positionFilter) {
                var positionText = $row.find('td:eq(11)').text().trim();
                if (positionText === '-' || positionText.indexOf(positionFilter) === -1) {
                    show = false;
                }
            }

            // Gender filter
            if (genderFilter) {
                var genderBadge = $row.find('td:eq(4) .badge').text().trim();
                if (genderBadge !== genderFilter) {
                    show = false;
                }
            }

            if (show) {
                $row.show();
                visibleCount++;
            } else {
                $row.hide();
            }
        });

        // Update counter
        $('#employeeCounter').text(visibleCount + ' من ' + totalCount + ' موظف');

        // Update filter info
        updateFilterInfo(searchValue, specializationFilter, appointmentTypeFilter, departmentFilter, positionFilter, genderFilter, visibleCount);
    }

    // Update filter info function
    function updateFilterInfo(search, specialization, appointmentType, department, position, gender, count) {
        var filters = [];

        if (search) filters.push('البحث: "' + search + '"');
        if (specialization) filters.push('التخصص: ' + specialization);
        if (appointmentType) filters.push('صفة التعيين: ' + appointmentType);
        if (department) filters.push('القسم: ' + department);
        if (position) filters.push('المسمى الوظيفي: ' + position);
        if (gender) filters.push('الجنس: ' + gender);

        var infoText = '';
        if (filters.length > 0) {
            infoText = '<i class="fas fa-filter text-primary"></i> مُطبق: ' + filters.join(' | ') + ' - النتائج: ' + count;
        } else {
            infoText = '<i class="fas fa-info-circle"></i> عرض جميع الموظفين - المجموع: ' + count;
        }

        $('#filterInfo').html(infoText);
    }

    // Initialize filter states on page load
    $('.filter-select').each(function() {
        if ($(this).val()) {
            $(this).next('.select2-container').addClass('filter-active');
        }
    });

    // Initial filter application
    applyAllFilters();

    // Add Bootstrap classes to form fields
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(element) {
        element.classList.add('form-control');
    });

    // Enable edit modal fields
    const modals = document.querySelectorAll('.modal');
    modals.forEach(function(modal) {
        modal.addEventListener('shown.bs.modal', function() {
            const inputs = this.querySelectorAll('input, select, textarea');
            inputs.forEach(function(input) {
                input.removeAttribute('readonly');
                input.removeAttribute('disabled');
            });

            // Focus on the first input
            const firstInput = this.querySelector('input');
            if (firstInput) {
                firstInput.focus();
            }
        });
    });

    // Handle per page selector change with AJAX
    $('#perPageSelect').on('change', function() {
        loadEmployeesAjax(1); // Reset to page 1 when changing per page
    });




    
    // Enable PhD field only when Masters field has value
    function checkPhdAvailability() {
        var mastersValue = $('#id_masters_degree').val();
        var phdField = $('#id_phd_degree');
        
        if (mastersValue && mastersValue.trim() !== '') {
            phdField.prop('disabled', false);
            phdField.removeClass('disabled');
        } else {
            phdField.prop('disabled', true);
            phdField.addClass('disabled');
            phdField.val(''); // Clear PhD field if masters is empty
        }
    }
    
    // Check on page load
    checkPhdAvailability();
    
    // Check when masters field changes
    $('#id_masters_degree').on('input change', function() {
        checkPhdAvailability();
    });
    
    // Form validation for add employee modal
    $('#addEmployeeForm').on('submit', function(e) {
        var mastersValue = $('#id_masters_degree').val();
        var phdValue = $('#id_phd_degree').val();
        
        if (phdValue && phdValue.trim() !== '' && (!mastersValue || mastersValue.trim() === '')) {
            e.preventDefault();
            alert('يجب إدخال المؤهل العلمي (ماجستير) قبل إدخال المؤهل العلمي (دكتوراه)');
            return false;
        }
    });
    

    
    // Function to bind events after Select2 initialization
    function bindFilterEvents() {
        console.log('Binding filter events');

        // Test if elements exist
        console.log('Form exists:', $('#employeeFilterForm').length);
        console.log('Select2 filters exist:', $('.select2-filter').length);
        console.log('Search input exists:', $('#employeeFilterForm input[name="search"]').length);

        // Auto-submit form when filters change using AJAX
        $('#employeeFilterForm select').off('change.filterAjax').on('change.filterAjax', function() {
            console.log('Filter changed:', $(this).attr('name'), $(this).val());
            performFilterAjax();
        });

        // Handle Select2 change events specifically
        $('.select2-filter').off('select2:select.filterAjax select2:unselect.filterAjax select2:clear.filterAjax')
            .on('select2:select.filterAjax select2:unselect.filterAjax select2:clear.filterAjax', function() {
            console.log('Select2 changed:', $(this).attr('name'), $(this).val());
            performFilterAjax();
        });

        // Auto search with debounce
        let searchTimeout;
        $('#employeeFilterForm input[name="search"]').off('input.filterAjax keyup.filterAjax')
            .on('input.filterAjax keyup.filterAjax', function() {
            console.log('Filter search input changed:', $(this).val());

            // Sync with main search input
            $('#searchInputMain').val($(this).val());

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                performFilterAjax();
            }, 500); // Wait 500ms after user stops typing
        });
    }

    // Simple filter AJAX function
    function performFilterAjax() {
        performFilterAjaxWithPage(1);
    }





    // Handle pagination clicks
    $(document).on('click', '.pagination .page-link[data-page]', function(e) {
        e.preventDefault();
        const page = $(this).data('page');
        loadEmployeesAjax(page);
    });



    // Load data on page load if there are URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.toString()) {
        console.log('URL has parameters, loading data via AJAX');
        loadEmployeesAjax();
    }




});
</script>
{% endblock %}

<style>
.ajax-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>


<div class="modal fade" id="addEmployeeModal" tabindex="-1" aria-labelledby="addEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEmployeeModalLabel">إضافة موظف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'employees:employee_list' %}" id="addEmployeeForm" novalidate>
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.ministry_number.id_for_label }}">الرقم الوزاري</label>
                                {{ form.ministry_number }}
                                {% if form.ministry_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.ministry_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.national_id.id_for_label }}">الرقم الوطني</label>
                                {{ form.national_id }}
                                {% if form.national_id.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.national_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="{{ form.full_name.id_for_label }}">الاسم الرباعي</label>
                        {{ form.full_name }}
                        {% if form.full_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.full_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.gender.id_for_label }}">الجنس</label>
                                {{ form.gender }}
                                {% if form.gender.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.gender.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.birth_date.id_for_label }}">تاريخ الميلاد</label>
                                {{ form.birth_date }}
                                {% if form.birth_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.birth_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.qualification.id_for_label }}">المؤهل العلمي (بكالوريس / دبلوم)</label>
                                {{ form.qualification }}
                                {% if form.qualification.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.qualification.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.post_graduate_diploma.id_for_label }}">المؤهل العلمي (دبلوم بعد البكالوريس)</label>
                                {{ form.post_graduate_diploma }}
                                {% if form.post_graduate_diploma.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.post_graduate_diploma.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.masters_degree.id_for_label }}">المؤهل العلمي (ماجستير)</label>
                                {{ form.masters_degree }}
                                {% if form.masters_degree.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.masters_degree.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.phd_degree.id_for_label }}">المؤهل العلمي (دكتوراه)</label>
                                {{ form.phd_degree }}
                                {% if form.phd_degree.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phd_degree.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="{{ form.specialization.id_for_label }}">التخصص</label>
                        {{ form.specialization }}
                        {% if form.specialization.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.specialization.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.hire_date.id_for_label }}">تاريخ التعيين</label>
                                {{ form.hire_date }}
                                {% if form.hire_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.hire_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="id_appointment_type">صفة التعيين</label>
                                <select name="appointment_type" id="id_appointment_type" class="form-control select2">
                                    <option value="">---------</option>
                                    {% for apt in appointment_types %}
                                        <option value="{{ apt.id }}">{{ apt.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.department_choice.id_for_label }}">القسم</label>
                                {{ form.department_choice }}
                                {% if form.department_choice.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.department_choice.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="id_position">المسمى الوظيفي</label>
                                <select name="position" id="id_position" class="form-control select2">
                                    <option value="">---------</option>
                                    {% for pos in positions %}
                                        <option value="{{ pos.id }}">{{ pos.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.phone_number.id_for_label }}">رقم الهاتف</label>
                                {{ form.phone_number }}
                                {% if form.phone_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.address.id_for_label }}">العنوان</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>


                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('addEmployeeForm').submit();">حفظ</button>
            </div>
        </div>
    </div>
</div>
