{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة التحكم - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* Modern Dashboard Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --info-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        --card-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        --card-hover-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    /* Dashboard Header */
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2.5rem 0;
        margin: -1.5rem -1.5rem 3rem -1.5rem;
        border-radius: 0 0 30px 30px;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .dashboard-header .container-fluid {
        position: relative;
        z-index: 1;
    }

    /* Enhanced Dashboard Cards */
    .dashboard-card {
        border: none;
        border-radius: 25px;
        box-shadow: var(--card-shadow);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        height: 100%;
        background: white;
        overflow: hidden;
        position: relative;
    }

    .dashboard-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: var(--primary-gradient);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .dashboard-card:hover {
        transform: translateY(-15px) scale(1.02);
        box-shadow: var(--card-hover-shadow);
    }

    .dashboard-card:hover::before {
        transform: scaleX(1);
    }

    .dashboard-card .card-body {
        padding: 2rem;
        position: relative;
    }

    .dashboard-card .text-xs {
        font-size: 0.75rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 0.75rem;
        display: block;
    }

    .dashboard-card .h3 {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Enhanced Icon Circles */
    .dashboard-card .icon-circle {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .dashboard-card .icon-circle::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: inherit;
        opacity: 0.1;
        border-radius: 50%;
    }

    .dashboard-card .icon-circle i {
        font-size: 2rem;
        color: white;
        position: relative;
        z-index: 1;
    }

    /* Enhanced Card Colors with Gradients */
    .card-primary {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    }

    .card-primary::before {
        background: var(--primary-gradient);
    }

    .card-primary .text-xs {
        color: #667eea;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .card-primary .icon-circle {
        background: var(--primary-gradient);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .card-success {
        background: linear-gradient(135deg, rgba(17, 153, 142, 0.05) 0%, rgba(56, 239, 125, 0.05) 100%);
    }

    .card-success::before {
        background: var(--success-gradient);
    }

    .card-success .text-xs {
        color: #11998e;
        background: linear-gradient(45deg, #11998e, #38ef7d);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .card-success .icon-circle {
        background: var(--success-gradient);
        box-shadow: 0 10px 25px rgba(17, 153, 142, 0.3);
    }

    .card-info {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    }

    .card-info::before {
        background: var(--info-gradient);
    }

    .card-info .text-xs {
        color: #667eea;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .card-info .icon-circle {
        background: var(--info-gradient);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .card-warning {
        background: linear-gradient(135deg, rgba(240, 147, 251, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%);
    }

    .card-warning::before {
        background: var(--warning-gradient);
    }

    .card-warning .text-xs {
        color: #f093fb;
        background: linear-gradient(45deg, #f093fb, #f5576c);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .card-warning .icon-circle {
        background: var(--warning-gradient);
        box-shadow: 0 10px 25px rgba(240, 147, 251, 0.3);
    }

    /* Enhanced Animations and Effects */
    .fade-in {
        animation: fadeInUp 0.8s ease-out forwards;
        opacity: 0;
        transform: translateY(30px);
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Pulse animation for counters */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .dashboard-card:hover .h3 {
        animation: pulse 1s ease-in-out;
    }

    /* Enhanced Tables */
    .table-enhanced {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .table-enhanced thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
    }

    .table-enhanced tbody tr {
        transition: all 0.3s ease;
    }

    .table-enhanced tbody tr:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        transform: scale(1.01);
    }

    /* Enhanced Cards */
    .card-enhanced {
        border: none;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s ease;
        overflow: hidden;
        background: white;
    }

    .card-enhanced:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .card-enhanced .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Enhanced Buttons */
    .btn-enhanced {
        border-radius: 15px;
        padding: 1rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-enhanced:hover::before {
        left: 100%;
    }

    .btn-enhanced:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-header {
            padding: 2rem 0;
            margin: -1rem -1rem 2rem -1rem;
        }

        .dashboard-card .h3 {
            font-size: 2rem;
        }

        .dashboard-card .icon-circle {
            width: 60px;
            height: 60px;
        }

        .dashboard-card .icon-circle i {
            font-size: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .dashboard-header h2 {
            font-size: 1.5rem;
        }

        .dashboard-card .h3 {
            font-size: 1.75rem;
        }

        .dashboard-card .card-body {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Enhanced Dashboard Header -->
<div class="dashboard-header">
    <div class="container-fluid">
        <div class="text-center">
            <h1 class="h2 mb-3 fw-bold">
                <i class="fas fa-tachometer-alt me-3"></i>لوحة التحكم الرئيسية
            </h1>
            <p class="mb-0 opacity-75 lead">نظرة شاملة على إحصائيات النظام والبيانات الحديثة</p>
        </div>
    </div>
</div>

<div class="row">
    <!-- Row 1 - Main Stats -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-primary fade-in" style="animation-delay: 0.1s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الموظفين</span>
                        <div class="h3 text-dark">{{ employee_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-success fade-in" style="animation-delay: 0.2s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الأقسام</span>
                        <div class="h3 text-dark">{{ department_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-info fade-in" style="animation-delay: 0.3s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الإجازات الحالية</span>
                        <div class="h3 text-dark">{{ active_leaves_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-calendar"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-warning fade-in" style="animation-delay: 0.4s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">المستخدمين</span>
                        <div class="h3 text-dark">{{ user_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Row 2 - Additional Stats -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-primary fade-in" style="animation-delay: 0.5s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الرتب</span>
                        <div class="h3 text-dark">{{ rank_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-medal"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-success fade-in" style="animation-delay: 0.6s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الملفات</span>
                        <div class="h3 text-dark">{{ file_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-folder"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-info fade-in" style="animation-delay: 0.7s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الإجازات بدون راتب</span>
                        <div class="h3 text-dark">{{ unpaid_leave_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-warning fade-in" style="animation-delay: 0.8s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">سجل حركات النظام</span>
                        <div class="h3 text-dark">{{ system_log_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-history"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Row 3 - Recent Data Tables -->
<div class="row">
    <div class="col-lg-6">
        <div class="card-enhanced fade-in" style="animation-delay: 0.9s;">
            <div class="card-header py-3">
                <h6 class="m-0 fw-bold">
                    <i class="fas fa-users me-2"></i>آخر الموظفين المضافين
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-enhanced">
                        <thead>
                            <tr>
                                <th>الرقم الوزاري</th>
                                <th>الاسم الكامل</th>
                                <th>القسم</th>
                                <th>تاريخ التعيين</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in recent_employees %}
                            <tr>
                                <td><span class="badge bg-primary">{{ employee.ministry_number }}</span></td>
                                <td>
                                    <a href="{% url 'employees:employee_detail' employee.pk %}" class="text-decoration-none fw-bold">
                                        {{ employee.full_name }}
                                    </a>
                                </td>
                                <td><span class="text-muted">{{ employee.school }}</span></td>
                                <td><span class="badge bg-info">{{ employee.hire_date|date:"Y-m-d" }}</span></td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center text-muted py-4">
                                    <i class="fas fa-users fa-2x mb-2 d-block"></i>
                                    لا يوجد موظفين
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card-enhanced fade-in" style="animation-delay: 1.0s;">
            <div class="card-header py-3">
                <h6 class="m-0 fw-bold">
                    <i class="fas fa-calendar-alt me-2"></i>آخر الإجازات
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-enhanced">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>نوع الإجازة</th>
                                <th>من</th>
                                <th>إلى</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for leave in recent_leaves %}
                            <tr>
                                <td class="fw-bold">{{ leave.employee.full_name }}</td>
                                <td><span class="text-muted">{{ leave.leave_type }}</span></td>
                                <td><span class="badge bg-secondary">{{ leave.start_date|date:"Y-m-d" }}</span></td>
                                <td><span class="badge bg-secondary">{{ leave.end_date|date:"Y-m-d" }}</span></td>
                                <td>
                                    {% if leave.status == 'pending' %}
                                        <span class="badge bg-warning rounded-pill">
                                            <i class="fas fa-clock me-1"></i>قيد الانتظار
                                        </span>
                                    {% elif leave.status == 'approved' %}
                                        <span class="badge bg-success rounded-pill">
                                            <i class="fas fa-check me-1"></i>موافق عليها
                                        </span>
                                    {% elif leave.status == 'rejected' %}
                                        <span class="badge bg-danger rounded-pill">
                                            <i class="fas fa-times me-1"></i>مرفوضة
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted py-4">
                                    <i class="fas fa-calendar-times fa-2x mb-2 d-block"></i>
                                    لا يوجد إجازات
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Row 4 - Additional Tables -->
<div class="row">
    <div class="col-lg-6">
        <div class="card-enhanced fade-in" style="animation-delay: 1.1s;">
            <div class="card-header py-3">
                <h6 class="m-0 fw-bold">
                    <i class="fas fa-folder-open me-2"></i>آخر حركات الملفات
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-enhanced">
                        <thead>
                            <tr>
                                <th>رقم الملف</th>
                                <th>العنوان</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in recent_file_movements %}
                            <tr>
                                <td><span class="badge bg-secondary">-</span></td>
                                <td class="fw-bold">
                                    {{ movement.employee.full_name }}
                                </td>
                                <td><span class="badge bg-info rounded-pill">{{ movement.status }}</span></td>
                                <td><span class="badge bg-primary">{{ movement.created_at|date:"Y-m-d" }}</span></td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center text-muted py-4">
                                    <i class="fas fa-folder fa-2x mb-2 d-block"></i>
                                    لا توجد حركات ملفات
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card-enhanced fade-in" style="animation-delay: 1.2s;">
            <div class="card-header py-3">
                <h6 class="m-0 fw-bold">
                    <i class="fas fa-medal me-2"></i>آخر الرتب المضافة
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-enhanced">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>نوع الرتبة</th>
                                <th>تاريخ الرتبة</th>
                                <th>الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for rank in recent_ranks %}
                            <tr>
                                <td class="fw-bold">{{ rank.employee.full_name }}</td>
                                <td><span class="badge bg-warning rounded-pill">{{ rank.rank_type.name }}</span></td>
                                <td><span class="badge bg-success">{{ rank.date_obtained|date:"Y-m-d" }}</span></td>
                                <td><span class="text-muted">{{ rank.notes|truncatechars:30 }}</span></td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center text-muted py-4">
                                    <i class="fas fa-medal fa-2x mb-2 d-block"></i>
                                    لا توجد رتب مضافة
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Row 5 - Leave Types -->
<div class="row">
    <div class="col-lg-12">
        <div class="card-enhanced fade-in" style="animation-delay: 1.3s;">
            <div class="card-header py-3">
                <h6 class="m-0 fw-bold">
                    <i class="fas fa-calendar-check me-2"></i>أنواع الإجازات المتاحة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for leave_type in leave_types %}
                    <div class="col-md-3 mb-3">
                        <div class="card-enhanced h-100">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-calendar-alt fa-2x text-primary"></i>
                                </div>
                                <h5 class="card-title fw-bold">{{ leave_type.get_name_display }}</h5>
                                <p class="card-text small text-muted">{{ leave_type.description|default:"لا يوجد وصف"|truncatechars:100 }}</p>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center text-muted py-5">
                        <i class="fas fa-calendar-times fa-3x mb-3 d-block"></i>
                        <p class="lead">لا توجد أنواع إجازات متاحة</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Row 6 - Quick Links -->
<div class="row">
    <div class="col-lg-12">
        <div class="card-enhanced fade-in" style="animation-delay: 1.4s;">
            <div class="card-header py-3">
                <h6 class="m-0 fw-bold">
                    <i class="fas fa-link me-2"></i>روابط سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'employees:employee_list' %}" class="btn btn-enhanced btn-primary w-100 d-flex align-items-center justify-content-center py-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-users me-2"></i> بيانات الموظفين
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'leaves:leave_list' %}" class="btn btn-enhanced btn-success w-100 d-flex align-items-center justify-content-center py-3" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                            <i class="fas fa-calendar-alt me-2"></i> الإجازات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'file_management:file_movement_list' %}" class="btn btn-enhanced btn-info w-100 d-flex align-items-center justify-content-center py-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-folder me-2"></i> الملفات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'ranks:employee_rank_list' %}" class="btn btn-enhanced btn-warning w-100 d-flex align-items-center justify-content-center py-3" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                            <i class="fas fa-medal me-2"></i> الرتب
                        </a>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'performance:performance_list' %}" class="btn btn-enhanced btn-danger w-100 d-flex align-items-center justify-content-center py-3" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);">
                            <i class="fas fa-file-alt me-2"></i> التقارير السنوية
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'reports:report_dashboard' %}" class="btn btn-enhanced btn-secondary w-100 d-flex align-items-center justify-content-center py-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-chart-pie me-2"></i> تقارير النظام
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'disciplinary:penalty_list' %}" class="btn btn-enhanced btn-dark w-100 d-flex align-items-center justify-content-center py-3" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);">
                            <i class="fas fa-calendar-times me-2"></i> الإجراءات التأديبية
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'system_logs:system_log_list' %}" class="btn btn-enhanced btn-primary w-100 d-flex align-items-center justify-content-center py-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-history me-2"></i> سجل حركات النظام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Enhanced Interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate counters
    function animateCounters() {
        const counters = document.querySelectorAll('.h3');

        counters.forEach(counter => {
            const target = parseInt(counter.textContent);
            if (isNaN(target)) return;

            const duration = 2000;
            const increment = target / (duration / 16);
            let current = 0;

            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };

            // Start animation when element is visible
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        setTimeout(updateCounter, 500);
                        observer.unobserve(entry.target);
                    }
                });
            });

            observer.observe(counter);
        });
    }

    // Initialize scroll animations
    function initScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0) scale(1)';
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.fade-in').forEach(element => {
            observer.observe(element);
        });
    }

    // Add hover effects to cards
    function addCardEffects() {
        const cards = document.querySelectorAll('.dashboard-card, .card-enhanced');

        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-15px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    // Initialize all animations
    animateCounters();
    initScrollAnimations();
    addCardEffects();
});
</script>
{% endblock %}
