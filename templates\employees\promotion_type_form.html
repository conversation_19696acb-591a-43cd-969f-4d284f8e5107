{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if is_update %}تعديل نوع الترفيع{% else %}إضافة نوع ترفيع{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .form-container {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #28a745;
    }

    .section-title {
        color: #28a745;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-action {
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        min-width: 120px;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
    }

    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .form-floating > label {
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label {
        color: #28a745;
    }

    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1 class="mb-3">
            <i class="fas fa-arrow-up"></i>
            {% if is_update %}تعديل نوع الترفيع{% else %}إضافة نوع ترفيع جديد{% endif %}
        </h1>
        <p class="lead mb-0">
            {% if is_update %}
                تعديل بيانات نوع الترفيع
            {% else %}
                إضافة نوع ترفيع جديد للنظام
            {% endif %}
        </p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container fade-in">
                <form method="post" id="promotionTypeForm">
                    {% csrf_token %}
                    
                    <!-- Promotion Type Information Section -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-info-circle"></i>
                            معلومات نوع الترفيع
                        </h5>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="form-floating mb-3">
                                    {{ form.name }}
                                    <label for="{{ form.name.id_for_label }}" class="required-field">{{ form.name.label }}</label>
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.name.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-floating mb-3">
                                    {{ form.description }}
                                    <label for="{{ form.description.id_for_label }}">{{ form.description.label }}</label>
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.description.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'employees:promotion_types_list' %}" class="btn btn-outline-secondary btn-action">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                        <button type="submit" class="btn btn-success btn-action">
                            <i class="fas fa-save"></i>
                            {% if is_update %}تحديث النوع{% else %}حفظ النوع{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    console.log('Promotion Type Form loaded');

    // Form validation
    $('#promotionTypeForm').on('submit', function(e) {
        const name = $('#id_name').val().trim();

        if (!name) {
            e.preventDefault();
            alert('الرجاء إدخال نوع الترفيع');
            $('#id_name').focus();
            return false;
        }

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
        submitBtn.prop('disabled', true);

        // Re-enable button after 3 seconds (in case of error)
        setTimeout(function() {
            submitBtn.html(originalText);
            submitBtn.prop('disabled', false);
        }, 3000);
    });

    // Auto-resize textarea
    $('#id_description').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Focus on first input
    $('#id_name').focus();
});
</script>
{% endblock %}
