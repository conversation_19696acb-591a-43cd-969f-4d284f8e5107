# Generated manually to add casual leave type
from django.db import migrations

def add_casual_leave_type(apps, schema_editor):
    """Add casual leave type to the database"""
    LeaveType = apps.get_model('leaves', 'LeaveType')
    
    # Create casual leave type if it doesn't exist
    casual_leave, created = LeaveType.objects.get_or_create(
        name='casual',
        defaults={
            'description': 'إجازة عرضية للموظفين',
            'max_days_per_year': 7
        }
    )
    
    if created:
        print(f"✅ تم إنشاء نوع الإجازة العرضية: {casual_leave}")
    else:
        print(f"ℹ️ نوع الإجازة العرضية موجود مسبقاً: {casual_leave}")

def remove_casual_leave_type(apps, schema_editor):
    """Remove casual leave type from the database"""
    LeaveType = apps.get_model('leaves', 'LeaveType')
    
    try:
        casual_leave = LeaveType.objects.get(name='casual')
        casual_leave.delete()
        print("❌ تم حذف نوع الإجازة العرضية")
    except LeaveType.DoesNotExist:
        print("ℹ️ نوع الإجازة العرضية غير موجود")

class Migration(migrations.Migration):

    dependencies = [
        ('leaves', '0004_alter_leavetype_name'),
    ]

    operations = [
        migrations.RunPython(add_casual_leave_type, remove_casual_leave_type),
    ]
