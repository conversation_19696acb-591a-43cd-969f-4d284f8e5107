{% extends 'base.html' %}
{% load static %}

{% block title %}{% if employee_course %}تعديل دورة موظف{% else %}إضافة دورة لموظف{% endif %} - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    .employee-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .employee-info h5 {
        margin-bottom: 15px;
        font-weight: bold;
    }
    .employee-info .info-item {
        margin-bottom: 8px;
    }
    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
    }
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 36px;
        padding-left: 12px;
    }
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }
    .search-section {
        background: #f8f9fc;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 20px;
        margin-bottom: 20px;
    }
    .input-group .form-control {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
    .input-group .btn {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    .alert-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 0.25rem;
    }
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
    #id_hours:disabled {
        background-color: #f8f9fa;
        opacity: 0.8;
    }
    .border-left-info {
        border-left: 4px solid #36b9cc !important;
    }
    #coursePreview {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border: 1px solid #36b9cc;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% if employee_course %}تعديل دورة موظف{% else %}إضافة دورة لموظف{% endif %}</h2>
    <a href="{% url 'ranks:employee_course_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة لدورات الموظفين
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <form method="post" id="employeeCourseForm">
            {% csrf_token %}

            <!-- Employee Search Section -->
            <div class="search-section">
                <h5 class="mb-3"><i class="fas fa-search me-2"></i>البحث عن الموظف</h5>
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.ministry_number.id_for_label }}" class="form-label">الرقم الوزاري</label>
                        <div class="input-group">
                            {{ form.ministry_number }}
                            <button type="button" id="searchEmployee" class="btn btn-info">
                                <i class="fas fa-search" id="searchIcon"></i>
                                <span id="searchText">البحث</span>
                            </button>
                        </div>
                        {% if form.ministry_number.errors %}
                            <div class="text-danger">{{ form.ministry_number.errors.0 }}</div>
                        {% endif %}
                        <small class="form-text text-muted">اضغط Enter أو انقر على زر البحث</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">حالة البحث</label>
                        <div id="searchStatus" class="alert alert-info" style="display: none;">
                            <i class="fas fa-info-circle"></i> أدخل الرقم الوزاري للبحث عن الموظف
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- Employee Info Display -->
            <div id="employeeInfo" class="employee-info" style="display: none;">
                <h5><i class="fas fa-user me-2"></i>معلومات الموظف</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item"><strong>الاسم:</strong> <span id="employeeName"></span></div>
                        <div class="info-item"><strong>الرقم الوزاري:</strong> <span id="employeeMinistry"></span></div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item"><strong>القسم:</strong> <span id="employeeDepartment"></span></div>
                        <div class="info-item"><strong>المسمى الوظيفي:</strong> <span id="employeePosition"></span></div>
                    </div>
                </div>
            </div>

            <!-- Course Details Section -->
            <div id="courseSection" style="display: none;">
                <h5 class="mb-3"><i class="fas fa-graduation-cap me-2"></i>تفاصيل الدورة</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.course.id_for_label }}" class="form-label">
                                <i class="fas fa-graduation-cap me-1"></i>اسم الدورة
                            </label>
                            {{ form.course }}
                            {% if form.course.errors %}
                                <div class="text-danger">{{ form.course.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                اختر الدورة من القائمة، سيتم تحديد عدد الساعات تلقائياً
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.hours.id_for_label }}" class="form-label">
                                <i class="fas fa-clock me-1"></i>عدد الساعات
                                <span class="text-muted">(سيتم تحديدها تلقائياً عند اختيار الدورة)</span>
                            </label>
                            {{ form.hours }}
                            {% if form.hours.errors %}
                                <div class="text-danger">{{ form.hours.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم ملء هذا الحقل تلقائياً عند اختيار الدورة، أو يمكنك إدخاله يدوياً
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course Preview Section -->
                <div id="coursePreview" class="alert alert-info border-left-info" style="display: none;">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-eye text-info me-2"></i>
                        <h6 class="mb-0">معاينة الدورة المختارة</h6>
                    </div>
                    <div class="row">
                        <div class="col-md-8">
                            <p class="mb-1"><strong><i class="fas fa-graduation-cap me-1"></i>اسم الدورة:</strong>
                                <span id="previewCourseName" class="text-primary">-</span>
                            </p>
                        </div>
                        <div class="col-md-4">
                            <p class="mb-1"><strong><i class="fas fa-clock me-1"></i>عدد الساعات:</strong>
                                <span id="previewCourseHours" class="badge bg-primary">-</span>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.completion_date.id_for_label }}" class="form-label">
                                <i class="fas fa-calendar me-1"></i>تاريخ الحصول على الدورة
                            </label>
                            {{ form.completion_date }}
                            {% if form.completion_date.errors %}
                                <div class="text-danger">{{ form.completion_date.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.certificate_number.id_for_label }}" class="form-label">
                                <i class="fas fa-certificate me-1"></i>رقم الشهادة (اختياري)
                            </label>
                            {{ form.certificate_number }}
                            {% if form.certificate_number.errors %}
                                <div class="text-danger">{{ form.certificate_number.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات (اختياري)</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger">{{ form.notes.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {% if employee_course %}تحديث الدورة{% else %}حفظ الدورة{% endif %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 for course dropdown
    $('#id_course').select2({
        placeholder: 'اختر الدورة',
        allowClear: true,
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });

    // Auto-fill hours when course is selected
    $('#id_course').on('change', function() {
        const courseId = $(this).val();
        const courseName = $(this).find('option:selected').text();
        const hoursInput = document.getElementById('id_hours');
        const coursePreview = document.getElementById('coursePreview');
        const previewCourseName = document.getElementById('previewCourseName');
        const previewCourseHours = document.getElementById('previewCourseHours');

        if (courseId) {
            // Show course preview
            previewCourseName.textContent = courseName;
            coursePreview.style.display = 'block';

            // Show loading indicator
            hoursInput.value = '';
            hoursInput.placeholder = 'جاري التحميل...';
            hoursInput.disabled = true;
            previewCourseHours.textContent = 'جاري التحميل...';
            previewCourseHours.className = 'badge bg-secondary';

            // Fetch course details
            fetch(`/ranks/api/course/${courseId}/`)
                .then(response => response.json())
                .then(data => {
                    if (data.hours && data.hours > 0) {
                        hoursInput.value = data.hours;
                        hoursInput.placeholder = 'عدد الساعات';
                        previewCourseHours.textContent = data.hours + ' ساعة';
                        previewCourseHours.className = 'badge bg-success';

                        // Show success message
                        showHoursMessage(`تم تحديد عدد الساعات تلقائياً: ${data.hours} ساعة`, 'success');
                    } else {
                        hoursInput.value = '';
                        hoursInput.placeholder = 'أدخل عدد الساعات يدوياً';
                        previewCourseHours.textContent = 'غير محدد';
                        previewCourseHours.className = 'badge bg-warning';
                        showHoursMessage('لم يتم تحديد عدد الساعات للدورة، يرجى إدخالها يدوياً', 'warning');
                    }
                    hoursInput.disabled = false;
                })
                .catch(error => {
                    console.error('Error fetching course details:', error);
                    hoursInput.value = '';
                    hoursInput.placeholder = 'أدخل عدد الساعات';
                    hoursInput.disabled = false;
                    previewCourseHours.textContent = 'خطأ في التحميل';
                    previewCourseHours.className = 'badge bg-danger';
                    showHoursMessage('حدث خطأ في تحميل بيانات الدورة، يرجى إدخال عدد الساعات يدوياً', 'danger');
                });
        } else {
            hoursInput.value = '';
            hoursInput.placeholder = 'أدخل عدد الساعات';
            hoursInput.disabled = false;
            coursePreview.style.display = 'none';
            hideHoursMessage();
        }
    });

    // Function to show hours message
    function showHoursMessage(message, type) {
        let messageDiv = document.getElementById('hoursMessage');
        if (!messageDiv) {
            messageDiv = document.createElement('div');
            messageDiv.id = 'hoursMessage';
            messageDiv.style.marginTop = '5px';
            document.getElementById('id_hours').parentNode.appendChild(messageDiv);
        }

        messageDiv.className = `alert alert-${type} alert-sm`;
        messageDiv.innerHTML = `<i class="fas fa-info-circle me-1"></i>${message}`;
        messageDiv.style.display = 'block';

        // Auto-hide success messages after 3 seconds
        if (type === 'success') {
            setTimeout(() => {
                hideHoursMessage();
            }, 3000);
        }
    }

    // Function to hide hours message
    function hideHoursMessage() {
        const messageDiv = document.getElementById('hoursMessage');
        if (messageDiv) {
            messageDiv.style.display = 'none';
        }
    }

    // Update preview when hours are changed manually
    document.getElementById('id_hours').addEventListener('input', function() {
        const hoursValue = this.value;
        const previewCourseHours = document.getElementById('previewCourseHours');
        const coursePreview = document.getElementById('coursePreview');

        if (coursePreview.style.display !== 'none' && hoursValue) {
            previewCourseHours.textContent = hoursValue + ' ساعة';
            previewCourseHours.className = 'badge bg-info';
        }
    });

    // Add Bootstrap classes to form fields
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(element) {
        if (!element.classList.contains('select2-hidden-accessible')) {
            element.classList.add('form-control');
        }
    });

    // Search employee functionality
    function searchEmployee() {
        const ministryNumber = document.getElementById('id_ministry_number').value.trim();
        const searchStatus = document.getElementById('searchStatus');
        const searchButton = document.getElementById('searchEmployee');
        const searchIcon = document.getElementById('searchIcon');
        const searchText = document.getElementById('searchText');

        if (!ministryNumber) {
            searchStatus.className = 'alert alert-warning';
            searchStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> الرجاء إدخال الرقم الوزاري';
            searchStatus.style.display = 'block';
            document.getElementById('employeeInfo').style.display = 'none';
            document.getElementById('courseSection').style.display = 'none';
            return;
        }

        // Show loading status
        searchButton.disabled = true;
        searchIcon.className = 'fas fa-spinner fa-spin';
        searchText.textContent = 'جاري البحث...';
        searchStatus.className = 'alert alert-info';
        searchStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري البحث عن الموظف...';
        searchStatus.style.display = 'block';

        // Make AJAX request to search for employee
        fetch(`/employees/api/search/?ministry_number=${ministryNumber}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Display employee info
                    document.getElementById('employeeName').textContent = data.employee.full_name;
                    document.getElementById('employeeMinistry').textContent = data.employee.ministry_number;
                    document.getElementById('employeeDepartment').textContent = data.employee.department || 'غير محدد';
                    document.getElementById('employeePosition').textContent = data.employee.position || 'غير محدد';

                    document.getElementById('employeeInfo').style.display = 'block';
                    document.getElementById('courseSection').style.display = 'block';

                    // Show success status
                    searchStatus.className = 'alert alert-success';
                    searchStatus.innerHTML = '<i class="fas fa-check"></i> تم العثور على الموظف بنجاح';
                } else {
                    // Show error status
                    searchStatus.className = 'alert alert-danger';
                    searchStatus.innerHTML = `<i class="fas fa-times"></i> ${data.error || 'لم يتم العثور على الموظف'}`;
                    document.getElementById('employeeInfo').style.display = 'none';
                    document.getElementById('courseSection').style.display = 'none';
                }
            })
            .finally(() => {
                // Reset button state
                searchButton.disabled = false;
                searchIcon.className = 'fas fa-search';
                searchText.textContent = 'البحث';
            })
            .catch(error => {
                console.error('Error:', error);
                searchStatus.className = 'alert alert-danger';
                searchStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> حدث خطأ أثناء البحث عن الموظف. تأكد من الاتصال بالإنترنت.';
                document.getElementById('employeeInfo').style.display = 'none';
                document.getElementById('courseSection').style.display = 'none';

                // Reset button state
                searchButton.disabled = false;
                searchIcon.className = 'fas fa-search';
                searchText.textContent = 'البحث';
            });
    }

    // Add click event listener to search button
    document.getElementById('searchEmployee').addEventListener('click', searchEmployee);

    // Add Enter key event listener to ministry number input
    document.getElementById('id_ministry_number').addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            searchEmployee();
        }
    });

    // Add input event listener for real-time validation
    document.getElementById('id_ministry_number').addEventListener('input', function() {
        const ministryNumber = this.value.trim();
        const searchStatus = document.getElementById('searchStatus');

        if (!ministryNumber) {
            searchStatus.style.display = 'none';
            document.getElementById('employeeInfo').style.display = 'none';
            document.getElementById('courseSection').style.display = 'none';
        } else {
            // Show hint for valid input
            searchStatus.className = 'alert alert-info';
            searchStatus.innerHTML = '<i class="fas fa-info-circle"></i> اضغط Enter أو انقر على زر البحث للعثور على الموظف';
            searchStatus.style.display = 'block';
        }
    });

    // Add focus event to show instructions
    document.getElementById('id_ministry_number').addEventListener('focus', function() {
        const searchStatus = document.getElementById('searchStatus');
        if (!this.value.trim()) {
            searchStatus.className = 'alert alert-info';
            searchStatus.innerHTML = '<i class="fas fa-info-circle"></i> أدخل الرقم الوزاري للموظف';
            searchStatus.style.display = 'block';
        }
    });

    // Show initial search status
    document.getElementById('searchStatus').style.display = 'block';

    // If editing existing course, show course section
    {% if employee_course %}
        document.getElementById('employeeInfo').style.display = 'block';
        document.getElementById('courseSection').style.display = 'block';
        
        // Pre-fill employee info
        document.getElementById('employeeName').textContent = '{{ employee_course.employee.full_name }}';
        document.getElementById('employeeMinistry').textContent = '{{ employee_course.employee.ministry_number }}';
        document.getElementById('employeeDepartment').textContent = '{{ employee_course.employee.current_employment.department.name|default:"غير محدد" }}';
        document.getElementById('employeePosition').textContent = '{{ employee_course.employee.current_employment.position.name|default:"غير محدد" }}';
    {% endif %}
});
</script>
{% endblock %}
