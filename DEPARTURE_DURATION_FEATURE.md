# إضافة حقل المدة للمغادرات
# Adding Duration Field to Departures

## الميزة الجديدة

### ✅ حقل المدة بالأيام
تم إضافة حقل جديد لحساب مدة المغادرة بالأيام باستخدام المعادلة:
**كل 420 دقيقة = يوم واحد**

## التحديثات المنفذة

### 1. تحديث النموذج (Model)

#### **إضافة الحقل الجديد:**
```python
# في leaves/models.py
class Departure(models.Model):
    # ... الحقول الموجودة
    duration_days = models.DecimalField(
        _('Duration in Days'), 
        max_digits=5, 
        decimal_places=2, 
        blank=True, 
        null=True
    )
```

#### **دالة حساب المدة:**
```python
def calculate_duration_days(self):
    """حساب مدة المغادرة بالأيام (كل 420 دقيقة = يوم واحد)"""
    if self.time_from and self.time_to:
        # تحويل الأوقات إلى دقائق
        from_minutes = self.time_from.hour * 60 + self.time_from.minute
        to_minutes = self.time_to.hour * 60 + self.time_to.minute
        
        # حساب الفرق بالدقائق
        duration_minutes = to_minutes - from_minutes
        
        # إذا كان الوقت النهائي أقل من البداية (عبور منتصف الليل)
        if duration_minutes < 0:
            duration_minutes += 24 * 60  # إضافة 24 ساعة
        
        # تحويل إلى أيام (كل 420 دقيقة = يوم واحد)
        duration_days = duration_minutes / 420
        return round(duration_days, 2)
    return 0
```

#### **حفظ تلقائي للمدة:**
```python
def save(self, *args, **kwargs):
    """حفظ النموذج مع حساب المدة تلقائياً"""
    self.duration_days = self.calculate_duration_days()
    super().save(*args, **kwargs)
```

### 2. Migration

#### **ملف Migration:**
```python
# leaves/migrations/0005_departure_duration_days.py
operations = [
    migrations.AddField(
        model_name='departure',
        name='duration_days',
        field=models.DecimalField(
            blank=True, 
            decimal_places=2, 
            max_digits=5, 
            null=True, 
            verbose_name='Duration in Days'
        ),
    ),
]
```

### 3. تحديث القوالب (Templates)

#### **أ) قائمة المغادرات (`departure_list.html`):**

**إضافة عمود المدة:**
```html
<!-- في رأس الجدول -->
<th><i class="fas fa-hourglass-half text-primary"></i> المدة</th>

<!-- في محتوى الجدول -->
<td>
    <span class="badge bg-info">
        <i class="fas fa-calendar-day"></i> 
        {% if departure.duration_days %}
            {{ departure.duration_days }} يوم
        {% else %}
            {{ departure.calculate_duration_days }} يوم
        {% endif %}
    </span>
</td>
```

#### **ب) تفاصيل المغادرة (`departure_detail.html`):**

**تحديث تخطيط الأوقات:**
```html
<div class="row">
    <div class="col-md-4">من الساعة</div>
    <div class="col-md-4">إلى الساعة</div>
    <div class="col-md-4">
        <label class="form-label fw-bold">
            <i class="fas fa-hourglass-half text-primary"></i> المدة:
        </label>
        <p class="form-control-plaintext">
            <span class="badge bg-info fs-6">
                <i class="fas fa-calendar-day"></i> 
                {{ departure.duration_days|default:departure.calculate_duration_days }} يوم
            </span>
        </p>
    </div>
</div>
```

#### **ج) نموذج الإضافة/التعديل (`departure_form.html`):**

**تحديث تخطيط الحقول:**
```html
<div class="row">
    <div class="col-md-4 mb-3">من الساعة</div>
    <div class="col-md-4 mb-3">إلى الساعة</div>
    <div class="col-md-4 mb-3">
        <label class="form-label">المدة</label>
        <div class="form-control-plaintext">
            <span id="duration_display" class="badge bg-info fs-6">
                <i class="fas fa-calendar-day"></i> 0.00 يوم
            </span>
        </div>
    </div>
</div>
```

#### **د) تأكيد الحذف (`departure_confirm_delete.html`):**

**إضافة عرض المدة:**
```html
<p><strong><i class="fas fa-hourglass-half"></i> المدة:</strong> 
    <span class="badge bg-info">
        {{ departure.duration_days|default:departure.calculate_duration_days }} يوم
    </span>
</p>
```

### 4. JavaScript للحساب التلقائي

#### **في نموذج الإضافة/التعديل:**
```javascript
function calculateDuration() {
    const timeFrom = timeFromInput.value;
    const timeTo = timeToInput.value;

    if (timeFrom && timeTo) {
        // تحويل الأوقات إلى دقائق
        const [fromHours, fromMinutes] = timeFrom.split(':').map(Number);
        const [toHours, toMinutes] = timeTo.split(':').map(Number);

        const fromTotalMinutes = fromHours * 60 + fromMinutes;
        let toTotalMinutes = toHours * 60 + toMinutes;

        // إذا كان الوقت النهائي أقل من البداية (عبور منتصف الليل)
        if (toTotalMinutes < fromTotalMinutes) {
            toTotalMinutes += 24 * 60; // إضافة 24 ساعة
        }

        // حساب الفرق بالدقائق
        const durationMinutes = toTotalMinutes - fromTotalMinutes;

        // تحويل إلى أيام (كل 420 دقيقة = يوم واحد)
        const durationDays = (durationMinutes / 420).toFixed(2);

        // تحديث العرض
        durationDisplay.innerHTML = `<i class="fas fa-calendar-day"></i> ${durationDays} يوم`;
        
        // تغيير اللون حسب المدة
        durationDisplay.className = 'badge fs-6 ' + (durationDays > 1 ? 'bg-warning' : 'bg-info');
    }
}

// إضافة مستمعي الأحداث
timeFromInput.addEventListener('change', calculateDuration);
timeToInput.addEventListener('change', calculateDuration);
```

## معادلة الحساب

### **القاعدة الأساسية:**
```
المدة بالأيام = (الوقت النهائي - الوقت الابتدائي) بالدقائق ÷ 420
```

### **أمثلة على الحساب:**

| من الساعة | إلى الساعة | المدة بالدقائق | المدة بالأيام | الحساب |
|-----------|------------|----------------|---------------|---------|
| 08:00 | 15:00 | 420 دقيقة | 1.00 يوم | 420 ÷ 420 = 1 |
| 09:00 | 12:00 | 180 دقيقة | 0.43 يوم | 180 ÷ 420 = 0.43 |
| 08:00 | 16:00 | 480 دقيقة | 1.14 يوم | 480 ÷ 420 = 1.14 |
| 14:00 | 17:30 | 210 دقيقة | 0.50 يوم | 210 ÷ 420 = 0.50 |

### **حالات خاصة:**

#### **عبور منتصف الليل:**
```
من الساعة: 22:00
إلى الساعة: 06:00
المدة = (6:00 + 24:00) - 22:00 = 8 ساعات = 480 دقيقة = 1.14 يوم
```

## الميزات المضافة

### 1. **حساب تلقائي:**
- المدة تُحسب تلقائياً عند الحفظ
- عرض فوري في النموذج عند تغيير الأوقات

### 2. **عرض بصري محسن:**
- أيقونة مميزة للمدة
- ألوان مختلفة حسب المدة:
  - أزرق فاتح: أقل من يوم
  - أصفر: يوم أو أكثر

### 3. **دقة في الحساب:**
- دعم الأرقام العشرية (حتى منزلتين)
- معالجة حالة عبور منتصف الليل
- تقريب النتائج لسهولة القراءة

### 4. **تكامل شامل:**
- ظهور المدة في جميع صفحات المغادرات
- حفظ تلقائي في قاعدة البيانات
- عرض متسق في جميع القوالب

## الفوائد المحققة

### 1. **سهولة المتابعة:**
- معرفة فورية لمدة كل مغادرة
- مقارنة سريعة بين المغادرات المختلفة

### 2. **دقة في الحسابات:**
- معادلة موحدة لجميع المغادرات
- تجنب الأخطاء اليدوية في الحساب

### 3. **تحسين الإنتاجية:**
- عدم الحاجة لحساب المدة يدوياً
- عرض فوري للمدة أثناء الإدخال

### 4. **مرونة في الاستخدام:**
- دعم المغادرات القصيرة والطويلة
- معالجة جميع أنواع الأوقات

## للاختبار

### 1. **إضافة مغادرة جديدة:**
```
http://localhost:8000/leaves/departures/add/
```
- أدخل أوقات مختلفة ولاحظ تحديث المدة فوراً

### 2. **عرض قائمة المغادرات:**
```
http://localhost:8000/leaves/departures/
```
- لاحظ عمود المدة الجديد مع الأيقونة

### 3. **تفاصيل مغادرة:**
- اضغط على أيقونة العين لأي مغادرة
- لاحظ عرض المدة في قسم التفاصيل

## الحالة
✅ **مكتمل** - تم إضافة حقل المدة بنجاح مع جميع الميزات المطلوبة.
