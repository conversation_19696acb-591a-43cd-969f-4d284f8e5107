{% extends 'base.html' %}
{% load static %}

{% block title %}حذف المغادرة - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-exclamation-triangle text-danger"></i> تأكيد حذف المغادرة</h2>
    <a href="{% url 'leaves:departure_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للقائمة
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3 bg-danger text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-trash"></i> تأكيد حذف المغادرة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تحذير:</strong> هل أنت متأكد من حذف هذه المغادرة؟ لا يمكن التراجع عن هذا الإجراء.
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <p><strong><i class="fas fa-user"></i> الموظف:</strong> {{ departure.employee.full_name }}</p>
                        <p><strong><i class="fas fa-id-card"></i> الرقم الوزاري:</strong> {{ departure.employee.ministry_number }}</p>
                        <p><strong><i class="fas fa-tag"></i> نوع المغادرة:</strong>
                            {% if departure.departure_type == 'personal' %}
                                <span class="badge bg-info"><i class="fas fa-user"></i> خاصة</span>
                            {% else %}
                                <span class="badge bg-primary"><i class="fas fa-building"></i> رسمية</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong><i class="fas fa-calendar"></i> التاريخ:</strong> {{ departure.date|date:"Y/m/d" }}</p>
                        <p><strong><i class="fas fa-clock"></i> الوقت:</strong> {{ departure.time_from }} - {{ departure.time_to }}</p>
                        <p><strong><i class="fas fa-hourglass-half"></i> المدة:</strong>
                            {% with hours=departure.get_duration_hours_minutes.0 minutes=departure.get_duration_hours_minutes.1 total_minutes=departure.calculate_duration_minutes %}
                                {% if total_minutes > 240 %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        {{ hours }}س {{ minutes }}د (تجاوز الحد الأقصى)
                                    </span>
                                {% elif total_minutes > 180 %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock"></i>
                                        {{ hours }}س {{ minutes }}د
                                    </span>
                                {% else %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i>
                                        {{ hours }}س {{ minutes }}د
                                    </span>
                                {% endif %}
                            {% endwith %}
                        </p>
                        <p><strong><i class="fas fa-check-circle"></i> الحالة:</strong>
                            {% if departure.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif departure.status == 'approved' %}
                                <span class="badge bg-success">موافق عليها</span>
                            {% elif departure.status == 'rejected' %}
                                <span class="badge bg-danger">مرفوضة</span>
                            {% endif %}
                        </p>
                    </div>
                </div>

                {% if departure.reason %}
                <div class="row">
                    <div class="col-12">
                        <p><strong><i class="fas fa-comment"></i> السبب:</strong> {{ departure.reason }}</p>
                    </div>
                </div>
                {% endif %}

                <hr>

                <div class="d-flex justify-content-end gap-2">
                    <a href="{% url 'leaves:departure_detail' departure.pk %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                    <form method="post" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> تأكيد الحذف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
