#!/usr/bin/env python3
"""
التحقق الشامل من جميع المكتبات المطلوبة للنظام
Comprehensive verification of all system dependencies
"""

import sys
import subprocess
import importlib
from pathlib import Path

def test_import(module_name, package_name=None, description=""):
    """اختبار استيراد مكتبة معينة"""
    try:
        if package_name:
            # استيراد جزء معين من المكتبة
            module = importlib.import_module(module_name)
            if hasattr(module, package_name):
                return True, f"✅ {description or module_name}"
            else:
                return False, f"❌ {description or module_name} - missing {package_name}"
        else:
            importlib.import_module(module_name)
            return True, f"✅ {description or module_name}"
    except ImportError as e:
        return False, f"❌ {description or module_name} - {str(e)}"

def check_all_dependencies():
    """فحص جميع المكتبات المطلوبة"""
    
    print("=" * 80)
    print("🔍 فحص شامل لجميع مكتبات نظام إدارة شؤون الموظفين")
    print("=" * 80)
    
    # قائمة شاملة بجميع المكتبات المطلوبة
    dependencies = [
        # Django Core
        ("django", None, "Django Framework"),
        ("django.contrib.admin", None, "Django Admin"),
        ("django.contrib.auth", None, "Django Authentication"),
        ("django.contrib.contenttypes", None, "Django Content Types"),
        ("django.contrib.sessions", None, "Django Sessions"),
        ("django.contrib.messages", None, "Django Messages"),
        ("django.contrib.staticfiles", None, "Django Static Files"),
        
        # Django Extensions
        ("import_export", None, "Django Import Export"),
        ("crispy_forms", None, "Django Crispy Forms"),
        ("crispy_bootstrap5", None, "Crispy Bootstrap5"),
        ("widget_tweaks", None, "Django Widget Tweaks"),
        
        # Database
        ("sqlite3", None, "SQLite3 (built-in)"),
        
        # File Processing
        ("PIL", None, "Pillow (Image Processing)"),
        ("openpyxl", None, "OpenPyXL (Excel Files)"),
        ("xlsxwriter", None, "XlsxWriter (Excel Writing)"),
        ("xlrd", None, "XLRD (Excel Reading)"),
        ("xlwt", None, "XLWT (Excel Writing)"),
        ("pandas", None, "Pandas (Data Analysis)"),
        ("numpy", None, "NumPy (Numerical Computing)"),
        
        # PDF Generation
        ("reportlab", None, "ReportLab (PDF Creation)"),
        ("weasyprint", None, "WeasyPrint (HTML to PDF)"),
        ("xhtml2pdf", None, "xhtml2pdf (Alternative PDF)"),
        ("arabic_reshaper", None, "Arabic Reshaper"),
        ("bidi", None, "Python BiDi"),
        
        # Date & Time
        ("dateutil", None, "Python DateUtil"),
        ("pytz", None, "PyTZ (Timezone)"),
        
        # HTTP & API
        ("requests", None, "Requests (HTTP)"),
        ("urllib3", None, "urllib3"),
        ("rest_framework", None, "Django REST Framework"),
        
        # Forms & UI
        ("django_filter", None, "Django Filter"),
        ("django_tables2", None, "Django Tables2"),
        ("django_bootstrap5", None, "Django Bootstrap5"),
        
        # Utilities
        ("humanize", None, "Humanize"),
        ("six", None, "Six"),
        ("certifi", None, "Certifi"),
        ("charset_normalizer", None, "Charset Normalizer"),
        ("idna", None, "IDNA"),
        
        # Development (Optional)
        ("whitenoise", None, "WhiteNoise (Static Files)"),
        ("gunicorn", None, "Gunicorn (WSGI Server)"),
    ]
    
    # تصنيف النتائج
    success_count = 0
    failed_count = 0
    failed_modules = []
    
    print("\n📦 فحص المكتبات الأساسية:")
    print("-" * 50)
    
    for module_name, package_name, description in dependencies:
        success, message = test_import(module_name, package_name, description)
        print(message)
        
        if success:
            success_count += 1
        else:
            failed_count += 1
            failed_modules.append((module_name, description))
    
    # عرض النتائج
    print("\n" + "=" * 80)
    print("📊 ملخص النتائج:")
    print("=" * 80)
    print(f"✅ مكتبات متاحة: {success_count}")
    print(f"❌ مكتبات مفقودة: {failed_count}")
    print(f"📈 معدل النجاح: {(success_count/(success_count+failed_count)*100):.1f}%")
    
    if failed_modules:
        print(f"\n❌ المكتبات المفقودة ({len(failed_modules)}):")
        print("-" * 40)
        for module_name, description in failed_modules:
            print(f"  • {description} ({module_name})")
        
        print(f"\n💡 لتثبيت المكتبات المفقودة:")
        print("   python تثبيت_المكتبات.py")
        print("   أو")
        print("   pip install -r requirements.txt")
    
    # فحص إضافي للمكتبات الحرجة
    print(f"\n🔍 فحص المكتبات الحرجة:")
    print("-" * 40)
    
    critical_tests = [
        ("django.core.management", "Django Management Commands"),
        ("django.db", "Django Database"),
        ("django.http", "Django HTTP"),
        ("django.shortcuts", "Django Shortcuts"),
        ("django.contrib.auth.decorators", "Django Auth Decorators"),
        ("openpyxl.Workbook", "Excel Workbook Creation"),
        ("reportlab.platypus", "PDF Document Creation"),
        ("PIL.Image", "Image Processing"),
        ("pandas.DataFrame", "Data Analysis"),
    ]
    
    critical_success = 0
    for module_name, description in critical_tests:
        success, message = test_import(module_name, None, description)
        print(message)
        if success:
            critical_success += 1
    
    critical_percentage = (critical_success / len(critical_tests)) * 100
    
    print(f"\n🎯 المكتبات الحرجة: {critical_success}/{len(critical_tests)} ({critical_percentage:.1f}%)")
    
    # التوصيات
    print(f"\n💡 التوصيات:")
    print("-" * 20)
    
    if failed_count == 0:
        print("✅ جميع المكتبات متاحة - النظام جاهز للتشغيل!")
        print("🚀 يمكنك تشغيل النظام باستخدام: python تشغيل_النظام.py")
    elif failed_count <= 5:
        print("⚠️  بعض المكتبات مفقودة - يُنصح بتثبيتها")
        print("📦 قم بتشغيل: python تثبيت_المكتبات.py")
    else:
        print("❌ العديد من المكتبات مفقودة - يجب تثبيتها قبل التشغيل")
        print("📦 قم بتشغيل: python تثبيت_المكتبات.py")
        print("📦 أو: pip install -r requirements.txt")
    
    if critical_percentage < 80:
        print("⚠️  بعض المكتبات الحرجة مفقودة - قد يواجه النظام مشاكل")
    
    return failed_count == 0

def main():
    """الدالة الرئيسية"""
    success = check_all_dependencies()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 تم التحقق بنجاح من جميع المكتبات!")
    else:
        print("⚠️  يرجى تثبيت المكتبات المفقودة قبل تشغيل النظام")
    print("=" * 80)
    
    # إبقاء النافذة مفتوحة في Windows
    try:
        input("\n📱 اضغط Enter للخروج...")
    except KeyboardInterrupt:
        pass

if __name__ == "__main__":
    main()
