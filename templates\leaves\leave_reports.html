{% extends 'base.html' %}
{% load static %}

{% block title %}تقارير الإجازات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>


    /* Table styles with clear borders - matching directorate employees report */
    .table-bordered {
        border: 2px solid #dee2e6 !important;
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid #000 !important;
        border-width: 1px !important;
        text-align: center;
    }

    .table-bordered thead th {
        border-bottom: 2px solid #000 !important;
    }

    /* Make text in cells bold for better visibility */
    .table-bordered td {
        font-weight: bold;
    }

    /* Make numbers larger and more visible */
    .table-bordered td:nth-child(n+2) {
        font-size: 16px;
    }

    /* Add background colors for the table cells */
    .bg-success.bg-opacity-25 {
        background-color: #d4edda !important;
    }

    .bg-danger.bg-opacity-25 {
        background-color: #f8d7da !important;
    }

    /* Purple color for departures */
    .bg-purple {
        background-color: #6f42c1 !important;
    }

    .bg-purple.bg-opacity-25 {
        background-color: rgba(111, 66, 193, 0.25) !important;
        border-left: 3px solid #6f42c1;
    }

    /* Add subtle animation for departure cells */
    .bg-purple.bg-opacity-25:hover {
        background-color: rgba(111, 66, 193, 0.35) !important;
        transition: background-color 0.3s ease;
    }

    .bg-info.bg-opacity-25 {
        background-color: #d1ecf1 !important;
    }

    /* Dropdown menu animation */
    .animated--fade-in {
        animation-name: fadeIn;
        animation-duration: 0.3s;
        animation-timing-function: ease-in-out;
    }

    @keyframes fadeIn {
        0% {
            opacity: 0;
            transform: translateY(10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Print styles */
    @media print {
        .no-print {
            display: none !important;
        }

        .card {
            break-inside: avoid;
            box-shadow: none !important;
            border: 1px solid #ddd;
        }


    }

    /* Search box styling */
    #employeeSearch {
        transition: all 0.3s ease;
        background-color: rgba(255, 255, 255, 0.9);
    }

    #employeeSearch:focus {
        border-color: #fff;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        background-color: #fff;
    }

    .input-group-text.bg-white {
        background-color: rgba(255, 255, 255, 0.9) !important;
    }

    #searchButton, #clearSearch {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        transition: all 0.3s ease;
    }

    #searchButton:hover, #clearSearch:hover {
        background-color: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: translateY(-1px);
    }

    #searchButton:active, #clearSearch:active {
        transform: translateY(0);
    }

    /* تحسين مظهر البحث */
    #searchInputMain {
        border-left: none;
        border-right: none;
        transition: all 0.3s ease;
    }

    #searchInputMain:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        z-index: 3;
        position: relative;
    }

    #searchButton {
        border-left: none;
        transition: all 0.3s ease;
        z-index: 2;
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    #searchButton:hover {
        background-color: #0056b3;
        color: white;
        border-color: #0056b3;
    }

    #searchButton:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .input-group-text {
        background-color: #f8f9fa;
        border-color: #ced4da;
        border-right: none;
    }

    /* تحسين مظهر زر مسح البحث */
    #clearSearchBtn {
        border-left: none;
        background-color: #6c757d;
        color: white;
        border-color: #6c757d;
    }

    #clearSearchBtn:hover {
        background-color: #545b62;
        color: white;
        border-color: #545b62;
    }

    /* تأثير البحث النشط */
    .search-active #searchInputMain {
        background-color: #fff3cd;
        border-color: #ffc107;
    }

    .search-active .input-group-text {
        background-color: #ffc107;
        color: #212529;
        border-color: #ffc107;
    }

    /* Search info styling improvements */
    #searchInfo {
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    /* Search info styling */
    #searchInfo {
        margin-bottom: 1rem;
        border-left: 4px solid #36b9cc;
        background-color: #d1ecf1;
        border-color: #bee5eb;
        color: #0c5460;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-line text-primary"></i> تقارير الإجازات والمغادرات</h2>
    <div>
        <a href="{% url 'leaves:leave_statistics' %}" class="btn btn-info me-2">
            <i class="fas fa-chart-bar"></i> الإحصائيات
        </a>
        <a href="{% url 'reports:directorate_employees_report' %}" class="btn btn-primary me-2">
            <i class="fas fa-users"></i> تقرير موظفي المديرية
        </a>
        <a href="{% url 'leaves:leave_list' %}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right"></i> العودة للإجازات
        </a>
        <button class="btn btn-primary shadow-sm btn-print">
            <i class="fas fa-print me-1"></i> طباعة التقرير
        </button>
    </div>
</div>

<!-- بطاقات إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي الموظفين
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ employee_balances|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            موظفين لديهم مغادرات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ employees_with_departures }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-plane-departure fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            السنة الحالية
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ current_year }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            أنواع الإجازات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ leave_types|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-list fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <form method="GET" class="d-inline-block" id="searchForm">
                    <div class="input-group" style="width: 350px;">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" id="searchInputMain" name="search" class="form-control form-control-sm"
                               placeholder="ابحث بالاسم، الرقم الوزاري..." value="" autocomplete="off">
                        <button type="submit" class="btn btn-primary btn-sm" id="searchButton">
                            <i class="fas fa-search" id="searchIcon"></i>
                            <i class="fas fa-spinner fa-spin d-none" id="searchSpinner"></i>
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" id="clearSearchBtn" title="مسح البحث">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </form>
            </div>
            <div class="text-end">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-chart-bar"></i> أرصدة الإجازات للموظفين - {{ current_year }}
                </h6>
                <small class="text-white">
                    <i class="fas fa-info-circle"></i>
                    يتم طرح المغادرات من رصيد الإجازات السنوية (كل 420 دقيقة = يوم واحد)
                </small>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="dataTable">
                <thead class="thead-light">
                    <tr>
                        <th rowspan="2">الرقم الوزاري</th>
                        <th rowspan="2">الموظف</th>
                        {% for leave_type in leave_types %}
                            {% if leave_type.name == 'annual' %}
                                <th colspan="4" class="text-center">{{ leave_type.get_name_display }}</th>
                            {% else %}
                                <th colspan="3" class="text-center">{{ leave_type.get_name_display }}</th>
                            {% endif %}
                        {% endfor %}
                    </tr>
                    <tr>
                        {% for leave_type in leave_types %}
                            <th class="bg-success text-white">الرصيد</th>
                            <th class="bg-danger text-white">المستخدم</th>
                            {% if leave_type.name == 'annual' %}
                                <th class="bg-purple text-white">المغادرات الخاصة</th>
                            {% endif %}
                            <th class="bg-info text-white">المتبقي</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for item in employee_balances %}
                    <tr class="text-center">
                        <td>
                            <span class="badge bg-secondary">{{ item.employee.ministry_number }}</span>
                        </td>
                        <td>
                            <a href="{% url 'employees:employee_detail' item.employee.pk %}">
                                {{ item.employee.full_name }}
                            </a>
                        </td>
                        {% for balance in item.type_balances %}
                            <td class="bg-success bg-opacity-25 fw-bold fs-5">{{ balance.initial }}</td>
                            <td class="bg-danger bg-opacity-25 fw-bold fs-5">{{ balance.used }}</td>
                            {% if balance.is_annual %}
                                <td class="bg-purple bg-opacity-25 fw-bold fs-5" title="مجموع المغادرات بالأيام">
                                    {{ balance.departures_used|floatformat:2 }}
                                </td>
                            {% endif %}
                            <!-- عرض المتبقي حسب نوع الإجازة -->
                            {% if balance.is_decimal %}
                                <!-- للإجازات السنوية: عرض الرقم العشري -->
                                <td class="bg-info bg-opacity-25 fw-bold fs-5">{{ balance.remaining|floatformat:2 }}</td>
                            {% else %}
                                <!-- للإجازات الأخرى: عرض رقم صحيح -->
                                <td class="bg-info bg-opacity-25 fw-bold fs-5">{{ balance.remaining|floatformat:0 }}</td>
                            {% endif %}
                        {% endfor %}
                    </tr>
                    {% empty %}
                    <tr class="text-center">
                        <td colspan="25">لا يوجد بيانات</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<!-- Simple Search without DataTables -->
<script>
    $(document).ready(function() {
        console.log('Initializing simple search...');

        // Initialize search active state
        if ($('#searchInputMain').val().trim()) {
            $('#searchInputMain').closest('form').addClass('search-active');
        }

        // Search function
        function performSearch() {
            console.log('performSearch called');
            const searchValue = $('#searchInputMain').val().toLowerCase().trim();
            console.log('Search value:', searchValue);

            // Show loading spinner
            $('#searchIcon').addClass('d-none');
            $('#searchSpinner').removeClass('d-none');

            // Small delay to show spinner
            setTimeout(function() {
                // Manual search in table rows
                let visibleCount = 0;
                const totalRows = $('#dataTable tbody tr').length;
                console.log('Total rows found:', totalRows);

                if (totalRows === 0) {
                    console.error('No table rows found! Check table structure.');
                    // Hide spinner
                    $('#searchSpinner').addClass('d-none');
                    $('#searchIcon').removeClass('d-none');
                    return;
                }

            $('#dataTable tbody tr').each(function(index) {
                const $row = $(this);
                const rowText = $row.text().toLowerCase().replace(/\s+/g, ' ').trim();

                // Search in specific columns (ministry number and employee name)
                const ministryNumber = $row.find('td:eq(0)').text().toLowerCase().trim();
                const employeeName = $row.find('td:eq(1)').text().toLowerCase().trim();

                console.log('Row', index, '- Ministry:', ministryNumber, 'Name:', employeeName);

                if (searchValue === '' ||
                    rowText.includes(searchValue) ||
                    ministryNumber.includes(searchValue) ||
                    employeeName.includes(searchValue)) {
                    $row.show();
                    visibleCount++;
                    console.log('Row', index, 'shown');
                } else {
                    $row.hide();
                    console.log('Row', index, 'hidden');
                }
            });

                console.log('Search completed. Visible rows:', visibleCount, 'Total rows:', totalRows);

                // Hide loading spinner
                $('#searchSpinner').addClass('d-none');
                $('#searchIcon').removeClass('d-none');

                // Show search results
                showSearchResults(searchValue, visibleCount, totalRows);
            }, 100);
        }

        // Show search results
        function showSearchResults(searchValue, visibleCount, totalRows) {
            var searchInfo = $('#searchInfo');

            if (searchInfo.length === 0) {
                searchInfo = $('<div id="searchInfo" class="alert mt-2 mb-3"></div>');
                $('.table-responsive').before(searchInfo);
            }

            if (searchValue.trim() === '') {
                searchInfo.hide();
                return;
            }

            if (visibleCount === 0) {
                searchInfo.removeClass().addClass('alert alert-warning mt-2 mb-3');
                searchInfo.html(`
                    <div class="d-flex justify-content-between align-items-center">
                        <span>
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                            لا توجد نتائج للبحث عن: "<strong class="text-primary">${searchValue}</strong>"
                        </span>
                        <span class="badge bg-warning text-dark">0 من ${totalRows}</span>
                    </div>
                `);
            } else {
                searchInfo.removeClass().addClass('alert alert-success mt-2 mb-3');
                var resultText = visibleCount === 1 ? 'موظف واحد' : `${visibleCount} موظف`;
                searchInfo.html(`
                    <div class="d-flex justify-content-between align-items-center">
                        <span>
                            <i class="fas fa-check-circle text-success"></i>
                            نتائج البحث عن: "<strong class="text-primary">${searchValue}</strong>"
                        </span>
                        <span class="badge bg-success">${resultText} من ${totalRows}</span>
                    </div>
                `);
            }
            searchInfo.show();
        }

        // Clear search function
        function clearSearch() {
            console.log('Clear search called');

            // Clear input
            $('#searchInputMain').val('');

            // Remove search active state
            $('#searchInputMain').closest('form').removeClass('search-active');

            // Show all rows
            $('#dataTable tbody tr').show();
            console.log('All rows shown');

            // Hide search info
            $('#searchInfo').hide();
        }

        // Search functionality with delay
        let searchTimeout;
        $('#searchInputMain').on('keyup input', function(e) {
            console.log('Search input event triggered:', e.type, 'Value:', $(this).val());
            clearTimeout(searchTimeout);
            const searchValue = $(this).val();

            // Update search active state
            const $form = $('#searchInputMain').closest('form');
            if (searchValue.trim()) {
                $form.addClass('search-active');
            } else {
                $form.removeClass('search-active');
            }

            // If Enter key is pressed, search immediately
            if (e.key === 'Enter' || e.keyCode === 13) {
                e.preventDefault();
                clearTimeout(searchTimeout);
                console.log('Enter pressed, searching immediately');
                performSearch();
                return false;
            }

            // Delay search to avoid too many requests
            searchTimeout = setTimeout(function() {
                console.log('Timeout triggered, performing search');
                performSearch();
            }, 300);
        });

        // Additional Enter key handler
        $('#searchInputMain').on('keypress', function(e) {
            if (e.which === 13 || e.keyCode === 13) {
                e.preventDefault();
                console.log('Enter key detected in keypress');
                clearTimeout(searchTimeout);
                performSearch();
                return false;
            }
        });

        // Search form submit handler
        $('#searchForm').on('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted, performing search');
            performSearch();
            return false;
        });

        // Search button click handler
        $('#searchButton').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Search button clicked');
            console.log('Search input value:', $('#searchInputMain').val());
            performSearch();
            return false;
        });

        // Clear search button
        $('#clearSearchBtn').on('click', function(e) {
            e.preventDefault();
            console.log('Clear button clicked');
            clearSearch();
            return false;
        });

        console.log('Simple search functionality initialized');

        // Test the search functionality
        setTimeout(function() {
            console.log('Testing search setup...');
            console.log('Table exists:', $('#dataTable').length > 0);
            console.log('Table rows:', $('#dataTable tbody tr').length);
            console.log('Search input exists:', $('#searchInputMain').length > 0);
            console.log('Search button exists:', $('#searchButton').length > 0);
        }, 1000);
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Print button functionality
        const printButtons = document.querySelectorAll('.btn-print');
        printButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    });
</script>
{% endblock %}
