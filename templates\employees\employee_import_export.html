{% extends 'base.html' %}
{% load static %}

{% block title %}استيراد وتصدير بيانات الموظفين - نظام شؤون الموظفين{% endblock %}

{% block styles %}
<style>
    /* تنسيقات عامة */
    .content-wrapper, .container-fluid {
        padding: 0 !important;
        margin: 0 !important;
        max-width: 100% !important;
        width: 100% !important;
    }

    .page-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .section-title {
        color: #4e73df;
        font-weight: bold;
        border-bottom: 2px solid #4e73df;
        padding-bottom: 0.5rem;
        display: inline-block;
        margin-bottom: 1.5rem;
    }

    .card {
        border: none;
        border-radius: 0.5rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
        margin-bottom: 2rem;
    }

    .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        padding: 1rem 1.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    /* تنسيقات قسم الاستيراد */
    .import-steps {
        counter-reset: step-counter;
        margin-bottom: 2rem;
    }

    .import-step {
        position: relative;
        padding-right: 3rem;
        margin-bottom: 1.5rem;
        min-height: 2.5rem;
    }

    .import-step:before {
        content: counter(step-counter);
        counter-increment: step-counter;
        position: absolute;
        right: 0;
        top: 0;
        width: 2.5rem;
        height: 2.5rem;
        background-color: #4e73df;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .import-form {
        background-color: #f8f9fc;
        border: 2px solid #d1d3e2;
        border-radius: 0.5rem;
        padding: 1.5rem;
        transition: all 0.3s;
    }

    .import-form:hover {
        border-color: #4e73df;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(78, 115, 223, 0.1);
    }

    .template-preview {
        border: 1px solid #e3e6f0;
        border-radius: 0.5rem;
        overflow-x: auto;
    }

    /* تنسيقات قسم التصدير */
    .export-options {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .export-option {
        flex: 1;
        min-width: 250px;
        background-color: #fff;
        border-radius: 0.5rem;
        border: 2px solid #e3e6f0;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 300px;
    }

    .export-option:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .export-option.excel {
        border-color: #1cc88a;
    }

    .export-option.excel:hover {
        background-color: #f0fff8;
    }

    .export-option.pdf {
        border-color: #e74a3b;
    }

    .export-option.pdf:hover {
        background-color: #fff5f5;
    }

    .export-option i {
        font-size: 4rem;
        margin-bottom: 1rem;
        display: block;
    }

    .export-option h5 {
        margin-bottom: 0.75rem;
        font-weight: bold;
    }

    .export-option p {
        margin-bottom: 1.5rem;
        line-height: 1.4;
    }

    .export-option.excel i {
        color: #1cc88a;
    }

    .export-option.pdf i {
        color: #e74a3b;
    }

    /* تنسيقات الجداول */
    .template-preview {
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
    }

    /* تنسيق جديد لحاوية الجدول */
    .table-container {
        width: 100%;
        border: 1px solid #e3e6f0;
        border-radius: 0.25rem;
        position: relative;
        margin-bottom: 1rem;
        overflow: hidden; /* لمنع التدفق خارج الحدود */
        box-shadow: 0 0.15rem 0.5rem rgba(78, 115, 223, 0.1);
    }

    .table-scroll {
        width: 100%;
        overflow-x: auto; /* تمرير أفقي فقط */
        display: block;
        -webkit-overflow-scrolling: touch;
        position: relative;
    }

    /* تنسيق شريط التمرير الأفقي */
    .table-scroll::-webkit-scrollbar {
        height: 16px; /* ارتفاع أكبر لشريط التمرير الأفقي */
        width: 0; /* إلغاء شريط التمرير العمودي */
    }

    .table-scroll::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 8px;
        border: 1px solid #e3e6f0;
    }

    .table-scroll::-webkit-scrollbar-thumb {
        background: #4e73df;
        border-radius: 8px;
        border: 3px solid #f1f1f1;
        min-width: 50px; /* الحد الأدنى لعرض المقبض */
    }

    .table-scroll::-webkit-scrollbar-thumb:hover {
        background: #3a5ccc;
    }

    /* إضافة أسهم لشريط التمرير */
    .table-scroll::-webkit-scrollbar-button:horizontal:start:decrement,
    .table-scroll::-webkit-scrollbar-button:horizontal:end:increment {
        display: block;
        width: 16px;
        height: 16px;
        background-color: #f1f1f1;
        background-repeat: no-repeat;
        background-position: center;
        border-radius: 8px;
    }

    .table-scroll::-webkit-scrollbar-button:horizontal:start:decrement {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%234e73df' viewBox='0 0 16 16'%3E%3Cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3E%3C/svg%3E");
    }

    .table-scroll::-webkit-scrollbar-button:horizontal:end:increment {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%234e73df' viewBox='0 0 16 16'%3E%3Cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
    }

    /* تنسيق للجدول داخل الحاوية */
    .table-scroll table {
        width: 100%;
        min-width: 1500px; /* عرض أدنى أكبر للجدول لضمان ظهور شريط التمرير الأفقي */
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    /* تنسيق رأس الجدول */
    .table-scroll thead th {
        background-color: #4e73df;
        color: white;
        font-weight: bold;
        padding: 12px 15px;
        text-align: center;
        border: 1px solid #3a5ccc;
    }

    /* تنسيق خلايا الجدول */
    .table-scroll tbody td {
        padding: 10px 15px;
        border: 1px solid #e3e6f0;
        text-align: center;
    }

    /* تنسيق صفوف الجدول المتناوبة */
    .table-scroll tbody tr:nth-child(even) {
        background-color: #f8f9fc;
    }

    .table-scroll tbody tr:hover {
        background-color: #eaecf4;
    }

    /* تنسيق تعليمات التمرير */
    .scroll-instruction {
        display: flex;
        align-items: center;
        background-color: #4e73df;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 0.85rem;
        animation: pulse 2s infinite;
    }

    .scroll-instruction i {
        color: white;
    }

    .me-1 {
        margin-right: 0.25rem !important;
    }

    .ms-1 {
        margin-left: 0.25rem !important;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }



    .table {
        margin-bottom: 0;
        width: 100%;
        color: #333;
        border-collapse: collapse;
    }

    .table-bordered {
        border: 1px solid #e3e6f0;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.075);
    }

    .thead-dark th {
        color: #fff;
        background-color: #4e73df;
        border-color: #4e73df;
    }

    .table th, .table td {
        padding: 0.75rem;
        vertical-align: middle;
        border: 1px solid #e3e6f0;
    }

    .text-center {
        text-align: center !important;
    }

    /* تنسيقات الأعمدة الاختيارية */
    .column-optional {
        background-color: rgba(0, 0, 0, 0.02);
    }

    /* تنسيقات أزرار التبديل */
    .toggle-table-view {
        margin-right: 0.25rem;
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .toggle-table-view.active {
        background-color: #4e73df;
        color: white;
        border-color: #4e73df;
    }

    .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }

    .card-footer {
        background-color: #f8f9fc;
        border-top: 1px solid #e3e6f0;
    }

    .bg-light {
        background-color: #f8f9fc !important;
    }

    .text-muted {
        color: #6c757d !important;
    }

    .py-2 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }

    .px-3 {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    /* تنسيقات التنبيهات */
    .alert {
        position: relative;
        padding: 1rem 1.25rem;
        margin-bottom: 1.5rem;
        border: 1px solid transparent;
        border-radius: 0.5rem;
    }

    .alert-info {
        background-color: #e8f4fd;
        border-color: #b8daff;
        color: #0c5460;
    }

    .alert-warning {
        background-color: #fff3cd;
        border-color: #ffeeba;
        color: #856404;
    }

    .me-2 {
        margin-right: 0.5rem !important;
    }

    .me-3 {
        margin-right: 1rem !important;
    }

    .d-flex {
        display: flex !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .fa-lg {
        font-size: 1.33333em;
        line-height: 0.75em;
        vertical-align: -0.0667em;
    }

    .fa-2x {
        font-size: 2em;
    }

    .alert-icon {
        display: flex;
        align-items: center;
        color: inherit;
    }

    /* تنسيقات الإحصائيات */
    .stats-card {
        background-color: #f8f9fc;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .stats-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e3e6f0;
    }

    .stats-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .stats-value {
        font-weight: bold;
        color: #4e73df;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- رأس الصفحة -->
    <div class="page-header d-flex justify-content-between align-items-center">
        <h3 class="font-weight-bold text-primary mb-0">استيراد وتصدير بيانات الموظفين</h3>
        <div>
            <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right mr-2"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- قسم الاستيراد -->
        <div class="col-lg-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="m-0 font-weight-bold text-primary">استيراد بيانات الموظفين</h5>
                    <div class="btn-group">
                        <a href="{% url 'employees:employee_import_export' %}?download_template=true" class="btn btn-secondary">
                            <i class="fas fa-download mr-2"></i> قالب أساسي
                        </a>
                        <a href="{% url 'employees:employee_import_export' %}?download_template=enhanced" class="btn btn-secondary">
                            <i class="fas fa-download mr-2"></i> قالب شامل
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <h6 class="font-weight-bold mb-3">خطوات الاستيراد:</h6>
                            <div class="import-steps">
                                <div class="import-step">
                                    <h6 class="font-weight-bold">تحميل قالب الاستيراد</h6>
                                    <p>قم بتحميل قالب ملف Excel الجاهز من خلال الضغط على زر "تحميل قالب الاستيراد".</p>
                                </div>
                                <div class="import-step">
                                    <h6 class="font-weight-bold">تعبئة البيانات</h6>
                                    <p>قم بتعبئة بيانات الموظفين في القالب مع الالتزام بالأعمدة المحددة.</p>
                                </div>
                                <div class="import-step">
                                    <h6 class="font-weight-bold">رفع الملف</h6>
                                    <p>قم برفع الملف بعد تعبئته من خلال النموذج أدناه.</p>
                                </div>
                            </div>

                            <div class="alert alert-info d-flex alert-permanent">
                                <div class="alert-icon me-3">
                                    <i class="fas fa-info-circle fa-2x"></i>
                                </div>
                                <div class="alert-content">
                                    <h6 class="alert-heading font-weight-bold">ملاحظات هامة:</h6>
                                    <ul class="mb-0">
                                        <li>تأكد من تعبئة جميع الحقول الإلزامية (الرقم الوزاري، الاسم الكامل، القسم، تاريخ التعيين، الجنس).</li>
                                        <li>تأكد من صحة تنسيق التواريخ (YYYY-MM-DD).</li>
                                        <li>تأكد من أن قيمة الجنس هي "ذكر" أو "انثى" فقط.</li>
                                        <li>سيتم تجاهل الموظفين الموجودين مسبقاً (بنفس الرقم الوزاري).</li>
                                    </ul>
                                </div>
                            </div>

                            <form method="post" enctype="multipart/form-data" class="import-form">
                                {% csrf_token %}
                                <div class="row align-items-end">
                                    <div class="col-md-8">
                                        <label for="excel_file" class="form-label font-weight-bold">اختر ملف Excel</label>
                                        <input class="form-control form-control-lg" type="file" id="excel_file" name="excel_file" accept=".xlsx, .xls" required>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" class="btn btn-primary btn-lg w-100">
                                            <i class="fas fa-file-import mr-2"></i> استيراد البيانات
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <div class="col-lg-6">
                            <h6 class="font-weight-bold mb-3">نموذج قالب الاستيراد:</h6>
                            <div class="template-preview">
                                <div class="card-header bg-light py-2 px-3 mb-0">
                                    <span class="font-weight-bold">نموذج قالب الاستيراد</span>
                                </div>
                                <div class="table-container" id="template-table-container">
                                    <div class="table-scroll" id="template-table-scroll">
                                        <table id="template-table">
                                        <thead class="thead-dark">
                                            <tr>
                                                <th class="text-center">الرقم الوزاري</th>
                                                <th class="text-center">الرقم الوطني</th>
                                                <th class="text-center">الاسم الكامل</th>
                                                <th class="text-center">المؤهل العلمي</th>
                                                <th class="text-center">التخصص</th>
                                                <th class="text-center">تاريخ التعيين</th>
                                                <th class="text-center">القسم</th>
                                                <th class="text-center">تاريخ الميلاد</th>
                                                <th class="text-center">العنوان</th>
                                                <th class="text-center">رقم الهاتف</th>
                                                <th class="text-center">الجنس</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="text-center">123456</td>
                                                <td class="text-center">9801234567</td>
                                                <td class="text-center">اسم موظف نموذجي</td>
                                                <td class="text-center">بكالوريوس</td>
                                                <td class="text-center">علوم حاسوب</td>
                                                <td class="text-center">2020-01-01</td>
                                                <td class="text-center">قسم تكنولوجيا المعلومات</td>
                                                <td class="text-center">1990-05-10</td>
                                                <td class="text-center">عمان - الأردن</td>
                                                <td class="text-center">0777123456</td>
                                                <td class="text-center">ذكر</td>
                                            </tr>
                                            <tr>
                                                <td class="text-center">789012</td>
                                                <td class="text-center">9876543210</td>
                                                <td class="text-center">اسم موظف نموذجي آخر</td>
                                                <td class="text-center">ماجستير</td>
                                                <td class="text-center">إدارة أعمال</td>
                                                <td class="text-center">2019-05-15</td>
                                                <td class="text-center">قسم الإدارة</td>
                                                <td class="text-center">1985-12-20</td>
                                                <td class="text-center">إربد - الأردن</td>
                                                <td class="text-center">0799876543</td>
                                                <td class="text-center">انثى</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="card-footer bg-light py-2 px-3 text-center">
                                    <div class="scroll-instruction">
                                        <i class="fas fa-long-arrow-alt-left me-1"></i>
                                        <span>مرر لليمين لرؤية كامل الحقول</span>
                                        <i class="fas fa-long-arrow-alt-right ms-1"></i>
                                    </div>
                                </div>
                                <div class="alert alert-warning mt-3 d-flex align-items-center">
                                    <i class="fas fa-info-circle me-2 fa-lg"></i>
                                    <div>
                                        <strong>ملاحظة هامة:</strong> يجب الالتزام بنفس ترتيب الأعمدة والتنسيق الموضح أعلاه لضمان نجاح عملية الاستيراد.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم التصدير -->
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold text-primary">تصدير بيانات الموظفين</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <h6 class="font-weight-bold mb-3">خيارات التصدير:</h6>
                            
                            <!-- التصدير الأساسي -->
                            <div class="mb-4">
                                <h6 class="text-info mb-3">📊 التصدير الأساسي (الحقول الأساسية فقط)</h6>
                                <div class="export-options">
                                    <div class="export-option excel">
                                        <i class="fas fa-file-excel"></i>
                                        <h5>Excel أساسي</h5>
                                        <p>الحقول الأساسية للموظفين</p>
                                        <a href="{% url 'employees:employee_import_export' %}?export=excel" class="btn btn-success">
                                            <i class="fas fa-download mr-2"></i> تنزيل Excel
                                        </a>
                                    </div>

                                    <div class="export-option pdf">
                                        <i class="fas fa-file-pdf"></i>
                                        <h5>PDF أساسي</h5>
                                        <p>تقرير أساسي بصيغة PDF</p>
                                        <a href="{% url 'employees:employee_import_export' %}?export=pdf" class="btn btn-danger">
                                            <i class="fas fa-download mr-2"></i> تنزيل PDF
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- التصدير الشامل -->
                            <div class="mb-4">
                                <h6 class="text-success mb-3">🔥 التصدير الشامل (جميع الحقول والبيانات المرتبطة)</h6>

                                <!-- إطار النص في المنتصف -->
                                <div class="text-center mb-4">
                                    <div class="alert alert-success d-inline-block" style="max-width: 600px;">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <i class="fas fa-info-circle me-3 fa-lg"></i>
                                            <div>
                                                <strong>يشمل التصدير الشامل:</strong><br>
                                                <small>المؤهلات العلمية، بيانات التوظيف، حالة الموظف، النقل، إجازة الأمومة، وجميع البيانات المرتبطة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="export-options">
                                    <div class="export-option excel" style="border: 2px solid #28a745; display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center; min-height: 350px;">
                                        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                            <i class="fas fa-file-excel" style="font-size: 4rem; margin-bottom: 1rem; color: #1cc88a;"></i>
                                            <h5 style="margin-bottom: 0.75rem; font-weight: bold;">Excel شامل</h5>
                                            <p style="margin-bottom: 1.5rem; line-height: 1.4; text-align: center;">جميع الحقول + المؤهلات + التوظيف + الحالة</p>
                                            <a href="{% url 'employees:employee_import_export' %}?export=excel&include_all=true" class="btn btn-success btn-lg">
                                                <i class="fas fa-download mr-2"></i> تنزيل Excel شامل
                                            </a>
                                        </div>
                                    </div>

                                    <div class="export-option pdf" style="border: 2px solid #dc3545; display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center; min-height: 350px;">
                                        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                            <i class="fas fa-file-pdf" style="font-size: 4rem; margin-bottom: 1rem; color: #e74a3b;"></i>
                                            <h5 style="margin-bottom: 0.75rem; font-weight: bold;">PDF شامل</h5>
                                            <p style="margin-bottom: 1.5rem; line-height: 1.4; text-align: center;">تقرير شامل بجميع البيانات</p>
                                            <a href="{% url 'employees:employee_import_export' %}?export=pdf&include_all=true" class="btn btn-danger btn-lg">
                                                <i class="fas fa-download mr-2"></i> تنزيل PDF شامل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- خيارات إضافية -->
                            <div class="mb-4">
                                <h6 class="text-warning mb-3">⚙️ خيارات إضافية</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card border-warning">
                                            <div class="card-body text-center">
                                                <i class="fas fa-file-download fa-2x text-warning mb-2"></i>
                                                <h6>قالب الاستيراد الأساسي</h6>
                                                <p class="small">تحميل قالب Excel أساسي للاستيراد (الحقول الأساسية فقط)</p>
                                                <a href="{% url 'employees:employee_import_export' %}?download_template=true" class="btn btn-warning">
                                                    <i class="fas fa-download mr-2"></i> تحميل القالب الأساسي
                                                </a>
                                                <br><br>
                                                <a href="{% url 'employees:employee_import_export' %}?download_template=enhanced" class="btn btn-outline-warning btn-sm">
                                                    <i class="fas fa-download mr-2"></i> القالب الشامل
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-info">
                                            <div class="card-body text-center">
                                                <i class="fas fa-users fa-2x text-info mb-2"></i>
                                                <h6>الموظفين النشطين فقط</h6>
                                                <p class="small">تصدير الموظفين النشطين (بدون متقاعدين)</p>
                                                <a href="{% url 'employees:employee_import_export' %}?export=excel&active_only=true" class="btn btn-info">
                                                    <i class="fas fa-download mr-2"></i> النشطين فقط
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h6 class="font-weight-bold mb-3">ميزات التصدير المحسنة:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-success">✅ الميزات الأساسية:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success mr-2"></i>تصدير جميع بيانات الموظفين دفعة واحدة</li>
                                        <li><i class="fas fa-check text-success mr-2"></i>دعم كامل للغة العربية</li>
                                        <li><i class="fas fa-check text-success mr-2"></i>تنسيق ملائم للطباعة والمشاركة</li>
                                        <li><i class="fas fa-check text-success mr-2"></i>ترتيب البيانات من اليمين إلى اليسار</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">🔥 الميزات الجديدة:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-star text-warning mr-2"></i>جميع المؤهلات العلمية (دبلوم، ماجستير، دكتوراه)</li>
                                        <li><i class="fas fa-star text-warning mr-2"></i>بيانات التوظيف والمناصب</li>
                                        <li><i class="fas fa-star text-warning mr-2"></i>حالة الموظف (نشط، متقاعد، منقول)</li>
                                        <li><i class="fas fa-star text-warning mr-2"></i>إجازات الأمومة والنقل الداخلي</li>
                                        <li><i class="fas fa-star text-warning mr-2"></i>تواريخ النظام والتحديثات</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="stats-card">
                                <h6 class="font-weight-bold mb-3">إحصائيات الموظفين:</h6>
                                <div class="stats-item">
                                    <span>إجمالي عدد الموظفين:</span>
                                    <span class="stats-value">{{ employees_count|default:"0" }}</span>
                                </div>
                                <div class="stats-item">
                                    <span>عدد الموظفين الذكور:</span>
                                    <span class="stats-value">{{ male_count|default:"0" }}</span>
                                </div>
                                <div class="stats-item">
                                    <span>عدد الموظفات الإناث:</span>
                                    <span class="stats-value">{{ female_count|default:"0" }}</span>
                                </div>
                                <div class="stats-item">
                                    <span>عدد الأقسام:</span>
                                    <span class="stats-value">{{ departments_count|default:"0" }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحسين تجربة المستخدم عند رفع الملف
        const fileInput = document.getElementById('excel_file');
        if (fileInput) {
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    const fileName = e.target.files[0].name;
                    const fileLabel = document.querySelector('label[for="excel_file"]');
                    if (fileLabel) {
                        fileLabel.textContent = 'الملف المختار: ' + fileName;
                    }
                }
            });
        }

        // إضافة مستمع لتغيير حجم النافذة
        window.addEventListener('resize', function() {
            // لا نحتاج لأي إجراء خاص بعد إزالة العرض المختصر والكامل
        });

        // تهيئة الجدول للتمرير
        const tableContainer = document.getElementById('template-table-container');
        const tableScroll = document.getElementById('template-table-scroll');
        const table = document.getElementById('template-table');

        // إضافة مستمع لتغيير حجم النافذة لإعادة تهيئة الجدول
        window.addEventListener('resize', function() {
            // إذا كانت الشاشة صغيرة، استخدم العرض المختصر
            if (window.innerWidth < 992) {
                const compactButton = document.querySelector('.toggle-table-view[data-view="compact"]');
                if (compactButton && !compactButton.classList.contains('active')) {
                    compactButton.click();
                }
            }
        });

        // إضافة مستمع للنقر على الجدول للتركيز عليه
        if (tableScroll) {
            tableScroll.addEventListener('click', function() {
                this.focus();
            });
        }
    });
</script>
{% endblock %}
