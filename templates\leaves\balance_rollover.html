{% extends 'base.html' %}
{% load static %}

{% block title %}تدوير رصيد الإجازات السنوية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin: -1.5rem -1.5rem 3rem -1.5rem;
        border-radius: 0 0 30px 30px;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-header .container-fluid {
        position: relative;
        z-index: 1;
    }

    .info-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        border-left: 5px solid #667eea;
    }

    .table-container {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    }

    .table-enhanced {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .table-enhanced thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
        padding: 1rem;
    }

    .table-enhanced tbody tr {
        transition: all 0.3s ease;
    }

    .table-enhanced tbody tr:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        transform: scale(1.01);
    }

    .btn-enhanced {
        border-radius: 15px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-enhanced:hover::before {
        left: 100%;
    }

    .btn-enhanced:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .balance-old {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-weight: 600;
    }

    .balance-new {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-weight: 600;
    }

    .balance-added {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 10px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .fade-in {
        opacity: 0;
        transform: translateY(30px);
        animation: fadeInUp 0.8s ease-out forwards;
    }

    @keyframes fadeInUp {
        from { 
            opacity: 0; 
            transform: translateY(30px) scale(0.95); 
        }
        to { 
            opacity: 1; 
            transform: translateY(0) scale(1); 
        }
    }

    .year-selector {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    /* Search and Filter Styles */
    .search-filter-container {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .form-select-sm, .form-control-sm {
        border-radius: 10px;
        border: 1px solid #d1d3e2;
        transition: all 0.3s ease;
    }

    .form-select-sm:focus, .form-control-sm:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .filter-active {
        background-color: #e3f2fd !important;
        border-color: #2196f3 !important;
        box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25) !important;
        color: #1976d2 !important;
        font-weight: 600 !important;
    }

    .search-active {
        background-color: #fff3cd !important;
        border-color: #ffc107 !important;
    }

    .btn-filter {
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    /* Ensure no unwanted borders or lines */
    .table-title-section {
        border: none !important;
        box-shadow: none !important;
        background: transparent !important;
    }

    .table-title-section h5 {
        border-bottom: none !important;
        padding-bottom: 0 !important;
    }

    .table-title-section::after {
        display: none !important;
    }

    /* Remove any potential Bootstrap borders */
    .table-container .text-center {
        border-top: none !important;
        border-bottom: none !important;
        border-left: none !important;
        border-right: none !important;
    }

    .table-container h5 {
        border: none !important;
        border-bottom: none !important;
        box-shadow: none !important;
    }

    .table-container p {
        border: none !important;
        border-top: none !important;
        box-shadow: none !important;
    }

    /* Override any potential CSS that might add lines */
    .table-container .text-center::before,
    .table-container .text-center::after,
    .table-container h5::before,
    .table-container h5::after,
    .table-container p::before,
    .table-container p::after {
        display: none !important;
        content: none !important;
    }

    /* Specific styles for clean title */
    #cleanTableTitle {
        border: none !important;
        border-top: none !important;
        border-bottom: none !important;
        border-left: none !important;
        border-right: none !important;
        box-shadow: none !important;
        background: none !important;
        background-color: transparent !important;
    }

    #tableMainTitle {
        border: none !important;
        border-bottom: none !important;
        box-shadow: none !important;
        background: none !important;
        text-decoration: none !important;
    }

    #tableSubTitle {
        border: none !important;
        border-top: none !important;
        box-shadow: none !important;
        background: none !important;
    }

    /* Force remove any potential lines */
    #cleanTableTitle::before,
    #cleanTableTitle::after,
    #tableMainTitle::before,
    #tableMainTitle::after,
    #tableSubTitle::before,
    #tableSubTitle::after {
        display: none !important;
        content: "" !important;
        border: none !important;
        background: none !important;
    }

    /* Enhanced Back Button */
    .btn-back {
        background-color: #000 !important;
        color: #fff !important;
        border: 2px solid #fff !important;
        border-radius: 15px;
        padding: 1rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-back::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-back:hover::before {
        left: 100%;
    }

    .btn-back:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        background-color: #333 !important;
        color: #fff !important;
        border-color: #fff !important;
    }

    .btn-back:focus {
        background-color: #000 !important;
        color: #fff !important;
        border-color: #fff !important;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1><i class="fas fa-sync-alt me-3"></i>تدوير رصيد الإجازات السنوية</h1>
        <p class="lead mb-4">تدوير أرصدة الإجازات السنوية لموظفي المديرية للسنة الجديدة</p>

        <!-- Action Buttons -->
        <div class="mt-4">
            <a href="{% url 'leaves:leave_balance_list' %}" class="btn btn-back btn-lg">
                <i class="fas fa-arrow-left me-2"></i> العودة لأرصدة الإجازات
            </a>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="search-filter-container fade-in" style="animation-delay: 0.2s;">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">
                <i class="fas fa-filter me-2"></i> البحث والفلترة
            </h6>
            <small class="text-muted">استخدم الفلاتر لتضييق نطاق البحث</small>
        </div>

        <form method="get" id="filterForm">
            <input type="hidden" name="year" value="{{ selected_year }}">
            <div class="row g-3">
                <!-- Search Input -->
                <div class="col-md-3">
                    <label class="form-label small fw-bold">
                        <i class="fas fa-search text-primary me-1"></i> البحث العام
                    </label>
                    <input type="text" name="search" class="form-control form-control-sm"
                           placeholder="ابحث بالاسم، الرقم الوزاري، التخصص..."
                           value="{{ search_query|default:'' }}" id="searchInput">
                </div>

                <!-- Department Filter -->
                <div class="col-md-2">
                    <label class="form-label small fw-bold">
                        <i class="fas fa-building text-info me-1"></i> القسم
                    </label>
                    <select class="form-select form-select-sm" name="department" id="departmentFilter">
                        <option value="">جميع الأقسام</option>
                        {% for dept in departments %}
                            <option value="{{ dept }}" {% if department_filter == dept %}selected{% endif %}>
                                {{ dept }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Specialty Filter -->
                <div class="col-md-2">
                    <label class="form-label small fw-bold">
                        <i class="fas fa-graduation-cap text-success me-1"></i> التخصص
                    </label>
                    <select class="form-select form-select-sm" name="specialty" id="specialtyFilter">
                        <option value="">جميع التخصصات</option>
                        {% for specialty in specialties %}
                            <option value="{{ specialty }}" {% if specialty_filter == specialty %}selected{% endif %}>
                                {{ specialty }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Balance Range Filter -->
                <div class="col-md-2">
                    <label class="form-label small fw-bold">
                        <i class="fas fa-coins text-warning me-1"></i> نطاق الرصيد السابق
                    </label>
                    <select class="form-select form-select-sm" name="balance_range" id="balanceRangeFilter">
                        <option value="">جميع النطاقات</option>
                        <option value="0" {% if balance_range_filter == '0' %}selected{% endif %}>صفر يوم</option>
                        <option value="1-30" {% if balance_range_filter == '1-30' %}selected{% endif %}>1-30 يوم</option>
                        <option value="31-60" {% if balance_range_filter == '31-60' %}selected{% endif %}>31-60 يوم</option>
                        <option value="60+" {% if balance_range_filter == '60+' %}selected{% endif %}>أكثر من 60 يوم</option>
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="col-md-3">
                    <label class="form-label small">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-sm btn-filter" title="تطبيق الفلاتر">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm btn-filter" id="clearFilters" title="مسح الفلاتر">
                            <i class="fas fa-times me-1"></i> مسح
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm btn-filter" id="showAllFields" title="عرض جميع الحقول">
                            <i class="fas fa-eye me-1"></i> عرض الكل
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filter Info -->
            <div class="row mt-3">
                <div class="col-12">
                    <div id="filterInfo" class="small text-muted"></div>
                </div>
            </div>
        </form>
    </div>

    <!-- Information Card -->
    <div class="info-card fade-in" style="animation-delay: 0.1s;">
        <div class="row align-items-center">
            <div class="col-md-1">
                <div class="text-center">
                    <i class="fas fa-info-circle fa-3x text-primary"></i>
                </div>
            </div>
            <div class="col-md-11">
                <h5 class="mb-2 fw-bold text-primary">معلومات مهمة حول تدوير الرصيد</h5>
                <ul class="mb-0">
                    <li><strong>الرصيد السابق:</strong> الرصيد المتبقي من الإجازات والمغادرات الخاصة من السنة السابقة</li>
                    <li><strong>الإضافة:</strong> يتم إضافة 30 يوم لكل موظف كرصيد جديد</li>
                    <li><strong>الحد الأقصى:</strong> لا يمكن أن يتجاوز الرصيد الجديد 60 يوم</li>
                    <li><strong>التطبيق:</strong> يتم التدوير على موظفي المديرية فقط</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Year Selection -->
    <div class="year-selector fade-in" style="animation-delay: 0.2s;">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label class="form-label fw-bold">اختر السنة للتدوير</label>
                <select name="year" class="form-select form-select-lg">
                    {% for year_option in available_years %}
                    <option value="{{ year_option }}" {% if year_option == selected_year %}selected{% endif %}>
                        {{ year_option }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-enhanced btn-primary btn-lg">
                    <i class="fas fa-search me-2"></i> عرض البيانات
                </button>
            </div>
            <div class="col-md-4">
                {% if rollover_data %}
                <div class="d-flex gap-2">
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <input type="hidden" name="year" value="{{ selected_year }}">
                        <input type="hidden" name="action" value="rollover">
                        <button type="submit" class="btn btn-enhanced btn-success btn-lg"
                                onclick="return confirm('هل أنت متأكد من تدوير الرصيد للسنة {{ selected_year }}؟ هذا الإجراء لا يمكن التراجع عنه.')">
                            <i class="fas fa-sync-alt me-2"></i> تنفيذ التدوير
                        </button>
                    </form>
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <input type="hidden" name="year" value="{{ selected_year }}">
                        <input type="hidden" name="action" value="migrate">
                        <button type="submit" class="btn btn-enhanced btn-primary btn-lg"
                                onclick="return confirm('هل أنت متأكد من ترحيل البيانات للسنة {{ selected_year }}؟ سيتم إضافة أرصدة جديدة لجميع الموظفين.')">
                            <i class="fas fa-database me-2"></i> ترحيل البيانات
                        </button>
                    </form>
                </div>
                {% endif %}
            </div>
        </form>
    </div>

    <!-- Rollover Data Table -->
    {% if rollover_data %}
    <div class="table-container fade-in" style="animation-delay: 0.4s;">

        <!-- CLEAN TITLE SECTION - NO BORDERS OR LINES -->
        <div id="cleanTableTitle" style="text-align: center; margin-bottom: 2rem; border: none !important; background: none !important; box-shadow: none !important;">
            <h5 id="tableMainTitle" style="margin-bottom: 1rem; font-weight: 600; border: none !important; padding: 0 !important; box-shadow: none !important; background: none !important;">
                <i class="fas fa-table me-2"></i>
                بيانات تدوير الرصيد للسنة {{ selected_year }}
            </h5>
            <p id="tableSubTitle" style="margin-bottom: 0; font-size: 0.9rem; color: #6c757d; border: none !important; padding: 0 !important; box-shadow: none !important; background: none !important;">
                إجمالي الموظفين: {{ rollover_data|length }}
            </p>
        </div>

        <div class="table-responsive">
            <table class="table table-enhanced" id="rolloverTable">
                <thead>
                    <tr>
                        <th width="10%">الرقم الوزاري</th>
                        <th width="25%">الاسم الكامل</th>
                        <th width="20%">القسم</th>
                        <th width="10%">الرصيد السابق</th>
                        <th width="10%">الإضافة</th>
                        <th width="10%">الرصيد الجديد</th>
                        <th width="15%">ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for data in rollover_data %}
                    <tr>
                        <td>
                            <span class="badge bg-primary">{{ data.employee.ministry_number }}</span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-2" style="width: 35px; height: 35px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8rem;">
                                    {{ data.employee.full_name|first }}
                                </div>
                                <div class="fw-bold">{{ data.employee.full_name }}</div>
                            </div>
                        </td>
                        <td>
                            <span class="text-muted">{{ data.department|default:"غير محدد" }}</span>
                        </td>
                        <td>
                            <span class="balance-old">{{ data.previous_balance }} يوم</span>
                        </td>
                        <td>
                            <span class="balance-added">+{{ data.added_days }} يوم</span>
                        </td>
                        <td>
                            <span class="balance-new">{{ data.new_balance }} يوم</span>
                        </td>
                        <td>
                            <small class="text-muted">{{ data.notes }}</small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="text-center py-5 fade-in" style="animation-delay: 0.4s;">
        <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">اختر السنة لعرض بيانات التدوير</h5>
        <p class="text-muted">قم بتحديد السنة من القائمة أعلاه لعرض بيانات تدوير الرصيد</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable if there are records
    {% if rollover_data %}
    $('#rolloverTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
        },
        "pageLength": 25,
        "ordering": true,
        "searching": false,
        "paging": true,
        "info": true,
        "responsive": true,
        "order": [[ 1, "asc" ]] // Sort by name ascending
    });
    {% endif %}

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Filter functionality
    const searchInput = document.getElementById('searchInput');
    const departmentFilter = document.getElementById('departmentFilter');
    const specialtyFilter = document.getElementById('specialtyFilter');
    const balanceRangeFilter = document.getElementById('balanceRangeFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const showAllFieldsBtn = document.getElementById('showAllFields');
    const filterInfo = document.getElementById('filterInfo');

    // Update filter info
    function updateFilterInfo() {
        const activeFilters = [];

        if (searchInput && searchInput.value.trim()) {
            activeFilters.push(`البحث: "${searchInput.value.trim()}"`);
            searchInput.classList.add('search-active');
        } else if (searchInput) {
            searchInput.classList.remove('search-active');
        }

        if (departmentFilter && departmentFilter.value) {
            activeFilters.push(`القسم: ${departmentFilter.options[departmentFilter.selectedIndex].text}`);
            departmentFilter.classList.add('filter-active');
        } else if (departmentFilter) {
            departmentFilter.classList.remove('filter-active');
        }

        if (specialtyFilter && specialtyFilter.value) {
            activeFilters.push(`التخصص: ${specialtyFilter.options[specialtyFilter.selectedIndex].text}`);
            specialtyFilter.classList.add('filter-active');
        } else if (specialtyFilter) {
            specialtyFilter.classList.remove('filter-active');
        }

        if (balanceRangeFilter && balanceRangeFilter.value) {
            activeFilters.push(`نطاق الرصيد: ${balanceRangeFilter.options[balanceRangeFilter.selectedIndex].text}`);
            balanceRangeFilter.classList.add('filter-active');
        } else if (balanceRangeFilter) {
            balanceRangeFilter.classList.remove('filter-active');
        }

        if (filterInfo) {
            if (activeFilters.length > 0) {
                filterInfo.innerHTML = `<i class="fas fa-filter text-primary"></i> الفلاتر النشطة: ${activeFilters.join(' | ')}`;
            } else {
                filterInfo.innerHTML = '<i class="fas fa-info-circle text-muted"></i> لا توجد فلاتر نشطة - يتم عرض جميع البيانات';
            }
        }
    }

    // Clear filters
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function() {
            const url = new URL(window.location);
            url.searchParams.delete('search');
            url.searchParams.delete('department');
            url.searchParams.delete('specialty');
            url.searchParams.delete('balance_range');
            window.location.href = url.toString();
        });
    }

    // Show all fields (remove all filters)
    if (showAllFieldsBtn) {
        showAllFieldsBtn.addEventListener('click', function() {
            if (searchInput) searchInput.value = '';
            if (departmentFilter) departmentFilter.value = '';
            if (specialtyFilter) specialtyFilter.value = '';
            if (balanceRangeFilter) balanceRangeFilter.value = '';

            // Submit form to apply changes
            document.getElementById('filterForm').submit();
        });
    }

    // Auto-submit on filter change
    [departmentFilter, specialtyFilter, balanceRangeFilter].forEach(filter => {
        if (filter) {
            filter.addEventListener('change', function() {
                document.getElementById('filterForm').submit();
            });
        }
    });

    // Search on Enter key
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('filterForm').submit();
            }
        });
    }

    // Initialize filter info
    updateFilterInfo();

    // Force remove any unwanted borders or lines from title section
    function removeUnwantedBorders() {
        const titleSection = document.getElementById('cleanTableTitle');
        const mainTitle = document.getElementById('tableMainTitle');
        const subTitle = document.getElementById('tableSubTitle');

        if (titleSection) {
            titleSection.style.border = 'none';
            titleSection.style.borderTop = 'none';
            titleSection.style.borderBottom = 'none';
            titleSection.style.borderLeft = 'none';
            titleSection.style.borderRight = 'none';
            titleSection.style.boxShadow = 'none';
            titleSection.style.background = 'none';
            titleSection.style.backgroundColor = 'transparent';
        }

        if (mainTitle) {
            mainTitle.style.border = 'none';
            mainTitle.style.borderBottom = 'none';
            mainTitle.style.boxShadow = 'none';
            mainTitle.style.background = 'none';
            mainTitle.style.textDecoration = 'none';
        }

        if (subTitle) {
            subTitle.style.border = 'none';
            subTitle.style.borderTop = 'none';
            subTitle.style.boxShadow = 'none';
            subTitle.style.background = 'none';
        }
    }

    // Call the function immediately and after a short delay
    removeUnwantedBorders();
    setTimeout(removeUnwantedBorders, 100);
    setTimeout(removeUnwantedBorders, 500);
});
</script>
{% endblock %}
