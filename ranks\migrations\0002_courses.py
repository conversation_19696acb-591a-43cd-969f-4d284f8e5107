# Generated manually for courses

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ranks', '0001_initial'),
        ('employees', '0002_employee_gender_alter_employee_school'),
    ]

    operations = [
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, unique=True, verbose_name='Course Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Course',
                'verbose_name_plural': 'Courses',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeCourse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('hours', models.PositiveIntegerField(verbose_name='Hours')),
                ('completion_date', models.DateField(verbose_name='Completion Date')),
                ('certificate_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='Certificate Number')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='ranks.course')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='courses', to='employees.employee')),
            ],
            options={
                'verbose_name': 'Employee Course',
                'verbose_name_plural': 'Employee Courses',
                'ordering': ['-completion_date'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='employeecourse',
            unique_together={('employee', 'course', 'completion_date')},
        ),
    ]
