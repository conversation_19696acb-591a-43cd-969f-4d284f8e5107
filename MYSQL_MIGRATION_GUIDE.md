# دليل التحويل من SQLite إلى MySQL
# SQLite to MySQL Migration Guide

## نظرة عامة

هذا الدليل يوضح كيفية تحويل نظام الموارد البشرية من قاعدة بيانات SQLite إلى MySQL مع الحفاظ على جميع البيانات الحالية.

## المتطلبات المسبقة

### 1. تثبيت MySQL Server
- حمل وثبت MySQL Server من: https://dev.mysql.com/downloads/mysql/
- أو استخدم XAMPP للتطوير: https://www.apachefriends.org/

### 2. التحقق من المكتبات المطلوبة
تأكد من وجود `mysqlclient` في requirements.txt (موجود بالفعل).

## خطوات التحويل

### الطريقة الأولى: التحويل التلقائي (الموصى بها)

#### 1. تشغيل سكريپت التحويل التلقائي
```bash
migrate_to_mysql.bat
```

هذا السكريپت سيقوم بـ:
- إنشاء نسخة احتياطية من البيانات الحالية
- اختبار الاتصال بـ MySQL
- تطبيق المخططات على MySQL
- استيراد البيانات
- التحقق من صحة العملية

### الطريقة الثانية: التحويل اليدوي

#### 1. إنشاء نسخة احتياطية
```bash
python backup_script.py
```

#### 2. إعداد قاعدة بيانات MySQL
```bash
python setup_mysql.py
```

أو يدوياً في MySQL:
```sql
CREATE DATABASE hr_system_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'hr_user'@'localhost' IDENTIFIED BY 'hr_password_2024';
GRANT ALL PRIVILEGES ON hr_system_db.* TO 'hr_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 3. اختبار الاتصال
```bash
python test_mysql_connection.py
```

#### 4. تطبيق المخططات
```bash
python manage.py migrate
```

#### 5. استيراد البيانات
```bash
python manage.py loaddata backup_data.json
```

#### 6. التحقق من صحة البيانات
```bash
python verify_migration.py
```

## إعدادات قاعدة البيانات

تم تحديث ملف `settings.py` ليستخدم MySQL:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'hr_system_db',
        'USER': 'hr_user',
        'PASSWORD': 'hr_password_2024',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'autocommit': True,
        },
    }
}
```

## الملفات المنشأة

### سكريپتات التحويل
- `backup_script.py` - إنشاء نسخة احتياطية من SQLite
- `setup_mysql.py` - إعداد قاعدة بيانات MySQL
- `test_mysql_connection.py` - اختبار الاتصال بـ MySQL
- `migrate_to_mysql.py` - سكريپت التحويل الشامل
- `verify_migration.py` - التحقق من صحة التحويل
- `migrate_to_mysql.bat` - سكريپت تشغيل تلقائي

### ملفات التوثيق
- `mysql_setup_guide.md` - دليل إعداد MySQL
- `MYSQL_MIGRATION_GUIDE.md` - هذا الدليل

### ملفات النسخ الاحتياطي
- `backup_data.json` - نسخة احتياطية من بيانات SQLite
- `hr_system/settings_sqlite_backup.py` - نسخة احتياطية من إعدادات SQLite

## استكشاف الأخطاء

### خطأ في الاتصال بـ MySQL
```
django.db.utils.OperationalError: (2003, "Can't connect to MySQL server")
```
**الحل:**
1. تأكد من تشغيل خدمة MySQL
2. تحقق من معلومات الاتصال في settings.py
3. تأكد من إنشاء قاعدة البيانات والمستخدم

### خطأ في ترميز البيانات
```
UnicodeEncodeError: 'charmap' codec can't encode characters
```
**الحل:**
- استخدم سكريپت backup_script.py بدلاً من dumpdata مباشرة

### خطأ في استيراد البيانات
```
django.db.utils.IntegrityError: Duplicate entry
```
**الحل:**
1. تأكد من أن قاعدة البيانات فارغة قبل الاستيراد
2. قم بحذف الجداول وإعادة تشغيل migrate

## التحقق من نجاح التحويل

### 1. اختبار تشغيل النظام
```bash
python manage.py runserver
```

### 2. اختبار العمليات الأساسية
- تسجيل الدخول
- عرض قوائم الموظفين
- إضافة موظف جديد
- تعديل بيانات موظف
- حذف سجل اختبار

### 3. فحص البيانات
```bash
python verify_migration.py
```

## الصيانة والنسخ الاحتياطي

### إنشاء نسخة احتياطية من MySQL
```bash
mysqldump -u hr_user -p hr_system_db > backup_mysql.sql
```

### استعادة النسخة الاحتياطية
```bash
mysql -u hr_user -p hr_system_db < backup_mysql.sql
```

## الأمان

### في البيئة الإنتاجية:
1. غير كلمة مرور قاعدة البيانات
2. استخدم مستخدم بصلاحيات محدودة
3. فعل SSL للاتصال
4. استخدم متغيرات البيئة لحفظ كلمات المرور

### مثال على استخدام متغيرات البيئة:
```python
import os
from decouple import config

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DB_NAME', default='hr_system_db'),
        'USER': config('DB_USER', default='hr_user'),
        'PASSWORD': config('DB_PASSWORD'),
        'HOST': config('DB_HOST', default='localhost'),
        'PORT': config('DB_PORT', default='3306'),
    }
}
```

## الدعم

في حالة مواجهة مشاكل:
1. راجع ملفات السجل
2. تأكد من إصدارات المكتبات
3. تحقق من إعدادات MySQL
4. استخدم النسخة الاحتياطية للعودة إلى SQLite إذا لزم الأمر
