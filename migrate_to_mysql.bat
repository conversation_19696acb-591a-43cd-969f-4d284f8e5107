@echo off
chcp 65001 > nul
echo ========================================
echo تحويل نظام الموارد البشرية إلى MySQL
echo HR System MySQL Migration
echo ========================================
echo.

echo الخطوة 1: التحقق من البيئة الافتراضية...
if not exist "venv\Scripts\python.exe" (
    echo خطأ: لم يتم العثور على البيئة الافتراضية
    echo يرجى تشغيل create_venv.bat أولاً
    pause
    exit /b 1
)

echo الخطوة 2: التحقق من النسخة الاحتياطية...
if not exist "backup_data.json" (
    echo إنشاء نسخة احتياطية من البيانات الحالية...
    venv\Scripts\python.exe backup_script.py
    if errorlevel 1 (
        echo فشل في إنشاء النسخة الاحتياطية
        pause
        exit /b 1
    )
) else (
    echo تم العثور على النسخة الاحتياطية الموجودة
)

echo.
echo الخطوة 3: اختبار الاتصال بـ MySQL...
venv\Scripts\python.exe test_mysql_connection.py
if errorlevel 1 (
    echo.
    echo فشل الاتصال بـ MySQL
    echo يرجى التأكد من:
    echo 1. تثبيت MySQL Server
    echo 2. تشغيل خدمة MySQL
    echo 3. إنشاء قاعدة البيانات والمستخدم
    echo.
    echo لإعداد MySQL، راجع ملف: mysql_setup_guide.md
    echo أو قم بتشغيل: python setup_mysql.py
    echo.
    pause
    exit /b 1
)

echo.
echo الخطوة 4: بدء عملية التحويل...
echo تحذير: هذه العملية ستحول النظام من SQLite إلى MySQL
echo تأكد من إنشاء نسخة احتياطية من جميع البيانات
echo.
set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i not "%confirm%"=="y" (
    echo تم إلغاء العملية
    pause
    exit /b 0
)

echo.
echo تنفيذ عملية التحويل...
venv\Scripts\python.exe migrate_to_mysql.py
if errorlevel 1 (
    echo فشلت عملية التحويل
    pause
    exit /b 1
)

echo.
echo ========================================
echo تمت عملية التحويل بنجاح!
echo ========================================
echo.
echo يمكنك الآن تشغيل النظام باستخدام:
echo python manage.py runserver
echo.
echo للتحقق من عمل النظام، قم بزيارة:
echo http://localhost:8000
echo.
pause
