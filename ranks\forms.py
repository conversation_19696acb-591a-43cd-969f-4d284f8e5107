from django import forms
from .models import RankType, EmployeeRank, Course, EmployeeCourse
from employees.models import Employee

class RankTypeForm(forms.ModelForm):
    class Meta:
        model = RankType
        fields = ['name', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

class EmployeeRankForm(forms.Form):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Rank type selection
    rank_type = forms.ModelChoiceField(
        queryset=RankType.objects.all(),
        label='نوع الرتبة',
        empty_label='اختر نوع الرتبة',
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    # Date obtained
    date_obtained = forms.DateField(
        label='تاريخ الحصول عليها',
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )

    # Notes
    notes = forms.CharField(
        label='ملاحظات',
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
    )

class EmployeeRankImportForm(forms.Form):
    excel_file = forms.FileField(
        label='ملف Excel',
        help_text='يجب أن يحتوي الملف على الأعمدة التالية: الرقم الوزاري، نوع الرتبة، تاريخ الحصول عليها، ملاحظات'
    )

    rank_type_mapping = forms.ModelChoiceField(
        queryset=RankType.objects.all(),
        label='نوع الرتبة الافتراضي',
        help_text='سيتم استخدام هذا النوع إذا لم يتم تحديد نوع الرتبة في الملف',
        required=False
    )


class CourseForm(forms.ModelForm):
    class Meta:
        model = Course
        fields = ['name', 'description', 'hours']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل اسم الدورة (مثال: دورة الحاسوب الأساسي)',
                'maxlength': '200'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'وصف مختصر عن محتوى الدورة وأهدافها (اختياري)'
            }),
            'hours': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'عدد ساعات الدورة',
                'min': '0',
                'max': '1000'
            }),
        }
        labels = {
            'name': 'اسم الدورة',
            'description': 'وصف الدورة',
            'hours': 'عدد الساعات'
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            name = name.strip()
            if len(name) < 3:
                raise forms.ValidationError('يجب أن يكون اسم الدورة 3 أحرف على الأقل')

            # التحقق من عدم وجود دورة بنفس الاسم
            if Course.objects.filter(name__iexact=name).exclude(pk=self.instance.pk if self.instance else None).exists():
                raise forms.ValidationError('يوجد دورة بهذا الاسم مسبقاً')

        return name

    def clean_hours(self):
        hours = self.cleaned_data.get('hours')
        if hours is not None:
            if hours < 0:
                raise forms.ValidationError('عدد الساعات لا يمكن أن يكون سالباً')
            if hours > 1000:
                raise forms.ValidationError('عدد الساعات لا يمكن أن يتجاوز 1000 ساعة')
        return hours


class EmployeeCourseForm(forms.Form):
    # Employee search
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        max_length=20,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل الرقم الوزاري'})
    )

    # Course selection
    course = forms.ModelChoiceField(
        queryset=Course.objects.all(),
        label='اسم الدورة',
        empty_label='اختر الدورة',
        widget=forms.Select(attrs={'class': 'form-control select2'})
    )

    # Hours
    hours = forms.IntegerField(
        label='عدد الساعات',
        min_value=1,
        required=True,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'سيتم تحديدها تلقائياً عند اختيار الدورة'
        })
    )

    # Completion date
    completion_date = forms.DateField(
        label='تاريخ الحصول على الدورة',
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )

    # Certificate number
    certificate_number = forms.CharField(
        label='رقم الشهادة',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )

    # Notes
    notes = forms.CharField(
        label='ملاحظات',
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
    )

    def clean_ministry_number(self):
        ministry_number = self.cleaned_data.get('ministry_number')
        if ministry_number:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                return ministry_number
            except Employee.DoesNotExist:
                raise forms.ValidationError('لا يوجد موظف بهذا الرقم الوزاري.')
        return ministry_number
