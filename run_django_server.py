#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run Django development server with error handling.
"""
import os
import sys
import subprocess

def main():
    """Run Django development server."""
    print("Starting Django server...")
    
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Set environment variables
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "hr_system.settings")
    
    # Try to run the server
    try:
        # Use subprocess to run the server
        process = subprocess.Popen(
            [sys.executable, "manage.py", "runserver", "0.0.0.0:8000"],
            cwd=current_dir
        )
        
        # Wait for the process to complete
        process.wait()
        
        # Check if the process exited with an error
        if process.returncode != 0:
            print(f"Server exited with error code {process.returncode}")
            input("Press Enter to exit...")
    except Exception as e:
        print(f"Error starting server: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
