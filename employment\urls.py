from django.urls import path
from . import views

app_name = 'employment'

urlpatterns = [
    path('', views.employment_list, name='employment_list'),
    path('<int:pk>/edit/', views.employment_update, name='employment_update'),
    path('<int:pk>/delete/', views.employment_delete, name='employment_delete'),
    path('departments/', views.department_list, name='department_list'),
    path('departments/add/', views.department_create, name='department_create'),
    path('departments/<int:pk>/', views.department_detail, name='department_detail'),
    path('departments/<int:pk>/edit/', views.department_update, name='department_update'),
    path('departments/<int:pk>/delete/', views.department_delete, name='department_delete'),
    path('departments/<int:pk>/employees/', views.department_employees, name='department_employees'),
    path('departments/import/', views.department_import, name='department_import'),
    path('departments/export-excel/', views.export_departments_to_excel, name='export_departments_to_excel'),
    path('school-details/', views.school_details, name='school_details'),
    path('school-details/export-excel/', views.export_school_details_to_excel, name='export_school_details_to_excel'),

    # صفة التعيين
    path('appointment-types/', views.appointment_type_list, name='appointment_type_list'),
    path('appointment-types/add/', views.appointment_type_create, name='appointment_type_create'),
    path('appointment-types/<int:pk>/', views.appointment_type_detail, name='appointment_type_detail'),
    path('appointment-types/<int:pk>/edit/', views.appointment_type_update, name='appointment_type_update'),
    path('appointment-types/<int:pk>/delete/', views.appointment_type_delete, name='appointment_type_delete'),

    # Technical Position URLs
    path('technical-positions/', views.technical_position_list, name='technical_position_list'),
    path('technical-positions/add/', views.technical_position_create, name='technical_position_create'),
    path('technical-positions/<int:pk>/edit/', views.technical_position_update, name='technical_position_update'),
    path('technical-positions/<int:pk>/delete/', views.technical_position_delete, name='technical_position_delete'),
    path('technical-positions/export-excel/', views.export_technical_positions_excel, name='export_technical_positions_excel'),

    # Actual Service URL
    path('actual-service/', views.actual_service_list, name='actual_service_list'),

    # المسميات الوظيفية
    path('positions/', views.position_list, name='position_list'),
    path('positions/add/', views.position_create, name='position_create'),
    path('positions/<int:pk>/', views.position_detail, name='position_detail'),
    path('positions/<int:pk>/edit/', views.position_update, name='position_update'),
    path('positions/<int:pk>/delete/', views.position_delete, name='position_delete'),

    # الحراك الوظيفي
    path('employee-positions/', views.employee_position_list, name='employee_position_list'),
    path('employee-positions/add/', views.employee_position_create, name='employee_position_create'),
    path('employee-positions/<int:pk>/edit/', views.employee_position_update, name='employee_position_update'),
    path('employee-positions/<int:pk>/delete/', views.employee_position_delete, name='employee_position_delete'),
    path('get-employee/', views.get_employee_by_ministry_number, name='get_employee_by_ministry_number'),
    path('test-form/', views.test_form, name='test_form'),

    # شهادة الخبرة
    path('experience-certificates/', views.experience_certificate_list, name='experience_certificate_list'),
    path('experience-certificates/<int:pk>/', views.experience_certificate_view, name='experience_certificate_view'),
    path('experience-certificates/<int:pk>/export/', views.experience_certificate_export, name='experience_certificate_export'),

    # إدارة المسميات الوظيفية
    path('cleanup-positions/', views.cleanup_teacher_positions, name='cleanup_teacher_positions'),

    # البيانات التعريفية للموظف
    path('employee-identifications/', views.employee_identification_list, name='employee_identification_list'),
    path('employee-identifications/add/', views.employee_identification_create, name='employee_identification_create'),
    path('employee-identifications/<int:pk>/', views.employee_identification_detail, name='employee_identification_detail'),
    path('employee-identifications/<int:pk>/edit/', views.employee_identification_update, name='employee_identification_update'),
    path('employee-identifications/<int:pk>/delete/', views.employee_identification_delete, name='employee_identification_delete'),
    path('employee-identifications/import/', views.import_id_numbers, name='import_id_numbers'),
    path('employee-identifications/export-pdf/', views.export_identifications_pdf, name='export_identifications_pdf'),
    path('employee-identifications/export-excel/', views.export_identifications_excel, name='export_identifications_excel'),
    path('employee-identifications/add-id-number/', views.add_id_number_page, name='add_id_number_page'),
    path('get-employee-by-ministry-number/', views.get_employee_by_ministry_number, name='get_employee_by_ministry_number'),
    path('get-employee-by-ministry-number-identification/', views.get_employee_by_ministry_number_identification, name='get_employee_by_ministry_number_identification'),
    path('update-id-number/', views.update_id_number, name='update_id_number'),
    path('delete-id-number-ajax/', views.delete_id_number_ajax, name='delete_id_number_ajax'),
    path('add-id-number/', views.add_id_number, name='add_id_number'),
    path('employee-identifications/<int:pk>/edit-id-number/', views.edit_id_number, name='edit_id_number'),
    path('employee-identifications/<int:pk>/delete-id-number/', views.delete_id_number, name='delete_id_number'),

    # الموظفين الزوائد
    path('excess-employees/', views.excess_employee_list, name='excess_employee_list'),
    path('excess-employees/add/', views.excess_employee_create, name='excess_employee_create'),
    path('excess-employees/<int:pk>/', views.excess_employee_detail, name='excess_employee_detail'),
    path('excess-employees/<int:pk>/edit/', views.excess_employee_update, name='excess_employee_update'),
    path('excess-employees/<int:pk>/delete/', views.excess_employee_delete, name='excess_employee_delete'),
    path('excess-employees/<int:pk>/resolve/', views.excess_employee_resolve, name='excess_employee_resolve'),
    path('excess-employees/export-excel/', views.export_excess_employees_excel, name='export_excess_employees_excel'),
    path('get-employee-by-ministry-number-json/', views.get_employee_by_ministry_number_json, name='get_employee_by_ministry_number_json'),

    # الحالات المرضية
    path('medical-conditions/', views.medical_condition_list, name='medical_condition_list'),
    path('medical-conditions/add/', views.medical_condition_create, name='medical_condition_create'),
    path('medical-conditions/<int:pk>/', views.medical_condition_detail, name='medical_condition_detail'),
    path('medical-conditions/<int:pk>/edit/', views.medical_condition_update, name='medical_condition_update'),
    path('medical-conditions/<int:pk>/delete/', views.medical_condition_delete, name='medical_condition_delete'),
    path('medical-conditions/export-excel/', views.export_medical_conditions_excel, name='export_medical_conditions_excel'),

    # أسماء الحالات المرضية
    path('medical-condition-names/', views.medical_condition_name_list, name='medical_condition_name_list'),
    path('medical-condition-names/add/', views.medical_condition_name_create, name='medical_condition_name_create'),
    path('medical-condition-names/<int:pk>/edit/', views.medical_condition_name_update, name='medical_condition_name_update'),
    path('medical-condition-names/<int:pk>/delete/', views.medical_condition_name_delete, name='medical_condition_name_delete'),

    # BTEC
    path('btec/', views.btec_list, name='btec_list'),
    path('btec/add/', views.btec_create, name='btec_create'),
    path('btec/<int:pk>/edit/', views.btec_update, name='btec_update'),
    path('btec/<int:pk>/delete/', views.btec_delete, name='btec_delete'),

    # BTEC Fields
    path('btec-fields/', views.btec_field_list, name='btec_field_list'),
    path('btec-fields/add/', views.btec_field_create, name='btec_field_create'),
    path('btec-fields/<int:pk>/edit/', views.btec_field_update, name='btec_field_update'),
    path('btec-fields/<int:pk>/delete/', views.btec_field_delete, name='btec_field_delete'),

    # BTEC Jobs
    path('btec-jobs/', views.btec_job_list, name='btec_job_list'),
    path('btec-jobs/add/', views.btec_job_create, name='btec_job_create'),
    path('btec-jobs/<int:pk>/edit/', views.btec_job_update, name='btec_job_update'),
    path('btec-jobs/<int:pk>/delete/', views.btec_job_delete, name='btec_job_delete'),

    # Non-Payment (عدم الصرف)
    path('non-payment/', views.non_payment_list, name='non_payment_list'),
    path('non-payment/add/', views.non_payment_create, name='non_payment_create'),
    path('non-payment/<int:pk>/edit/', views.non_payment_update, name='non_payment_update'),
    path('non-payment/<int:pk>/delete/', views.non_payment_delete, name='non_payment_delete'),
    path('non-payment/transfer/', views.non_payment_transfer, name='non_payment_transfer'),
    path('non-payment/summary/', views.non_payment_summary, name='non_payment_summary'),
    path('non-payment/print-summary/', views.non_payment_print_summary, name='non_payment_print_summary'),
    path('non-payment/cancel-transfer/', views.non_payment_cancel_transfer, name='non_payment_cancel_transfer'),

    # Zero Schools URLs
    path('zero-schools/', views.zero_schools_list, name='zero_schools_list'),
    path('zero-schools/add/', views.zero_school_create, name='zero_school_create'),
    path('zero-schools/<int:pk>/edit/', views.zero_school_edit, name='zero_school_edit'),
    path('zero-schools/<int:pk>/delete/', views.zero_school_delete, name='zero_school_delete'),
    path('zero-schools/export-excel/', views.export_zero_schools_excel, name='export_zero_schools_excel'),

    # Service Purchase URLs
    path('service-purchase/', views.service_purchase_list, name='service_purchase_list'),
    path('service-purchase/add/', views.service_purchase_create, name='service_purchase_create'),
    path('service-purchase/<int:pk>/edit/', views.service_purchase_update, name='service_purchase_update'),
    path('service-purchase/<int:pk>/delete/', views.service_purchase_delete, name='service_purchase_delete'),
    path('service-purchase/export-excel/', views.service_purchase_export_excel, name='service_purchase_export_excel'),
    path('search-employee-by-ministry-number/', views.get_employee_by_ministry_number_service_purchase, name='search_employee_by_ministry_number'),

    # AJAX endpoints
    path('ajax/get-employee-by-ministry-number/', views.get_employee_by_ministry_number, name='get_employee_by_ministry_number'),
]
