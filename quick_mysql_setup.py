#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إعداد MySQL سريع باستخدام Docker أو محاكي محلي
Quick MySQL Setup using Docker or Local Emulator
"""

import os
import sys
import subprocess
import time
import socket

def check_docker():
    """فحص وجود Docker"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Docker متوفر: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("! Docker غير متوفر")
    return False

def start_mysql_docker():
    """تشغيل MySQL باستخدام Docker"""
    print("تشغيل MySQL باستخدام Docker...")
    
    try:
        # إيقاف أي حاوية MySQL موجودة
        subprocess.run(['docker', 'stop', 'hr-mysql'], capture_output=True)
        subprocess.run(['docker', 'rm', 'hr-mysql'], capture_output=True)
        
        # تشغيل حاوية MySQL جديدة
        cmd = [
            'docker', 'run', '--name', 'hr-mysql',
            '-e', 'MYSQL_ROOT_PASSWORD=root123',
            '-e', 'MYSQL_DATABASE=hr_system_db',
            '-e', 'MYSQL_USER=hr_user',
            '-e', 'MYSQL_PASSWORD=hr_password_2024',
            '-p', '3306:3306',
            '-d', 'mysql:8.0',
            '--character-set-server=utf8mb4',
            '--collation-server=utf8mb4_unicode_ci'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ تم تشغيل MySQL Docker بنجاح")
            print("انتظار تشغيل MySQL...")
            
            # انتظار تشغيل MySQL
            for i in range(30):
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    result = sock.connect_ex(('localhost', 3306))
                    sock.close()
                    
                    if result == 0:
                        print("✓ MySQL جاهز للاستخدام")
                        return True
                except:
                    pass
                
                print(f"انتظار... ({i+1}/30)")
                time.sleep(2)
            
            print("✗ انتهت مهلة انتظار تشغيل MySQL")
            return False
        else:
            print(f"✗ فشل تشغيل Docker: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في Docker: {e}")
        return False

def download_portable_mysql():
    """تحميل MySQL محمول"""
    print("تحميل MySQL محمول...")
    
    try:
        import requests
        import zipfile
        
        # رابط MySQL محمول (مثال)
        url = "https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-8.0.35-winx64.zip"
        filename = "mysql-portable.zip"
        
        print("تحميل MySQL...")
        response = requests.get(url, stream=True)
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        print("استخراج الملفات...")
        with zipfile.ZipFile(filename, 'r') as zip_ref:
            zip_ref.extractall("mysql-portable")
        
        print("✓ تم تحميل MySQL محمول")
        return True
        
    except Exception as e:
        print(f"✗ خطأ في التحميل: {e}")
        return False

def create_mysql_config():
    """إنشاء ملف إعداد MySQL"""
    config_content = """[mysqld]
port=3306
basedir=mysql-portable
datadir=mysql-portable/data
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
default-authentication-plugin=mysql_native_password
"""
    
    try:
        with open("my.ini", "w") as f:
            f.write(config_content)
        print("✓ تم إنشاء ملف الإعداد")
        return True
    except Exception as e:
        print(f"✗ خطأ في إنشاء الإعداد: {e}")
        return False

def use_online_mysql():
    """استخدام قاعدة بيانات MySQL مجانية عبر الإنترنت"""
    print("إعداد قاعدة بيانات MySQL مجانية...")
    
    # خدمات MySQL مجانية
    services = [
        {
            "name": "FreeSQLDatabase",
            "host": "sql12.freesqldatabase.com",
            "port": 3306,
            "info": "قم بالتسجيل في: https://www.freesqldatabase.com/"
        },
        {
            "name": "DB4Free",
            "host": "db4free.net",
            "port": 3306,
            "info": "قم بالتسجيل في: https://www.db4free.net/"
        }
    ]
    
    print("خدمات MySQL مجانية متاحة:")
    for i, service in enumerate(services, 1):
        print(f"{i}. {service['name']}")
        print(f"   الخادم: {service['host']}")
        print(f"   المعلومات: {service['info']}")
        print()
    
    return False

def main():
    """الدالة الرئيسية"""
    print("إعداد MySQL سريع")
    print("=" * 20)
    
    print("الخيارات المتاحة:")
    print("1. Docker MySQL (الأسرع)")
    print("2. تحميل XAMPP")
    print("3. MySQL محمول")
    print("4. خدمة MySQL مجانية")
    print("5. العودة إلى SQLite")
    
    choice = input("\nاختر رقم الخيار (1-5): ").strip()
    
    if choice == "1":
        if check_docker():
            return start_mysql_docker()
        else:
            print("Docker غير متوفر، جرب خيار آخر")
            return False
    
    elif choice == "2":
        print("تحميل XAMPP...")
        os.system("python download_xampp.py")
        return False
    
    elif choice == "3":
        return download_portable_mysql()
    
    elif choice == "4":
        use_online_mysql()
        return False
    
    elif choice == "5":
        print("العودة إلى SQLite...")
        os.system("python rollback_to_sqlite.py")
        return True
    
    else:
        print("خيار غير صحيح")
        return False

if __name__ == "__main__":
    if main():
        print("\n✓ تم الإعداد بنجاح!")
    else:
        print("\n! لم يكتمل الإعداد")
        print("يمكنك:")
        print("1. تثبيت XAMPP يدوياً من: https://www.apachefriends.org/")
        print("2. استخدام SQLite (python rollback_to_sqlite.py)")
        print("3. إعادة المحاولة لاحقاً")
