#!/usr/bin/env python
"""
سكريبت لنقل البيانات من SQLite إلى MySQL
"""

import os
import sys
import django
from django.conf import settings

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

import sqlite3
from django.db import connection
from django.apps import apps

def migrate_data_from_sqlite():
    """نقل البيانات من SQLite إلى MySQL"""
    
    # الاتصال بقاعدة بيانات SQLite القديمة
    sqlite_db_path = 'db.sqlite3'
    if not os.path.exists(sqlite_db_path):
        print("❌ ملف قاعدة البيانات SQLite غير موجود")
        return False
    
    sqlite_conn = sqlite3.connect(sqlite_db_path)
    sqlite_cursor = sqlite_conn.cursor()
    
    # الحصول على قائمة الجداول
    sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = sqlite_cursor.fetchall()
    
    print(f"📊 تم العثور على {len(tables)} جدول في قاعدة البيانات القديمة")
    
    # جداول مهمة للنسخ
    important_tables = [
        'auth_user',
        'employees_employee', 
        'employees_department',
        'employees_position',
        'ranks_rank',
        'ranks_course',
        'ranks_employeerank',
        'ranks_employeecourse',
        'employment_employment',
        'announcements_announcement'
    ]
    
    mysql_cursor = connection.cursor()
    
    for table_name in important_tables:
        try:
            # التحقق من وجود الجدول في SQLite
            sqlite_cursor.execute(f"SELECT count(*) FROM sqlite_master WHERE type='table' AND name='{table_name}';")
            if sqlite_cursor.fetchone()[0] == 0:
                print(f"⚠️  الجدول {table_name} غير موجود في SQLite")
                continue
            
            # الحصول على البيانات من SQLite
            sqlite_cursor.execute(f"SELECT * FROM {table_name}")
            rows = sqlite_cursor.fetchall()
            
            if not rows:
                print(f"📝 الجدول {table_name} فارغ")
                continue
            
            # الحصول على أسماء الأعمدة
            sqlite_cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = sqlite_cursor.fetchall()
            column_names = [col[1] for col in columns_info]
            
            print(f"📋 نسخ {len(rows)} سجل من جدول {table_name}")
            
            # تحضير استعلام الإدراج
            placeholders = ', '.join(['%s'] * len(column_names))
            columns_str = ', '.join([f"`{col}`" for col in column_names])
            insert_query = f"INSERT IGNORE INTO {table_name} ({columns_str}) VALUES ({placeholders})"
            
            # إدراج البيانات في MySQL
            for row in rows:
                try:
                    # تحويل None إلى NULL وتنظيف البيانات
                    cleaned_row = []
                    for value in row:
                        if value is None:
                            cleaned_row.append(None)
                        elif isinstance(value, str):
                            # تنظيف النصوص من الرموز الخاصة
                            cleaned_value = value.encode('utf-8', errors='ignore').decode('utf-8')
                            cleaned_row.append(cleaned_value)
                        else:
                            cleaned_row.append(value)
                    
                    mysql_cursor.execute(insert_query, cleaned_row)
                except Exception as e:
                    print(f"⚠️  خطأ في إدراج سجل من {table_name}: {str(e)}")
                    continue
            
            connection.commit()
            print(f"✅ تم نسخ جدول {table_name} بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في نسخ جدول {table_name}: {str(e)}")
            continue
    
    sqlite_conn.close()
    print("🎉 تم الانتهاء من نسخ البيانات")
    return True

if __name__ == "__main__":
    print("🚀 بدء عملية نقل البيانات من SQLite إلى MySQL...")
    success = migrate_data_from_sqlite()
    if success:
        print("✅ تمت عملية النقل بنجاح!")
    else:
        print("❌ فشلت عملية النقل!")
