{% if grades %}
<div class="table-responsive">
    <table class="table table-striped table-hover" id="gradesTable">
        <thead>
            <tr>
                <th width="8%">الرقم الوزاري</th>
                <th width="20%">الاسم كامل</th>
                <th width="15%">التخصص</th>
                <th width="15%">القسم</th>
                <th width="10%">الدرجة الوظيفية</th>
                <th width="10%">نوع الترفيع</th>
                <th width="8%">السنة بالدرجة</th>
                <th width="10%">تاريخ الحلول</th>
                <th width="6%">ملاحظات</th>
                <th width="4%">الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for grade in grades %}
            <tr>
                <td>
                    <span class="badge bg-primary">{{ grade.employee.ministry_number }}</span>
                </td>
                <td>
                    <strong>{{ grade.employee.full_name }}</strong>
                </td>
                <td>
                    <span class="text-muted">{{ grade.employee.specialization|default:"غير محدد" }}</span>
                </td>
                <td>
                    <span class="text-info">{{ grade.employee.school|default:"غير محدد" }}</span>
                </td>
                <td>
                    {% if grade.grade %}
                        {% if grade.grade == 'special_grade' %}
                            <span class="badge bg-warning text-dark">الدرجة الخاصة</span>
                        {% elif grade.grade == 'grade_1' %}
                            <span class="badge bg-success">الدرجة الأولى</span>
                        {% elif grade.grade == 'grade_2' %}
                            <span class="badge bg-info">الدرجة الثانية</span>
                        {% elif grade.grade == 'grade_3' %}
                            <span class="badge bg-secondary">الدرجة الثالثة</span>
                        {% elif grade.grade == 'grade_4' %}
                            <span class="badge bg-dark">الدرجة الرابعة</span>
                        {% elif grade.grade == 'grade_5' %}
                            <span class="badge bg-light text-dark">الدرجة الخامسة</span>
                        {% else %}
                            <span class="badge bg-primary">{{ grade.get_grade_display }}</span>
                        {% endif %}
                    {% else %}
                        <span class="text-muted">غير محدد</span>
                    {% endif %}
                </td>
                <td>
                    {% if grade.promotion_type %}
                        <span class="badge bg-success">{{ grade.promotion_type.name }}</span>
                    {% else %}
                        <span class="text-muted">غير محدد</span>
                    {% endif %}
                </td>
                <td>
                    <span class="badge bg-primary">{{ grade.years_in_grade }} سنة</span>
                </td>
                <td>
                    {% if grade.grade_date %}
                        <i class="fas fa-calendar-alt text-muted me-1"></i>
                        {{ grade.grade_date|date:"Y/m/d" }}
                    {% else %}
                        <span class="text-muted">غير محدد</span>
                    {% endif %}
                </td>
                <td>
                    {% if grade.notes %}
                        <i class="fas fa-sticky-note text-warning"
                           title="{{ grade.notes }}"
                           data-bs-toggle="tooltip"></i>
                    {% else %}
                        <span class="text-muted">-</span>
                    {% endif %}
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="{% url 'employees:employee_grade_update' grade.pk %}"
                           class="btn btn-outline-primary btn-sm"
                           data-bs-toggle="tooltip"
                           title="تعديل الدرجة">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="{% url 'employees:employee_grade_delete' grade.pk %}"
                           class="btn btn-outline-danger btn-sm"
                           data-bs-toggle="tooltip"
                           title="حذف الدرجة"
                           onclick="return confirm('هل أنت متأكد من حذف هذه الدرجة؟')">
                            <i class="fas fa-trash"></i>
                        </a>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<nav aria-label="Page navigation" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadPage({{ page_obj.previous_page_number }}); return false;">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        {% endif %}

        {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadPage({{ num }}); return false;">{{ num }}</a>
                </li>
            {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadPage({{ page_obj.next_page_number }}); return false;">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
    <h5 class="text-muted">لا توجد درجات وظيفية</h5>
    <p class="text-muted">
        {% if search_query %}
            لم يتم العثور على نتائج للبحث "{{ search_query }}"
        {% else %}
            لم يتم إضافة أي درجات وظيفية بعد
        {% endif %}
    </p>
    <a href="{% url 'employees:employee_grade_create' %}" class="btn btn-primary">
        <i class="fas fa-plus"></i> إضافة درجة وظيفية
    </a>
</div>
{% endif %}
