<!DOCTYPE html>
<html>
<head>
    <title>تحميل صورة الهوية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            direction: rtl;
            margin: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #333;
        }
        .instructions {
            text-align: right;
            margin: 20px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .btn {
            padding: 10px 20px;
            background-color: #009688;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
        }
        .btn:hover {
            background-color: #00796b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تحميل صورة الهوية للنظام</h1>
        
        <div class="instructions">
            <h3>تعليمات:</h3>
            <ol>
                <li>اختر صورة الهوية التي تريد استخدامها في النظام.</li>
                <li>انقر على زر "تحميل الصورة".</li>
                <li>بعد التحميل، انسخ الصورة إلى المجلد <code>static/img/</code> باسم <code>jordan_id_back.jpg</code>.</li>
            </ol>
        </div>
        
        <form>
            <input type="file" id="imageInput" accept="image/*" />
            <br>
            <button type="button" class="btn" onclick="uploadImage()">تحميل الصورة</button>
        </form>
        
        <div id="result" style="margin-top: 20px;"></div>
    </div>

    <script>
        function uploadImage() {
            const fileInput = document.getElementById('imageInput');
            const resultDiv = document.getElementById('result');
            
            if (fileInput.files.length === 0) {
                resultDiv.innerHTML = '<p style="color: red;">الرجاء اختيار صورة أولاً</p>';
                return;
            }
            
            const file = fileInput.files[0];
            
            // عرض معلومات الملف
            resultDiv.innerHTML = `
                <h3>تم اختيار الصورة بنجاح</h3>
                <p><strong>اسم الملف:</strong> ${file.name}</p>
                <p><strong>نوع الملف:</strong> ${file.type}</p>
                <p><strong>حجم الملف:</strong> ${Math.round(file.size / 1024)} كيلوبايت</p>
                <p>الرجاء نسخ هذه الصورة إلى المجلد <code>static/img/</code> باسم <code>jordan_id_back.jpg</code></p>
            `;
            
            // عرض معاينة للصورة
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.style.maxWidth = '100%';
                img.style.marginTop = '10px';
                img.style.border = '1px solid #ddd';
                img.style.borderRadius = '5px';
                resultDiv.appendChild(img);
            };
            reader.readAsDataURL(file);
        }
    </script>
</body>
</html>
