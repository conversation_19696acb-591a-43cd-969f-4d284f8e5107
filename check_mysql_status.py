#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريپت فحص حالة MySQL
MySQL Status Check Script
"""

import subprocess
import socket
import sys

def check_mysql_service():
    """فحص خدمة MySQL في Windows"""
    print("فحص خدمة MySQL...")
    print("-" * 30)
    
    try:
        # فحص خدمات MySQL المختلفة
        mysql_services = ['MySQL80', 'MySQL', 'mysqld', 'MYSQL']
        
        for service in mysql_services:
            try:
                result = subprocess.run(
                    ['sc', 'query', service],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    if 'RUNNING' in result.stdout:
                        print(f"✓ خدمة {service} تعمل")
                        return True
                    else:
                        print(f"! خدمة {service} موجودة ولكن لا تعمل")
                        print("جرب تشغيلها من Services أو XAMPP")
                        
            except subprocess.TimeoutExpired:
                continue
            except Exception:
                continue
        
        print("✗ لم يتم العثور على خدمة MySQL نشطة")
        return False
        
    except Exception as e:
        print(f"✗ خطأ في فحص الخدمة: {str(e)}")
        return False

def check_mysql_port():
    """فحص منفذ MySQL (3306)"""
    print("\nفحص منفذ MySQL (3306)...")
    print("-" * 30)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 3306))
        sock.close()
        
        if result == 0:
            print("✓ منفذ MySQL (3306) مفتوح ومتاح")
            return True
        else:
            print("✗ منفذ MySQL (3306) غير متاح")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في فحص المنفذ: {str(e)}")
        return False

def check_mysql_client():
    """فحص عميل MySQL"""
    print("\nفحص عميل MySQL...")
    print("-" * 30)
    
    try:
        # فحص mysql command
        result = subprocess.run(
            ['mysql', '--version'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print(f"✓ عميل MySQL متوفر: {result.stdout.strip()}")
            return True
        else:
            print("✗ عميل MySQL غير متوفر")
            return False
            
    except FileNotFoundError:
        print("✗ أمر mysql غير موجود في PATH")
        return False
    except Exception as e:
        print(f"✗ خطأ في فحص العميل: {str(e)}")
        return False

def check_xampp():
    """فحص XAMPP"""
    print("\nفحص XAMPP...")
    print("-" * 30)
    
    xampp_paths = [
        'C:\\xampp\\mysql\\bin\\mysql.exe',
        'C:\\xampp\\apache\\bin\\httpd.exe',
        'C:\\Program Files\\XAMPP\\mysql\\bin\\mysql.exe'
    ]
    
    xampp_found = False
    for path in xampp_paths:
        try:
            import os
            if os.path.exists(path):
                print(f"✓ تم العثور على XAMPP في: {path}")
                xampp_found = True
                break
        except:
            continue
    
    if not xampp_found:
        print("! لم يتم العثور على XAMPP")
        print("إذا كان XAMPP مثبت، تأكد من المسار الصحيح")
    
    return xampp_found

def test_mysql_connection():
    """اختبار الاتصال بـ MySQL"""
    print("\nاختبار الاتصال بـ MySQL...")
    print("-" * 30)
    
    try:
        import mysql.connector
        
        connection = mysql.connector.connect(
            host='localhost',
            user='hr_user',
            password='hr_password_2024',
            database='hr_system_db',
            connect_timeout=5
        )
        
        if connection.is_connected():
            print("✓ تم الاتصال بقاعدة البيانات بنجاح!")
            
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"✓ إصدار MySQL: {version}")
            
            cursor.execute("SELECT DATABASE()")
            db_name = cursor.fetchone()[0]
            print(f"✓ قاعدة البيانات الحالية: {db_name}")
            
            connection.close()
            return True
        else:
            print("✗ فشل الاتصال بقاعدة البيانات")
            return False
            
    except ImportError:
        print("✗ مكتبة mysql.connector غير مثبتة")
        print("قم بتثبيتها: pip install mysql-connector-python")
        return False
    except Exception as e:
        print(f"✗ خطأ في الاتصال: {str(e)}")
        print("\nتأكد من:")
        print("1. تشغيل خدمة MySQL")
        print("2. إنشاء قاعدة البيانات والمستخدم")
        print("3. صحة معلومات الاتصال")
        return False

def provide_solutions():
    """تقديم حلول للمشاكل الشائعة"""
    print("\n" + "=" * 50)
    print("حلول للمشاكل الشائعة:")
    print("=" * 50)
    
    print("\n1. إذا لم تكن خدمة MySQL تعمل:")
    print("   - افتح XAMPP Control Panel وشغل MySQL")
    print("   - أو افتح Services وشغل خدمة MySQL")
    
    print("\n2. إذا لم يكن MySQL مثبت:")
    print("   - حمل وثبت XAMPP من: https://www.apachefriends.org/")
    print("   - أو حمل MySQL Server من: https://dev.mysql.com/downloads/")
    
    print("\n3. إذا لم تكن قاعدة البيانات منشأة:")
    print("   - افتح phpMyAdmin (http://localhost/phpmyadmin)")
    print("   - أو شغل: python setup_mysql.py")
    
    print("\n4. للعودة إلى SQLite:")
    print("   - شغل: python rollback_to_sqlite.py")

def main():
    """الدالة الرئيسية"""
    print("فحص حالة MySQL")
    print("=" * 20)
    
    all_checks = []
    
    # فحص الخدمة
    all_checks.append(check_mysql_service())
    
    # فحص المنفذ
    all_checks.append(check_mysql_port())
    
    # فحص العميل
    all_checks.append(check_mysql_client())
    
    # فحص XAMPP
    check_xampp()
    
    # اختبار الاتصال
    all_checks.append(test_mysql_connection())
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    if any(all_checks):
        print("✓ MySQL متوفر جزئياً أو كلياً")
        if all(all_checks):
            print("✓ جميع الفحوصات نجحت! MySQL جاهز للاستخدام")
        else:
            print("! بعض الفحوصات فشلت، راجع التفاصيل أعلاه")
    else:
        print("✗ MySQL غير متوفر أو غير مُعد بشكل صحيح")
        provide_solutions()
    
    print("=" * 50)

if __name__ == "__main__":
    main()
