{% extends 'base.html' %}
{% load static %}

{% block title %}المخطط الهيكلي - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .org-chart-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
        position: relative;
        overflow: hidden;
    }

    .org-chart-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .chart-content {
        position: relative;
        z-index: 1;
    }

    .page-title {
        text-align: center;
        color: white;
        margin-bottom: 3rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .page-title h1 {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 1rem;
    }

    .page-title p {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    /* Organizational Chart Styles */
    .org-chart {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }

    .org-level {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        gap: 2rem;
        width: 100%;
        flex-wrap: wrap;
    }

    .org-node {
        background: white;
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        text-align: center;
        position: relative;
        transition: all 0.3s ease;
        border: 3px solid transparent;
        min-width: 200px;
        max-width: 250px;
    }

    .org-node:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        border-color: #ffd700;
    }

    .org-node.director {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #333;
        font-weight: 800;
        font-size: 1.1rem;
        border: 3px solid #fff;
        box-shadow: 0 20px 40px rgba(255, 215, 0, 0.4);
    }

    .org-node.manager {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 700;
        border: 3px solid #fff;
    }

    .org-node.department {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .org-node.special-dept {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .org-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        display: block;
    }

    .org-title {
        font-weight: bold;
        margin-bottom: 0.5rem;
        line-height: 1.3;
    }

    .org-subtitle {
        font-size: 0.8rem;
        opacity: 0.8;
    }

    /* Connection Lines */
    .connection-line {
        width: 2px;
        height: 30px;
        background: white;
        margin: 0 auto;
        opacity: 0.7;
    }

    .connection-horizontal {
        height: 2px;
        background: white;
        opacity: 0.7;
        margin: 15px 0;
    }

    /* Department Sections */
    .departments-container {
        display: flex;
        justify-content: space-between;
        gap: 3rem;
        width: 100%;
        max-width: 1400px;
        margin: 0 auto;
    }

    .department-section {
        flex: 1;
        position: relative;
    }

    .department-section::before {
        content: '';
        position: absolute;
        top: -30px;
        left: 50%;
        transform: translateX(-50%);
        width: 2px;
        height: 30px;
        background: white;
        opacity: 0.7;
    }

    .section-title {
        text-align: center;
        color: white;
        margin-bottom: 2rem;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .departments-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        justify-items: center;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .departments-container {
            flex-direction: column;
            gap: 2rem;
        }

        .department-section::before {
            display: none;
        }

        .departments-grid {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        }
    }

    @media (max-width: 768px) {
        .org-level {
            flex-direction: column;
            align-items: center;
        }

        .org-node {
            min-width: 180px;
            max-width: 300px;
        }

        .page-title h1 {
            font-size: 2rem;
        }

        .departments-container {
            gap: 1.5rem;
        }

        .departments-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
    }

    /* Animation */
    .fade-in {
        opacity: 0;
        transform: translateY(30px);
        animation: fadeInUp 0.8s ease-out forwards;
    }

    @keyframes fadeInUp {
        from { 
            opacity: 0; 
            transform: translateY(30px) scale(0.95); 
        }
        to { 
            opacity: 1; 
            transform: translateY(0) scale(1); 
        }
    }

    .back-button {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid white;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 15px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .back-button:hover {
        background: white;
        color: #667eea;
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="org-chart-container">
    <!-- Back Button -->
    <a href="{% url 'home:home' %}" class="back-button">
        <i class="fas fa-arrow-right me-2"></i> العودة للرئيسية
    </a>

    <div class="container-fluid chart-content">
        <!-- Page Title -->
        <div class="page-title fade-in">
            <h1><i class="fas fa-sitemap me-3"></i>المخطط الهيكلي</h1>
            <p>الهيكل التنظيمي لمديرية التربية والتعليم</p>
        </div>

        <!-- Organizational Chart -->
        <div class="org-chart">
            <!-- Level 1: Director -->
            <div class="org-level fade-in" style="animation-delay: 0.2s;">
                <div class="org-node director">
                    <i class="fas fa-crown org-icon"></i>
                    <div class="org-title">المدير</div>
                    <div class="org-subtitle">مدير التربية والتعليم</div>
                </div>
            </div>

            <!-- Connection Line -->
            <div class="connection-line fade-in" style="animation-delay: 0.3s;"></div>

            <!-- Level 2: Deputy Directors and Direct Departments -->
            <div class="org-level fade-in" style="animation-delay: 0.4s;">
                <div class="org-node manager">
                    <i class="fas fa-user-tie org-icon"></i>
                    <div class="org-title">مدير الشؤون الإدارية والمالية</div>
                </div>
                
                <div class="org-node manager">
                    <i class="fas fa-graduation-cap org-icon"></i>
                    <div class="org-title">مدير الشؤون التعليمية</div>
                </div>
                
                <div class="org-node special-dept">
                    <i class="fas fa-building org-icon"></i>
                    <div class="org-title">الديوان</div>
                </div>
                
                <div class="org-node special-dept">
                    <i class="fas fa-bullhorn org-icon"></i>
                    <div class="org-title">الإعلام</div>
                </div>
            </div>

            <!-- Connection Lines -->
            <div class="connection-line fade-in" style="animation-delay: 0.5s;"></div>

            <!-- Level 3: Departments Side by Side -->
            <div class="departments-container fade-in" style="animation-delay: 0.6s;">
                <!-- Administrative and Financial Departments -->
                <div class="department-section">
                    <h4 class="section-title">أقسام الشؤون الإدارية والمالية</h4>
                    <div class="departments-grid">
                        <div class="org-node department">
                            <i class="fas fa-users org-icon"></i>
                            <div class="org-title">قسم شؤون الموظفين</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-chart-line org-icon"></i>
                            <div class="org-title">قسم التخطيط التربوي</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-dollar-sign org-icon"></i>
                            <div class="org-title">قسم الشؤون المالية</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-truck org-icon"></i>
                            <div class="org-title">قسم اللوازم والكتب والنقليات</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-hammer org-icon"></i>
                            <div class="org-title">قسم الأبنية المدرسية</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-search-dollar org-icon"></i>
                            <div class="org-title">قسم التدقيق المالي</div>
                        </div>
                    </div>
                </div>

                <!-- Educational Departments -->
                <div class="department-section">
                    <h4 class="section-title">أقسام الشؤون التعليمية</h4>
                    <div class="departments-grid">
                        <div class="org-node department">
                            <i class="fas fa-book org-icon"></i>
                            <div class="org-title">قسم التعليم العام وشؤون الطلبة</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-tools org-icon"></i>
                            <div class="org-title">قسم التعليم المهني والإنتاج</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-heart org-icon"></i>
                            <div class="org-title">قسم التعليم الخاص</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-clipboard-check org-icon"></i>
                            <div class="org-title">قسم الامتحانات والاختبارات</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-eye org-icon"></i>
                            <div class="org-title">قسم الإشراف التربوي</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-laptop org-icon"></i>
                            <div class="org-title">قسم تكنولوجيا التعليم والمعلومات</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-theater-masks org-icon"></i>
                            <div class="org-title">قسم النشاطات المدرسية</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-comments org-icon"></i>
                            <div class="org-title">قسم الإرشاد المدرسي</div>
                        </div>

                        <div class="org-node department">
                            <i class="fas fa-dumbbell org-icon"></i>
                            <div class="org-title">الصالة الرياضية</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add smooth scrolling
    $('html, body').css({
        'scroll-behavior': 'smooth'
    });
    
    // Add hover effects for better interactivity
    $('.org-node').hover(
        function() {
            $(this).css('transform', 'translateY(-10px) scale(1.05)');
        },
        function() {
            $(this).css('transform', 'translateY(0) scale(1)');
        }
    );
});
</script>
{% endblock %}
