{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if is_update %}تعديل الدرجة الوظيفية{% else %}إضافة درجة وظيفية{% endif %}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 10px;
    }
    
    .form-container {
        background: white;
        border-radius: 10px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .form-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #667eea;
    }
    
    .section-title {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        margin-left: 0.5rem;
    }
    
    .employee-info {
        background: #e3f2fd;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #bbdefb;
    }
    
    .employee-info.success {
        background: #e8f5e8;
        border-color: #c8e6c9;
        color: #2e7d32;
    }
    
    .search-button {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
    
    .ministry-input {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    
    .btn-action {
        min-width: 120px;
    }
    
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
    
    .form-floating > label {
        right: 0;
        left: auto;
        transform-origin: 100% 0;
    }
    
    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label {
        transform: scale(.85) translateY(-0.5rem) translateX(.15rem);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1 class="mb-3">
            <i class="fas fa-medal"></i>
            {% if is_update %}تعديل الدرجة الوظيفية{% else %}إضافة درجة وظيفية جديدة{% endif %}
        </h1>
        <p class="lead mb-0">
            {% if is_update %}
                تعديل بيانات الدرجة الوظيفية للموظف
            {% else %}
                إضافة درجة وظيفية جديدة لموظف
            {% endif %}
        </p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                <form method="post" id="gradeForm">
                    {% csrf_token %}

                    <!-- Hidden fields -->
                    {{ form.employee }}
                    <input type="hidden" name="employee_id" id="id_employee_id" value="{{ form.employee_id.value|default:'' }}">
                    
                    <!-- Employee Search Section -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-search"></i>
                            بحث الموظف
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text"
                                           name="ministry_number"
                                           id="id_ministry_number"
                                           class="form-control ministry-input"
                                           placeholder="أدخل الرقم الوزاري"
                                           value="{{ form.ministry_number.value|default:'' }}"
                                           required>
                                    <button type="button" class="btn btn-primary search-button" id="searchBtn">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                                <small class="form-text text-muted">أدخل الرقم الوزاري واضغط بحث أو Enter</small>
                            </div>
                        </div>
                        
                        <!-- Employee Info Display -->
                        <div id="employeeInfo" class="employee-info mt-3" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>اسم الموظف:</strong>
                                    <span id="employeeName">-</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>التخصص:</strong>
                                    <span id="employeeSpecialization">-</span>
                                </div>
                                <div class="col-md-6 mt-2">
                                    <strong>القسم:</strong>
                                    <span id="employeeDepartment">-</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Hidden fields -->
                        {{ form.employee_id }}
                        {{ form.employee }}
                        
                        <!-- Employee Name Display Field -->
                        <div class="mt-3">
                            <div class="form-floating">
                                <input type="text"
                                       name="employee_name"
                                       id="id_employee_name"
                                       class="form-control"
                                       readonly
                                       placeholder="سيتم عرض اسم الموظف هنا"
                                       value="{{ form.employee_name.value|default:'' }}">
                                <label for="id_employee_name" class="required-field">اسم الموظف</label>
                            </div>
                        </div>
                    </div>

                    <!-- Grade Information Section -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-medal"></i>
                            معلومات الدرجة الوظيفية
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ form.grade }}
                                    <label for="{{ form.grade.id_for_label }}" class="required-field">{{ form.grade.label }}</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ form.promotion_type }}
                                    <label for="{{ form.promotion_type.id_for_label }}">{{ form.promotion_type.label }}</label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ form.years_in_grade }}
                                    <label for="{{ form.years_in_grade.id_for_label }}">{{ form.years_in_grade.label }}</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ form.grade_date }}
                                    <label for="{{ form.grade_date.id_for_label }}" class="required-field">{{ form.grade_date.label }}</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            {{ form.notes }}
                            <label for="{{ form.notes.id_for_label }}">{{ form.notes.label }}</label>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'employees:employee_grades_list' %}" class="btn btn-outline-secondary btn-action">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                        <div>
                            {% if is_update %}
                            <button type="submit" class="btn btn-primary btn-action">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            {% else %}
                            <button type="submit" class="btn btn-success btn-action">
                                <i class="fas fa-plus"></i> إضافة الدرجة
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    console.log('jQuery loaded:', typeof $ !== 'undefined');
    console.log('Document ready fired');
    // Initialize Select2 for grade dropdown
    $('#id_grade').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر الدرجة الوظيفية',
        allowClear: true
    });

    // Initialize Select2 for promotion type dropdown
    $('#id_promotion_type').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر نوع الترفيع',
        allowClear: true
    });

    // Employee search functionality
    function searchEmployee() {
        const ministryNumber = $('#id_ministry_number').val().trim();
        console.log('Searching for ministry number:', ministryNumber);

        if (!ministryNumber) {
            alert('الرجاء إدخال الرقم الوزاري');
            return;
        }

        // Show loading state
        $('#searchBtn').html('<i class="fas fa-spinner fa-spin"></i> جاري البحث...');
        $('#searchBtn').prop('disabled', true);

        console.log('Making AJAX request to:', '{% url "employees:search_employee_for_grade" %}');

        $.ajax({
            url: '{% url "employees:search_employee_for_grade" %}',
            type: 'GET',
            data: {
                'ministry_number': ministryNumber
            },
            dataType: 'json',
            success: function(response) {
                console.log('Search response:', response); // للتشخيص

                if (response.success) {
                    // Update employee info display
                    $('#employeeName').text(response.employee.full_name || 'غير محدد');
                    $('#employeeSpecialization').text(response.employee.specialization || 'غير محدد');
                    $('#employeeDepartment').text(response.employee.department || 'غير محدد');

                    // Update form fields
                    $('#id_employee_id').val(response.employee.id);
                    $('#id_employee_name').val(response.employee.full_name || '');

                    // Show employee info with success styling
                    $('#employeeInfo').removeClass().addClass('employee-info success').show();

                    // Add visual feedback
                    $('#id_employee_name').css({
                        'background-color': '#e8f5e8',
                        'border-color': '#28a745',
                        'color': '#155724'
                    });

                    // Show success message
                    console.log('تم العثور على الموظف بنجاح');

                } else {
                    alert(response.error || 'لم يتم العثور على الموظف');
                    $('#employeeInfo').hide();
                    $('#id_employee_id').val('');
                    $('#id_employee_name').val('').css({
                        'background-color': '',
                        'border-color': '',
                        'color': ''
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error Details:');
                console.error('XHR:', xhr);
                console.error('Status:', status);
                console.error('Error:', error);
                console.error('Response Text:', xhr.responseText);

                let errorMessage = 'حدث خطأ أثناء البحث عن الموظف';

                try {
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    } else if (xhr.responseText) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.error) {
                            errorMessage = response.error;
                        }
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                }

                alert(errorMessage);
                $('#employeeInfo').hide();
                $('#id_employee_id').val('');
                $('#id_employee_name').val('');
            },
            complete: function() {
                // Reset button state
                $('#searchBtn').html('<i class="fas fa-search"></i> بحث');
                $('#searchBtn').prop('disabled', false);
            }
        });
    }

    // Check if elements exist
    console.log('Search button exists:', $('#searchBtn').length > 0);
    console.log('Ministry number input exists:', $('#id_ministry_number').length > 0);

    // Search button click event
    $('#searchBtn').click(function(e) {
        e.preventDefault();
        console.log('Search button clicked');
        searchEmployee();
    });

    // Enter key press event for ministry number input
    $('#id_ministry_number').keypress(function(e) {
        console.log('Key pressed:', e.which);
        if (e.which === 13) { // Enter key
            e.preventDefault();
            console.log('Enter key pressed, searching...');
            searchEmployee();
        }
    });

    // Form validation
    $('#gradeForm').submit(function(e) {
        const employeeId = $('#id_employee_id').val();
        const grade = $('#id_grade').val();
        const gradeDate = $('#id_grade_date').val();

        if (!employeeId) {
            e.preventDefault();
            alert('الرجاء البحث عن الموظف أولاً');
            $('#id_ministry_number').focus();
            return false;
        }

        if (!grade) {
            e.preventDefault();
            alert('الرجاء اختيار الدرجة الوظيفية');
            $('#id_grade').focus();
            return false;
        }

        if (!gradeDate) {
            e.preventDefault();
            alert('الرجاء إدخال تاريخ الحلول بالدرجة');
            $('#id_grade_date').focus();
            return false;
        }
    });

    // Auto-focus on ministry number input
    {% if not is_update %}
    $('#id_ministry_number').focus();
    {% endif %}

    // If editing existing grade, show employee info
    {% if is_update and grade %}
    $('#employeeInfo').addClass('success').show();
    $('#employeeName').text('{{ grade.employee.full_name }}');
    $('#employeeSpecialization').text('{{ grade.employee.specialization|default:"غير محدد" }}');
    $('#employeeDepartment').text('{{ grade.employee.school|default:"غير محدد" }}');
    $('#id_employee_name').css({
        'background-color': '#e8f5e8',
        'border-color': '#28a745',
        'color': '#155724'
    });
    {% endif %}
});
</script>
{% endblock %}
