#!/usr/bin/env python3
"""
فحص جميع ملفات Python في المشروع لاستخراج المكتبات المستخدمة
"""

import os
import ast
import sys
from pathlib import Path

def extract_imports_from_file(file_path):
    """استخراج جميع imports من ملف Python"""
    imports = set()
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # تحليل الكود باستخدام AST
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.add(alias.name.split('.')[0])
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.add(node.module.split('.')[0])
    
    except Exception as e:
        print(f"خطأ في تحليل الملف {file_path}: {e}")
    
    return imports

def scan_project():
    """فحص جميع ملفات Python في المشروع"""
    
    # المجلدات المستثناة
    exclude_dirs = {
        'venv', '__pycache__', '.git', 'staticfiles', 
        'media', 'node_modules', '.pytest_cache'
    }
    
    # المكتبات المدمجة في Python (لا نحتاج لتثبيتها)
    builtin_modules = {
        'os', 'sys', 'json', 'datetime', 'time', 'random', 'math',
        'collections', 'itertools', 'functools', 'operator', 'typing',
        'pathlib', 'urllib', 'http', 'email', 'html', 'xml', 'csv',
        'sqlite3', 'hashlib', 'uuid', 'base64', 'pickle', 'copy',
        'threading', 'multiprocessing', 'subprocess', 'platform',
        'logging', 'warnings', 'traceback', 'inspect', 'ast',
        'importlib', 'pkgutil', 'zipfile', 'tarfile', 'gzip',
        'shutil', 'tempfile', 'glob', 're', 'string', 'io',
        'decimal', 'fractions', 'statistics', 'calendar'
    }
    
    all_imports = set()
    
    # فحص جميع ملفات Python
    for root, dirs, files in os.walk('.'):
        # إزالة المجلدات المستثناة
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                imports = extract_imports_from_file(file_path)
                all_imports.update(imports)
    
    # إزالة المكتبات المدمجة
    external_imports = all_imports - builtin_modules
    
    # إزالة أسماء التطبيقات المحلية
    local_apps = {
        'employees', 'employment', 'leaves', 'ranks', 'reports',
        'home', 'accounts', 'notifications', 'system_logs', 'backup',
        'file_management', 'disciplinary', 'performance', 'announcements',
        'hr_system'
    }
    
    external_imports = external_imports - local_apps
    
    return sorted(external_imports)

def main():
    print("=" * 60)
    print("فحص المكتبات المستخدمة في المشروع")
    print("=" * 60)
    
    imports = scan_project()
    
    print(f"\nالمكتبات الخارجية المستخدمة ({len(imports)}):")
    print("-" * 40)
    
    for imp in imports:
        print(f"- {imp}")
    
    print(f"\nإجمالي المكتبات الخارجية: {len(imports)}")
    
    # مقارنة مع requirements.txt
    print("\n" + "=" * 60)
    print("مقارنة مع requirements.txt")
    print("=" * 60)
    
    if os.path.exists('requirements.txt'):
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            req_lines = f.readlines()
        
        req_packages = set()
        for line in req_lines:
            line = line.strip()
            if line and not line.startswith('#'):
                if '==' in line:
                    package = line.split('==')[0].strip()
                elif '>=' in line:
                    package = line.split('>=')[0].strip()
                elif '<=' in line:
                    package = line.split('<=')[0].strip()
                else:
                    package = line.strip()
                
                # تنظيف اسم المكتبة
                package = package.replace('-', '_').lower()
                req_packages.add(package)
        
        # تحويل أسماء imports لنفس التنسيق
        normalized_imports = {imp.replace('-', '_').lower() for imp in imports}
        
        missing_in_req = normalized_imports - req_packages
        extra_in_req = req_packages - normalized_imports
        
        if missing_in_req:
            print(f"\nمكتبات مستخدمة لكن غير موجودة في requirements.txt ({len(missing_in_req)}):")
            for pkg in sorted(missing_in_req):
                print(f"- {pkg}")
        
        if extra_in_req:
            print(f"\nمكتبات في requirements.txt لكن غير مستخدمة ({len(extra_in_req)}):")
            for pkg in sorted(extra_in_req):
                print(f"- {pkg}")
        
        if not missing_in_req and not extra_in_req:
            print("\n✅ جميع المكتبات المستخدمة موجودة في requirements.txt")
    
    else:
        print("\n❌ ملف requirements.txt غير موجود")

if __name__ == "__main__":
    main()
