from django.db import models
from django.utils.translation import gettext_lazy as _

class Employee(models.Model):
    """Model for storing employee personal information"""
    GENDER_CHOICES = [
        ('male', _('ذكر')),
        ('female', _('أنثى')),
    ]

    ministry_number = models.Char<PERSON>ield(_('Ministry Number'), max_length=20, unique=True)
    national_id = models.Char<PERSON><PERSON>(_('National ID'), max_length=20, unique=True)
    full_name = models.Char<PERSON><PERSON>(_('Full Name'), max_length=255)
    gender = models.Char<PERSON><PERSON>(_('Gender'), max_length=10, choices=GENDER_CHOICES, default='male')
    qualification = models.Char<PERSON>ield(_('Qualification'), max_length=255)
    post_graduate_diploma = models.Char<PERSON>ield(_('Post Graduate Diploma'), max_length=255, blank=True, null=True)
    masters_degree = models.Char<PERSON>ield(_('Masters Degree'), max_length=255, blank=True, null=True)
    phd_degree = models.Cha<PERSON><PERSON><PERSON>(_('PhD Degree'), max_length=255, blank=True, null=True)
    specialization = models.Char<PERSON>ield(_('Specialization'), max_length=255)
    hire_date = models.DateField(_('Hire Date'))
    school = models.CharField(_('Department'), max_length=255)
    birth_date = models.DateField(_('Birth Date'))
    address = models.TextField(_('Address'))
    phone_number = models.CharField(_('Phone Number'), max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Employee')
        verbose_name_plural = _('Employees')
        ordering = ['ministry_number']

    def __str__(self):
        return f"{self.full_name} ({self.ministry_number})"


class TeacherSpecialtyAssignment(models.Model):
    """Model for teachers assigned to teach different specialties"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='specialty_assignments', verbose_name='المعلم')
    original_specialty = models.CharField(max_length=100, verbose_name='التخصص الأصلي')
    assigned_specialty = models.CharField(max_length=100, verbose_name='التخصص المحمل عليه')
    department = models.CharField(max_length=100, verbose_name='القسم')
    subjects_taught = models.TextField(verbose_name='المواد التي يدرسها')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    assignment_date = models.DateField(verbose_name='تاريخ التحميل')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'معلم محمل على تخصص آخر'
        verbose_name_plural = 'معلمين محملين على تخصصات أخرى'
        ordering = ['-assignment_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.assigned_specialty}"

    def get_latest_position(self):
        """Get the latest position for this employee"""
        # First check if there's a position in the employee_position table
        latest_position = self.positions.order_by('-date_obtained').first()
        if latest_position:
            return latest_position.position.name

        # If no position in employee_position, check employment
        from employment.models import Employment
        current_employment = Employment.objects.filter(employee=self).first()
        if current_employment and current_employment.position:
            return current_employment.position.name

        return '-'

    def get_latest_position_with_date(self):
        """Get the latest position with date for this employee"""
        # First check if there's a position in the employee_position table
        latest_position = self.positions.order_by('-date_obtained').first()
        if latest_position:
            return f"{latest_position.position.name} ({latest_position.date_obtained.strftime('%Y-%m-%d')})"

        # If no position in employee_position, check employment
        from employment.models import Employment
        current_employment = Employment.objects.filter(employee=self).first()
        if current_employment and current_employment.position:
            return f"{current_employment.position.name} ({current_employment.start_date.strftime('%Y-%m-%d')})"

        return '-'

    def get_current_department(self):
        """Get the current department for this employee"""
        from employment.models import Employment
        current_employment = Employment.objects.filter(employee=self, is_current=True).first()
        if current_employment and current_employment.department:
            return current_employment.department.name
        return '-'

class AnnualReport(models.Model):
    """Model for storing employee annual reports"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='annual_reports')
    year = models.PositiveIntegerField(_('Year'))
    score = models.DecimalField(_('Score'), max_digits=5, decimal_places=2)
    notes = models.TextField(_('Notes'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Annual Report')
        verbose_name_plural = _('Annual Reports')
        ordering = ['-year']
        unique_together = ['employee', 'year']

    def __str__(self):
        return f"{self.employee.full_name} - {self.year}"

class Penalty(models.Model):
    """Model for storing employee penalties"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='penalties')
    date = models.DateField(_('Date'))
    description = models.TextField(_('Description'))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Penalty')
        verbose_name_plural = _('Penalties')
        ordering = ['-date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.date}"


class RetiredEmployee(models.Model):
    """Model for storing retired employees"""
    employee = models.OneToOneField(Employee, on_delete=models.CASCADE, related_name='retirement')
    retirement_date = models.DateField(_('Retirement Date'))
    retirement_reason = models.CharField(_('Retirement Reason'), max_length=255, default='تقاعد عادي')
    notes = models.TextField(_('Notes'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Retired Employee')
        verbose_name_plural = _('Retired Employees')
        ordering = ['-retirement_date']

    def __str__(self):
        return f"{self.employee.full_name} - متقاعد في {self.retirement_date}"


class ExternalTransfer(models.Model):
    """Model for externally transferred employees"""
    employee = models.OneToOneField(
        Employee, 
        on_delete=models.CASCADE, 
        related_name='external_transfer',
        verbose_name=_('Employee')
    )
    transfer_date = models.DateField(
        _('Transfer Date'),
        help_text=_('Date when the employee was transferred')
    )
    destination_directorate = models.CharField(
        _('Destination Directorate'),
        max_length=200,
        help_text=_('The directorate the employee was transferred to')
    )
    transfer_reason = models.CharField(
        _('Transfer Reason'),
        max_length=200,
        default='نقل خارجي',
        help_text=_('Reason for the transfer')
    )
    notes = models.TextField(
        _('Notes'),
        blank=True,
        null=True,
        help_text=_('Additional notes about the transfer')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('External Transfer')
        verbose_name_plural = _('External Transfers')
        ordering = ['-transfer_date']

    def __str__(self):
        return f"{self.employee.full_name} - منقول إلى {self.destination_directorate} ({self.transfer_date})"


class MaternityLeave(models.Model):
    """Model for storing maternity leaves"""
    employee = models.ForeignKey(
        Employee, 
        on_delete=models.CASCADE, 
        related_name='maternity_leaves',
        verbose_name=_('Employee')
    )
    start_date = models.DateField(
        _('Start Date'),
        help_text=_('Start date of maternity leave')
    )
    end_date = models.DateField(
        _('End Date'),
        help_text=_('End date of maternity leave (automatically calculated)')
    )
    is_active = models.BooleanField(
        _('Is Active'),
        default=True,
        help_text=_('Whether the maternity leave is currently active')
    )
    notes = models.TextField(
        _('Notes'),
        blank=True,
        null=True,
        help_text=_('Additional notes about the maternity leave')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Maternity Leave')
        verbose_name_plural = _('Maternity Leaves')
        ordering = ['-start_date']

    def __str__(self):
        status = "نشطة" if self.is_active else "منتهية"
        return f"{self.employee.full_name} - إجازة أمومة ({status}) - {self.start_date}"

    def save(self, *args, **kwargs):
        """Override save to automatically calculate end date"""
        if self.start_date and not self.end_date:
            from datetime import timedelta
            # Add 90 days to start date (calculation starts from next day)
            self.end_date = self.start_date + timedelta(days=90)
        
        # Check if leave has ended
        from datetime import date
        if self.end_date and date.today() > self.end_date:
            self.is_active = False
            if not self.notes or 'الإجازة انتهت' not in self.notes:
                if self.notes:
                    self.notes += '\n\nالإجازة انتهت تلقائياً في ' + str(self.end_date)
                else:
                    self.notes = 'الإجازة انتهت تلقائياً في ' + str(self.end_date)
        
        super().save(*args, **kwargs)


class InternalTransfer(models.Model):
    """Model for tracking internal department transfers"""
    employee = models.ForeignKey(
        Employee, 
        on_delete=models.CASCADE, 
        related_name='internal_transfers',
        verbose_name=_('Employee')
    )
    previous_department = models.CharField(
        _('Previous Department'),
        max_length=255,
        help_text=_('Department the employee transferred from')
    )
    new_department = models.CharField(
        _('New Department'),
        max_length=255,
        help_text=_('Department the employee transferred to')
    )
    transfer_date = models.DateField(
        _('Transfer Date'),
        help_text=_('Date when the transfer occurred')
    )
    start_date = models.DateField(
        _('Start Date'),
        help_text=_('Date when employee started working in new department')
    )
    notes = models.TextField(
        _('Notes'),
        blank=True,
        null=True,
        help_text=_('Additional notes about the transfer')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Internal Transfer')
        verbose_name_plural = _('Internal Transfers')
        ordering = ['-transfer_date']

    def __str__(self):
        return f"{self.employee.full_name} - من {self.previous_department} إلى {self.new_department} ({self.transfer_date})"


class EmployeeAssignment(models.Model):
    """Model for tracking employee assignments to specific tasks/roles"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='assignments', verbose_name="الموظف")
    department = models.CharField(max_length=100, verbose_name="القسم")
    assigned_role = models.CharField(max_length=200, verbose_name="الوظيفة المكلف بها")
    assignment_date = models.DateField(verbose_name="تاريخ التكليف")
    end_date = models.DateField(null=True, blank=True, verbose_name="تاريخ انتهاء التكليف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "تكليف موظف"
        verbose_name_plural = "تكليفات الموظفين"
        ordering = ['-assignment_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.assigned_role}"


class SharedEmployee(models.Model):
    """Model for tracking employees shared between multiple schools/departments"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='shared_assignments', verbose_name="الموظف")
    original_department = models.CharField(max_length=100, verbose_name="القسم الأصلي")
    shared_department = models.CharField(max_length=100, verbose_name="القسم المشترك")
    sharing_date = models.DateField(verbose_name="تاريخ الإشراك")
    end_date = models.DateField(null=True, blank=True, verbose_name="تاريخ انتهاء الإشراك")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "موظف مشترك"
        verbose_name_plural = "الموظفين المشتركين"
        ordering = ['-sharing_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.shared_department}"


class JobGrade(models.Model):
    """Model for job grades"""
    name = models.CharField(max_length=100, unique=True, verbose_name="اسم الدرجة")
    description = models.TextField(blank=True, verbose_name="الوصف")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "الدرجة الوظيفية"
        verbose_name_plural = "الدرجات الوظيفية"
        ordering = ['name']

    def __str__(self):
        return self.name


class PromotionType(models.Model):
    """Model for promotion types"""
    name = models.CharField(max_length=100, unique=True, verbose_name="نوع الترفيع")
    description = models.TextField(blank=True, verbose_name="الوصف")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "نوع الترفيع"
        verbose_name_plural = "أنواع الترفيع"
        ordering = ['name']

    def __str__(self):
        return self.name


class EmployeeGrade(models.Model):
    """Model for tracking employee job grades"""
    GRADE_CHOICES = [
        ('grade_9', 'الدرجة التاسعة'),
        ('grade_8', 'الدرجة الثامنة'),
        ('grade_7', 'الدرجة السابعة'),
        ('grade_6', 'الدرجة السادسة'),
        ('grade_5', 'الدرجة الخامسة'),
        ('grade_4', 'الدرجة الرابعة'),
        ('grade_3', 'الدرجة الثالثة'),
        ('grade_2', 'الدرجة الثانية'),
        ('grade_1', 'الدرجة الأولى'),
        ('special_grade', 'الدرجة الخاصة'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='grades', verbose_name="الموظف")
    grade = models.CharField(max_length=20, choices=GRADE_CHOICES, verbose_name="الدرجة الوظيفية")
    promotion_type = models.ForeignKey(PromotionType, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="نوع الترفيع")
    years_in_grade = models.PositiveIntegerField(default=0, verbose_name="السنة بالدرجة")
    grade_date = models.DateField(verbose_name="تاريخ الحلول بالدرجة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "الدرجة الوظيفية"
        verbose_name_plural = "الدرجات الوظيفية"
        ordering = ['-grade_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_grade_display()}"
