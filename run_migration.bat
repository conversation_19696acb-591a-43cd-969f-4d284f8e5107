@echo off
echo Running database migration...

REM Try to activate virtual environment
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
)

REM Run the migration
python manage.py migrate leaves

REM If migration fails, try to run our custom script
if %errorlevel% neq 0 (
    echo Migration failed, trying custom script...
    python manage.py shell < update_database.py
)

echo Done!
pause
