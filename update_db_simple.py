#!/usr/bin/env python3
"""
تحديث قاعدة البيانات لإضافة حقل عدد الساعات
Simple database update to add hours field
"""

import sqlite3
import os

def update_database():
    """تحديث قاعدة البيانات"""
    
    # مسار قاعدة البيانات
    db_paths = [
        'mysql_data/hr_system_db.sqlite3',
        'db.sqlite3',
        'hr_system_db.sqlite3'
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ لم يتم العثور على قاعدة البيانات")
        return False
    
    print(f"📍 استخدام قاعدة البيانات: {db_path}")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ranks_course';")
        if not cursor.fetchone():
            print("❌ جدول ranks_course غير موجود")
            return False
        
        # التحقق من وجود الحقل
        cursor.execute("PRAGMA table_info(ranks_course);")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'hours' in columns:
            print("ℹ️ حقل عدد الساعات موجود مسبقاً")
        else:
            print("🔧 إضافة حقل عدد الساعات...")
            cursor.execute("ALTER TABLE ranks_course ADD COLUMN hours INTEGER DEFAULT 0;")
            print("✅ تم إضافة حقل عدد الساعات بنجاح!")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("🎉 تم تحديث قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    print("🔧 تحديث قاعدة البيانات لإضافة حقل عدد الساعات")
    print("=" * 50)
    
    success = update_database()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ تم التحديث بنجاح!")
        print("🚀 يمكنك الآن استخدام حقل عدد الساعات في الدورات")
    else:
        print("❌ فشل في التحديث!")
    
    input("\n📱 اضغط Enter للخروج...")
