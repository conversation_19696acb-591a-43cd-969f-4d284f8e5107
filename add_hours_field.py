#!/usr/bin/env python3
"""
إضافة حقل عدد الساعات إلى جدول الدورات
Add hours field to courses table
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

from django.db import connection

def add_hours_field():
    """إضافة حقل عدد الساعات إلى جدول الدورات"""
    
    with connection.cursor() as cursor:
        try:
            # التحقق من وجود الحقل
            cursor.execute("PRAGMA table_info(ranks_course);")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'hours' not in columns:
                print("🔧 إضافة حقل عدد الساعات...")
                cursor.execute("ALTER TABLE ranks_course ADD COLUMN hours INTEGER DEFAULT 0;")
                print("✅ تم إضافة حقل عدد الساعات بنجاح!")
            else:
                print("ℹ️ حقل عدد الساعات موجود مسبقاً")
                
        except Exception as e:
            print(f"❌ خطأ في إضافة الحقل: {e}")

if __name__ == "__main__":
    add_hours_field()
