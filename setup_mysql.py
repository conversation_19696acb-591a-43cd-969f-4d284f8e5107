#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت إعداد قاعدة بيانات MySQL
MySQL Database Setup Script
"""

import mysql.connector
from mysql.connector import Error
import sys
import getpass

def create_database_and_user():
    """إنشاء قاعدة البيانات والمستخدم"""
    
    # معلومات الاتصال
    db_config = {
        'database_name': 'hr_system_db',
        'username': 'hr_user',
        'password': 'hr_password_2024',
        'host': 'localhost',
        'port': 3306
    }
    
    try:
        # طلب معلومات root من المستخدم
        print("إعداد قاعدة بيانات MySQL لنظام الموارد البشرية")
        print("=" * 50)
        
        root_password = getpass.getpass("أدخل كلمة مرور MySQL root: ")
        
        # الاتصال بـ MySQL كـ root
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password=root_password
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            print("تم الاتصال بـ MySQL بنجاح!")
            
            # إنشاء قاعدة البيانات
            print("إنشاء قاعدة البيانات...")
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_config['database_name']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"تم إنشاء قاعدة البيانات: {db_config['database_name']}")
            
            # إنشاء المستخدم
            print("إنشاء المستخدم...")
            cursor.execute(f"CREATE USER IF NOT EXISTS '{db_config['username']}'@'{db_config['host']}' IDENTIFIED BY '{db_config['password']}'")
            print(f"تم إنشاء المستخدم: {db_config['username']}")
            
            # منح الصلاحيات
            print("منح الصلاحيات...")
            cursor.execute(f"GRANT ALL PRIVILEGES ON {db_config['database_name']}.* TO '{db_config['username']}'@'{db_config['host']}'")
            cursor.execute("FLUSH PRIVILEGES")
            print("تم منح جميع الصلاحيات")
            
            # التحقق من إنشاء قاعدة البيانات
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            db_exists = any(db_config['database_name'] in db for db in databases)
            
            if db_exists:
                print(f"✓ تم التحقق من وجود قاعدة البيانات: {db_config['database_name']}")
            else:
                print(f"✗ خطأ: لم يتم العثور على قاعدة البيانات: {db_config['database_name']}")
                return False
            
            print("\n" + "=" * 50)
            print("تم إعداد قاعدة البيانات بنجاح!")
            print("معلومات الاتصال:")
            print(f"  قاعدة البيانات: {db_config['database_name']}")
            print(f"  المستخدم: {db_config['username']}")
            print(f"  كلمة المرور: {db_config['password']}")
            print(f"  الخادم: {db_config['host']}")
            print(f"  المنفذ: {db_config['port']}")
            print("=" * 50)
            
            return True
            
    except Error as e:
        print(f"خطأ في MySQL: {e}")
        return False
    except Exception as e:
        print(f"خطأ عام: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("تم إغلاق الاتصال بـ MySQL")

def test_connection():
    """اختبار الاتصال بقاعدة البيانات الجديدة"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            database='hr_system_db',
            user='hr_user',
            password='hr_password_2024'
        )
        
        if connection.is_connected():
            print("✓ تم اختبار الاتصال بنجاح!")
            return True
    except Error as e:
        print(f"✗ فشل اختبار الاتصال: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            connection.close()

if __name__ == "__main__":
    print("بدء إعداد MySQL...")
    
    if create_database_and_user():
        print("\nاختبار الاتصال...")
        if test_connection():
            print("\nتم إعداد MySQL بنجاح! يمكنك الآن تحديث إعدادات Django.")
        else:
            print("\nتم إنشاء قاعدة البيانات ولكن فشل اختبار الاتصال.")
            sys.exit(1)
    else:
        print("\nفشل في إعداد قاعدة البيانات.")
        sys.exit(1)
