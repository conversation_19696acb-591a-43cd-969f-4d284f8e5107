{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة دورة جديدة - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: #f8f9fc;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 25px;
        margin-bottom: 25px;
    }
    .form-section h5 {
        color: #5a5c69;
        border-bottom: 2px solid #e3e6f0;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    .required-field {
        color: #e74a3b;
    }
    .form-control:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
    .btn-primary {
        background: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
        border: none;
    }
    .btn-primary:hover {
        background: linear-gradient(180deg, #224abe 10%, #1e3a8a 100%);
    }
    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-graduation-cap me-2"></i>إضافة دورة جديدة</h2>
    <a href="{% url 'ranks:course_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة لأسماء الدورات
    </a>
</div>

<!-- عرض الرسائل -->
{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {% if message.tags == 'error' %}
                <i class="fas fa-exclamation-triangle me-2"></i>
            {% elif message.tags == 'success' %}
                <i class="fas fa-check-circle me-2"></i>
            {% elif message.tags == 'warning' %}
                <i class="fas fa-exclamation-circle me-2"></i>
            {% else %}
                <i class="fas fa-info-circle me-2"></i>
            {% endif %}
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}
{% endif %}

<div class="card shadow mb-4">
    <div class="card-header py-3 bg-primary text-white">
        <h6 class="m-0 font-weight-bold">
            <i class="fas fa-plus-circle me-2"></i>
            بيانات الدورة الجديدة
        </h6>
    </div>
    <div class="card-body">
        <form method="post" id="courseForm">
            {% csrf_token %}
            
            <div class="form-section">
                <h5><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                <i class="fas fa-graduation-cap me-1"></i>
                                اسم الدورة <span class="required-field">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.name.errors %}
                                        <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="help-text">
                                <i class="fas fa-info-circle me-1"></i>
                                أدخل اسم الدورة التدريبية (مثال: دورة الحاسوب الأساسي)
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="{{ form.hours.id_for_label }}" class="form-label">
                                <i class="fas fa-clock me-1"></i>
                                عدد الساعات
                            </label>
                            {{ form.hours }}
                            {% if form.hours.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.hours.errors %}
                                        <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="help-text">
                                <i class="fas fa-info-circle me-1"></i>
                                عدد ساعات الدورة (0-1000)
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-calendar me-1"></i>
                                تاريخ الإضافة
                            </label>
                            <input type="text" class="form-control" value="{{ today|date:'d/m/Y' }}" readonly>
                            <div class="help-text">
                                <i class="fas fa-clock me-1"></i>
                                سيتم حفظ التاريخ تلقائياً
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-1"></i>
                                وصف الدورة
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.description.errors %}
                                        <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="help-text">
                                <i class="fas fa-info-circle me-1"></i>
                                وصف مختصر عن محتوى الدورة وأهدافها (اختياري)
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="form-section">
                <h5><i class="fas fa-lightbulb me-2"></i>نصائح مهمة</h5>
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>ملاحظات:</h6>
                    <ul class="mb-0">
                        <li>تأكد من كتابة اسم الدورة بوضوح ودقة</li>
                        <li>يمكن إضافة وصف مفصل للدورة لتسهيل التعرف عليها لاحقاً</li>
                        <li>سيتم حفظ تاريخ ووقت الإضافة تلقائياً</li>
                        <li>يمكن تعديل بيانات الدورة لاحقاً من قائمة الدورات</li>
                    </ul>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <a href="{% url 'ranks:course_list' %}" class="btn btn-secondary btn-lg me-3">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>حفظ الدورة
                </button>
            </div>
        </form>
    </div>
</div>

<!-- معاينة البيانات -->
<div class="card shadow">
    <div class="card-header py-3 bg-info text-white">
        <h6 class="m-0 font-weight-bold">
            <i class="fas fa-eye me-2"></i>
            معاينة البيانات
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <p><strong><i class="fas fa-graduation-cap me-1"></i>اسم الدورة:</strong>
                    <span id="previewName" class="text-muted">سيظهر هنا عند الكتابة</span>
                </p>
            </div>
            <div class="col-md-3">
                <p><strong><i class="fas fa-clock me-1"></i>عدد الساعات:</strong>
                    <span id="previewHours" class="text-muted">0</span>
                    <span class="text-muted">ساعة</span>
                </p>
            </div>
            <div class="col-md-5">
                <p><strong><i class="fas fa-calendar me-1"></i>تاريخ الإضافة:</strong>
                    <span class="badge bg-secondary">{{ today|date:'d/m/Y' }}</span>
                </p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <p><strong><i class="fas fa-align-left me-1"></i>الوصف:</strong>
                    <span id="previewDescription" class="text-muted">سيظهر هنا عند الكتابة</span>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // معاينة مباشرة للبيانات
    const nameInput = document.getElementById('{{ form.name.id_for_label }}');
    const descriptionInput = document.getElementById('{{ form.description.id_for_label }}');
    const hoursInput = document.getElementById('{{ form.hours.id_for_label }}');
    const previewName = document.getElementById('previewName');
    const previewDescription = document.getElementById('previewDescription');
    const previewHours = document.getElementById('previewHours');

    // تحديث معاينة الاسم
    nameInput.addEventListener('input', function() {
        const value = this.value.trim();
        previewName.textContent = value || 'سيظهر هنا عند الكتابة';
        previewName.className = value ? 'text-primary fw-bold' : 'text-muted';
    });

    // تحديث معاينة الوصف
    descriptionInput.addEventListener('input', function() {
        const value = this.value.trim();
        previewDescription.textContent = value || 'سيظهر هنا عند الكتابة';
        previewDescription.className = value ? 'text-success' : 'text-muted';
    });

    // تحديث معاينة عدد الساعات
    hoursInput.addEventListener('input', function() {
        const value = parseInt(this.value) || 0;
        previewHours.textContent = value;
        previewHours.className = value > 0 ? 'text-info fw-bold' : 'text-muted';
    });

    // التحقق من صحة النموذج
    document.getElementById('courseForm').addEventListener('submit', function(e) {
        const courseName = nameInput.value.trim();
        
        if (!courseName) {
            e.preventDefault();
            alert('يرجى إدخال اسم الدورة');
            nameInput.focus();
            return false;
        }

        if (courseName.length < 3) {
            e.preventDefault();
            alert('يجب أن يكون اسم الدورة 3 أحرف على الأقل');
            nameInput.focus();
            return false;
        }

        // تأكيد الحفظ
        if (!confirm('هل أنت متأكد من رغبتك في حفظ هذه الدورة؟')) {
            e.preventDefault();
            return false;
        }
    });

    // تركيز على حقل الاسم عند تحميل الصفحة
    nameInput.focus();
});
</script>
{% endblock %}
