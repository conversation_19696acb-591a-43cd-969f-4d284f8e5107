{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير موظفي المديرية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* Add background colors for the table cells to match PDF preview */
    .bg-success.bg-opacity-25 {
        background-color: #d4edda !important;
    }

    .bg-danger.bg-opacity-25 {
        background-color: #f8d7da !important;
    }

    .bg-info.bg-opacity-25 {
        background-color: #d1ecf1 !important;
    }

    /* Add clear borders to table cells */
    .table-bordered {
        border: 2px solid #dee2e6 !important;
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid #000 !important;
        border-width: 1px !important;
    }

    .table-bordered thead th {
        border-bottom: 2px solid #000 !important;
    }

    /* Make text in cells bold for better visibility */
    .table-bordered td {
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ report_title }}</h1>
        <div>
            <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للتقارير
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تصفية التقرير</h6>
        </div>
        <div class="card-body">
            <form method="get" class="mb-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="department">القسم</label>
                            <select name="department" id="department" class="form-control">
                                <option value="">جميع أقسام المديرية</option>
                                {% for dept in departments %}
                                <option value="{{ dept.id }}" {% if selected_department.id == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                        <button type="submit" name="export" value="excel" class="btn btn-success me-2">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button type="submit" name="export" value="pdf" class="btn btn-danger me-2">
                            <i class="fas fa-file-pdf"></i> تصدير PDF
                        </button>
                        <a href="javascript:void(0);" onclick="openWhatsAppDialog();" class="btn btn-success">
                            <i class="fab fa-whatsapp"></i> إرسال واتس اب
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">قائمة الموظفين</h6>
            <span class="badge bg-primary fs-6 p-2">عدد الموظفين: {{ employee_data|length }}</span>
        </div>
        <div class="card-body">
            {% if employee_data %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="dataTable" width="100%" cellspacing="0">
                    <thead class="thead-light">
                        <tr>
                            <th rowspan="2">الرقم الوزاري</th>
                            <th rowspan="2">الاسم</th>
                            <th rowspan="2">القسم</th>
                            {% for leave_type in leave_types %}
                                {% if leave_type.name == 'annual' %}
                                    <th colspan="4" class="text-center">{{ leave_type.display_name }}</th>
                                {% else %}
                                    <th colspan="3" class="text-center">{{ leave_type.display_name }}</th>
                                {% endif %}
                            {% endfor %}
                        </tr>
                        <tr>
                            {% for leave_type in leave_types %}
                                <th class="bg-success text-white">الرصيد الأولي</th>
                                <th class="bg-danger text-white">الإجازات المستخدمة</th>
                                {% if leave_type.name == 'annual' %}
                                    <th class="bg-warning text-dark">المغادرات الخاصة</th>
                                {% endif %}
                                <th class="bg-info text-white">الرصيد المتبقي</th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in employee_data %}
                        <tr class="text-center">
                            <td>{{ item.employee.ministry_number }}</td>
                            <td>
                                <a href="{% url 'employees:employee_detail' item.employee.pk %}">
                                    {{ item.employee.full_name }}
                                </a>
                            </td>
                            <td>{{ item.department }}</td>

                            <!-- الإجازات السنوية -->
                            <td class="bg-success bg-opacity-25">{{ item.annual_balance.initial }}</td>
                            <td class="bg-danger bg-opacity-25">{{ item.annual_balance.used }}</td>
                            <td class="bg-warning bg-opacity-25">{{ item.annual_balance.departures|floatformat:2 }}</td>
                            <td class="bg-info bg-opacity-25">{{ item.annual_balance.remaining|floatformat:2 }}</td>

                            <!-- الإجازات المرضية -->
                            <td class="bg-success bg-opacity-25">{{ item.sick_balance.initial }}</td>
                            <td class="bg-danger bg-opacity-25">{{ item.sick_balance.used }}</td>
                            <td class="bg-info bg-opacity-25">{{ item.sick_balance.remaining }}</td>

                            <!-- الإجازات العرضية -->
                            <td class="bg-success bg-opacity-25">{{ item.casual_balance.initial }}</td>
                            <td class="bg-danger bg-opacity-25">{{ item.casual_balance.used }}</td>
                            <td class="bg-info bg-opacity-25">{{ item.casual_balance.remaining }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> لا توجد بيانات للموظفين تطابق معايير البحث.
            </div>
            {% endif %}
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">معلومات عن التقرير</h6>
        </div>
        <div class="card-body">
            <p>يقوم هذا التقرير بإنشاء ملف Excel أو PDF يحتوي على معلومات عن موظفي المديرية وأرصدة إجازاتهم.</p>
            <p>يمكنك تحديد قسم معين لإنشاء تقرير خاص بموظفي هذا القسم فقط، أو اختيار جميع أقسام المديرية.</p>
            <p>يتضمن التقرير المعلومات التالية:</p>
            <ul>
                <li>الرقم الوزاري للموظف</li>
                <li>اسم الموظف</li>
                <li>القسم</li>
            </ul>
            <p>كما يتضمن التقرير معلومات عن أرصدة الإجازات للموظفين:</p>
            <ul>
                <li>الإجازة السنوية:
                    <ul>
                        <li>الرصيد الأولي: رصيد الإجازة في بداية السنة</li>
                        <li>الإجازات المستخدمة: عدد أيام الإجازة المستخدمة خلال السنة</li>
                        <li>الرصيد المتبقي: الرصيد المتبقي من الإجازة</li>
                    </ul>
                </li>
                <li>الإجازة المرضية:
                    <ul>
                        <li>الرصيد الأولي: رصيد الإجازة في بداية السنة</li>
                        <li>الإجازات المستخدمة: عدد أيام الإجازة المستخدمة خلال السنة</li>
                        <li>الرصيد المتبقي: الرصيد المتبقي من الإجازة</li>
                    </ul>
                </li>
                <li>الإجازة العرضية:
                    <ul>
                        <li>الرصيد الأولي: رصيد الإجازة في بداية السنة</li>
                        <li>الإجازات المستخدمة: عدد أيام الإجازة المستخدمة خلال السنة</li>
                        <li>الرصيد المتبقي: الرصيد المتبقي من الإجازة</li>
                    </ul>
                </li>
            </ul>
            <p><strong>ملاحظة:</strong> عند تصدير التقرير إلى PDF، يتم ترتيب الجدول من اليمين إلى اليسار بدءًا من الرقم الوزاري.</p><br>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize DataTable when document is ready
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "{% static 'vendor/datatables/ar.json' %}"
            },
            "order": [[1, "asc"]],
            "pageLength": 25
        });
    });

    // Global function to open WhatsApp dialog
    function openWhatsAppDialog() {
        // Check if there are any rows in the table
        if (document.querySelectorAll('#dataTable tbody tr').length === 0) {
            alert('لا توجد بيانات للموظفين لإرسال رسائل واتساب.');
            return;
        }

        // Get selected department (can be empty for all departments)
        var departmentId = document.getElementById('department').value;

        // Show loading indicator
        var loadingDiv = document.createElement('div');
        loadingDiv.id = 'whatsapp-loading';
        loadingDiv.style.position = 'fixed';
        loadingDiv.style.top = '50%';
        loadingDiv.style.left = '50%';
        loadingDiv.style.transform = 'translate(-50%, -50%)';
        loadingDiv.style.padding = '20px';
        loadingDiv.style.background = 'rgba(0,0,0,0.7)';
        loadingDiv.style.color = 'white';
        loadingDiv.style.borderRadius = '10px';
        loadingDiv.style.zIndex = '9999';
        loadingDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تحضير الرسائل...';
        document.body.appendChild(loadingDiv);

        // Make AJAX request to get all employees in the department
        fetch(`/reports/directorate-employees/whatsapp/department/?department=${departmentId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.count === 0) {
                        document.body.removeChild(loadingDiv);
                        alert('لا يوجد موظفين لديهم أرقام هواتف في هذا القسم');
                        return;
                    }

                    // Update loading message
                    loadingDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تحضير الرسائل لـ ' + data.count + ' موظف...';

                    // Create a single WhatsApp message for all employees
                    var allMessages = '';

                    // Add department name as header
                    allMessages += `قسم: ${data.department}\n\n`;

                    // Add each employee's message
                    data.employees.forEach(function(employee, index) {
                        allMessages += `${index + 1}. ${employee.name}:\n`;

                        // Extract just the leave balances part from the message
                        var messageLines = employee.message.split('\n');
                        var balanceLines = [];
                        var inBalanceSection = false;

                        for (var i = 0; i < messageLines.length; i++) {
                            var line = messageLines[i];
                            if (line.includes('أرصدة الإجازات')) {
                                inBalanceSection = true;
                                continue;
                            }

                            if (inBalanceSection && line.startsWith('-')) {
                                balanceLines.push(line);
                            }

                            if (inBalanceSection && line.trim() === '') {
                                inBalanceSection = false;
                            }
                        }

                        // Add the balance lines to the message
                        balanceLines.forEach(function(line) {
                            allMessages += `   ${line}\n`;
                        });

                        allMessages += '\n';
                    });

                    // Add footer
                    allMessages += 'مع تحيات قسم شؤون الموظفين';

                    // Create WhatsApp URL
                    var encodedMessage = encodeURIComponent(allMessages);
                    var whatsappUrl = `https://wa.me/?text=${encodedMessage}`;

                    // Remove loading indicator
                    document.body.removeChild(loadingDiv);

                    // Open WhatsApp in a new window
                    window.open(whatsappUrl, "_blank");
                } else {
                    // Remove loading indicator
                    document.body.removeChild(loadingDiv);

                    // Show error message
                    alert(data.error || 'حدث خطأ أثناء جلب بيانات الموظفين');
                }
            })
            .catch(error => {
                // Remove loading indicator
                if (document.getElementById('whatsapp-loading')) {
                    document.body.removeChild(document.getElementById('whatsapp-loading'));
                }

                // Show error message
                alert('حدث خطأ أثناء الاتصال بالخادم');
                console.error('Error:', error);
            });
    }

    // Function to send WhatsApp for a specific employee using the API
    function sendWhatsAppForEmployee(employeeId) {
        // Show loading indicator
        var loadingDiv = document.createElement('div');
        loadingDiv.id = 'whatsapp-loading';
        loadingDiv.style.position = 'fixed';
        loadingDiv.style.top = '50%';
        loadingDiv.style.left = '50%';
        loadingDiv.style.transform = 'translate(-50%, -50%)';
        loadingDiv.style.padding = '20px';
        loadingDiv.style.background = 'rgba(0,0,0,0.7)';
        loadingDiv.style.color = 'white';
        loadingDiv.style.borderRadius = '10px';
        loadingDiv.style.zIndex = '9999';
        loadingDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
        document.body.appendChild(loadingDiv);

        // Make AJAX request to get WhatsApp data
        fetch(`/reports/directorate-employees/whatsapp/${employeeId}/`)
            .then(response => response.json())
            .then(data => {
                // Remove loading indicator
                document.body.removeChild(loadingDiv);

                if (data.success) {
                    // Create WhatsApp URL
                    var encodedMessage = encodeURIComponent(data.message);
                    var whatsappUrl = `https://wa.me/${data.employee.phone}?text=${encodedMessage}`;

                    // Open WhatsApp in a new window
                    window.open(whatsappUrl, "_blank");
                } else {
                    // Show error message
                    alert(data.error || 'حدث خطأ أثناء إنشاء رابط واتس اب');
                }
            })
            .catch(error => {
                // Remove loading indicator
                if (document.getElementById('whatsapp-loading')) {
                    document.body.removeChild(document.getElementById('whatsapp-loading'));
                }

                // Show error message
                alert('حدث خطأ أثناء الاتصال بالخادم');
                console.error('Error:', error);
            });
    }
</script>
{% endblock %}
