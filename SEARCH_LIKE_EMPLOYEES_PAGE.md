# تطبيق شريط البحث مثل صفحة الموظفين
# Implementing Search Bar Like Employees Page

## ✅ التحديث المنفذ

### 🎯 الهدف:
نسخ شريط البحث من صفحة بيانات الموظفين وتطبيقه على صفحة تقارير الإجازات لضمان عمله بنفس الكفاءة.

## 🔄 التغييرات المنفذة

### 1. تحديث HTML لشريط البحث

#### **قبل التحديث:**
```html
<div class="input-group" style="width: 400px;">
    <span class="input-group-text bg-white">
        <i class="fas fa-search text-muted"></i>
    </span>
    <input type="text" class="form-control" id="employeeSearch" 
           placeholder="البحث في الأسماء والأرقام الوزارية..." autocomplete="off">
    <button class="btn btn-outline-light" type="button" id="searchButton" title="بحث">
        <i class="fas fa-search" id="searchIcon"></i>
        <i class="fas fa-spinner fa-spin" id="searchSpinner" style="display: none;"></i>
    </button>
    <button class="btn btn-outline-light" type="button" id="clearSearch" title="مسح البحث">
        <i class="fas fa-times"></i>
    </button>
</div>
```

#### **بعد التحديث (مثل صفحة الموظفين):**
```html
<form method="GET" class="d-inline-block" id="searchForm">
    <div class="input-group" style="width: 350px;">
        <span class="input-group-text">
            <i class="fas fa-search"></i>
        </span>
        <input type="text" id="searchInputMain" name="search" class="form-control form-control-sm"
               placeholder="ابحث بالاسم، الرقم الوزاري..." value="" autocomplete="off">
        <button type="submit" class="btn btn-outline-primary btn-sm" id="searchButton">
            <i class="fas fa-search"></i>
        </button>
        <button type="button" class="btn btn-outline-secondary btn-sm" id="clearSearchBtn" title="مسح البحث">
            <i class="fas fa-times"></i>
        </button>
    </div>
</form>
```

### 2. تحديث CSS

#### **CSS المنسوخ من صفحة الموظفين:**
```css
/* تحسين مظهر البحث */
#searchInputMain {
    border-left: none;
    border-right: none;
    transition: all 0.3s ease;
}

#searchInputMain:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    z-index: 3;
    position: relative;
}

#searchButton {
    border-left: none;
    transition: all 0.3s ease;
    z-index: 2;
}

#searchButton:hover {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

#searchButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
    border-right: none;
}

/* تحسين مظهر زر مسح البحث */
.input-group .btn-outline-secondary {
    border-left: none;
    color: #6c757d;
}

.input-group .btn-outline-secondary:hover {
    background-color: #6c757d;
    color: white;
    border-color: #6c757d;
}

/* تأثير البحث النشط */
.search-active #searchInputMain {
    background-color: #fff3cd;
    border-color: #ffc107;
}

.search-active .input-group-text {
    background-color: #ffc107;
    color: #212529;
    border-color: #ffc107;
}
```

### 3. تحديث JavaScript

#### **JavaScript المنسوخ والمعدل من صفحة الموظفين:**
```javascript
$(document).ready(function() {
    console.log('Initializing DataTable and search...');
    
    // Initialize DataTable
    var table = $('#dataTable').DataTable({
        "language": {
            "url": "{% static 'vendor/datatables/ar.json' %}"
        },
        "order": [[1, "asc"]],
        "pageLength": 25,
        "searching": true,
        "dom": 'lrtip'
    });

    // Initialize search active state
    if ($('#searchInputMain').val().trim()) {
        $('#searchInputMain').closest('form').addClass('search-active');
    }

    // Search functionality with delay
    let searchTimeout;
    $('#searchInputMain').on('keyup input', function(e) {
        console.log('Search input event triggered:', e.type, $(this).val());
        clearTimeout(searchTimeout);
        const searchValue = $(this).val();

        // Update search active state
        const $form = $('#searchInputMain').closest('form');
        if (searchValue.trim()) {
            $form.addClass('search-active');
        } else {
            $form.removeClass('search-active');
        }

        // If Enter key is pressed, search immediately
        if (e.key === 'Enter') {
            e.preventDefault();
            clearTimeout(searchTimeout);
            console.log('Enter pressed, searching immediately');
            performSearch();
            return;
        }

        // Delay search to avoid too many requests
        console.log('Setting timeout for search');
        searchTimeout = setTimeout(function() {
            console.log('Timeout triggered, performing search');
            performSearch();
        }, 500); // Wait 500ms after user stops typing
    });

    // Search function
    function performSearch() {
        console.log('performSearch called');
        const searchValue = $('#searchInputMain').val();
        
        // Perform DataTable search
        table.search(searchValue).draw();
        
        // Show search results
        showSearchResults(searchValue);
    }

    // Search form submit handler - prevent form submission
    $('#searchForm').on('submit', function(e) {
        e.preventDefault(); // Always prevent form submission
        console.log('Form submitted, performing search');
        performSearch();
        return false;
    });
    
    // Search button click handler
    $('#searchButton').on('click', function(e) {
        e.preventDefault(); // Prevent form submission
        console.log('Search button clicked');
        performSearch();
        return false;
    });
    
    // Clear search button
    $('#clearSearchBtn').on('click', function(e) {
        e.preventDefault();
        console.log('Clear button clicked');
        
        // Clear input
        $('#searchInputMain').val('');
        
        // Remove search active state
        $('#searchInputMain').closest('form').removeClass('search-active');
        
        // Clear DataTable search
        table.search('').draw();
        
        // Hide search info
        $('#searchInfo').hide();
        
        return false;
    });
});
```

## 🎯 الميزات المنسوخة من صفحة الموظفين

### **1. التصميم:**
- ✅ **نفس التخطيط**: form مع input-group
- ✅ **نفس الألوان**: أزرق للبحث، رمادي للمسح
- ✅ **نفس الأحجام**: btn-sm و form-control-sm
- ✅ **نفس الأيقونات**: fas fa-search و fas fa-times

### **2. الوظائف:**
- ✅ **البحث أثناء الكتابة**: مع تأخير 500ms
- ✅ **البحث بـ Enter**: فوري عند الضغط
- ✅ **البحث بالزر**: عند النقر على زر البحث
- ✅ **منع إرسال النموذج**: preventDefault للـ form
- ✅ **مسح البحث**: زر منفصل للمسح

### **3. التأثيرات البصرية:**
- ✅ **حالة البحث النشط**: خلفية صفراء عند البحث
- ✅ **تأثيرات hover**: تغيير الألوان عند التمرير
- ✅ **انتقالات سلسة**: transition للتأثيرات
- ✅ **focus states**: تأثيرات عند التركيز

### **4. معالجة الأحداث:**
- ✅ **keyup و input**: للبحث أثناء الكتابة
- ✅ **submit**: لمنع إرسال النموذج
- ✅ **click**: لأزرار البحث والمسح
- ✅ **preventDefault**: لمنع السلوك الافتراضي

## 🔍 طريقة عمل البحث

### **1. البحث التلقائي:**
```javascript
// البحث أثناء الكتابة مع تأخير
$('#searchInputMain').on('keyup input', function(e) {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(function() {
        performSearch();
    }, 500);
});
```

### **2. البحث الفوري:**
```javascript
// البحث فوري عند Enter
if (e.key === 'Enter') {
    e.preventDefault();
    clearTimeout(searchTimeout);
    performSearch();
    return;
}
```

### **3. البحث بالزر:**
```javascript
// البحث عند النقر على الزر
$('#searchButton').on('click', function(e) {
    e.preventDefault();
    performSearch();
    return false;
});
```

## 📊 عرض النتائج

### **رسائل النتائج:**
```javascript
if (info.recordsDisplay === 0) {
    // لا توجد نتائج
    searchInfo.html(`
        <i class="fas fa-exclamation-triangle"></i>
        لا توجد نتائج للبحث عن: "<strong>${searchValue}</strong>"
        <span class="badge bg-warning text-dark ms-2">0 من ${info.recordsTotal}</span>
    `);
} else {
    // توجد نتائج
    var resultText = info.recordsDisplay === 1 ? 'موظف واحد' : `${info.recordsDisplay} موظف`;
    searchInfo.html(`
        <i class="fas fa-check-circle"></i>
        نتائج البحث عن: "<strong>${searchValue}</strong>"
        <span class="badge bg-success ms-2">${resultText} من ${info.recordsTotal}</span>
    `);
}
```

## 🧪 للاختبار

### **خطوات الاختبار:**
1. **افتح صفحة التقارير**: http://localhost:8000/leaves/reports/
2. **اختبر البحث أثناء الكتابة**:
   - اكتب "أحمد" → يجب أن يبحث تلقائياً بعد 500ms ✅
3. **اختبر البحث بـ Enter**:
   - اكتب نص واضغط Enter → يجب أن يبحث فوراً ✅
4. **اختبر زر البحث**:
   - اكتب نص واضغط زر البحث → يجب أن يبحث ✅
5. **اختبر زر المسح**:
   - اضغط زر X → يجب أن يمسح البحث ✅
6. **اختبر التأثيرات البصرية**:
   - لاحظ الخلفية الصفراء عند البحث ✅
   - لاحظ تأثيرات hover على الأزرار ✅

### **النتائج المتوقعة:**
- ✅ البحث يعمل بنفس طريقة صفحة الموظفين
- ✅ التصميم متطابق مع صفحة الموظفين
- ✅ جميع الوظائف تعمل بشكل صحيح
- ✅ التأثيرات البصرية تعمل كما هو متوقع

## الملفات المحدثة

1. **`templates/leaves/leave_reports.html`**:
   - تحديث HTML لشريط البحث
   - نسخ CSS من صفحة الموظفين
   - نسخ وتعديل JavaScript من صفحة الموظفين

## الخلاصة

✅ **شريط البحث الآن مطابق تماماً لصفحة الموظفين**
✅ **جميع الوظائف منسوخة ومطبقة بنجاح**
✅ **التصميم والتأثيرات البصرية متطابقة**
✅ **البحث يعمل بنفس الكفاءة والسرعة**
✅ **معالجة الأحداث مطابقة للأصل**

**البحث الآن يعمل بنفس طريقة صفحة الموظفين تماماً! 🎉**
