{% extends 'base.html' %}
{% load static %}

{% block title %}تأكيد حذف الموظف المشترك{% endblock %}

{% block extra_css %}
<style>
    .delete-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 20px;
    }
    .employee-details {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        border-left: 4px solid #dc3545;
    }
    .btn-custom {
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    .btn-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
    }
    .detail-row:last-child {
        border-bottom: none;
    }
    .detail-label {
        font-weight: 600;
        color: #495057;
    }
    .detail-value {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                        تأكيد حذف الموظف المشترك
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'employees:shared_employees_list' %}">المعلمين المشتركين</a></li>
                            <li class="breadcrumb-item active">تأكيد الحذف</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'employees:shared_employees_list' %}" class="btn btn-secondary btn-custom">
                        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="delete-container text-center">
                <!-- Warning Icon -->
                <div class="warning-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>

                <!-- Warning Message -->
                <h4 class="text-danger mb-3">تحذير!</h4>
                <p class="lead mb-4">
                    هل أنت متأكد من رغبتك في حذف بيانات الإشراك للموظف التالي؟
                </p>
                <p class="text-muted">
                    <strong>تنبيه:</strong> هذا الإجراء لا يمكن التراجع عنه.
                </p>

                <!-- Employee Details -->
                <div class="employee-details text-start">
                    <h5 class="mb-3">
                        <i class="fas fa-user text-primary me-2"></i>
                        تفاصيل الموظف المشترك
                    </h5>
                    
                    <div class="detail-row">
                        <span class="detail-label">الرقم الوزاري:</span>
                        <span class="detail-value">{{ shared_employee.employee.ministry_number }}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">الاسم الكامل:</span>
                        <span class="detail-value">{{ shared_employee.employee.full_name }}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">التخصص:</span>
                        <span class="detail-value">{{ shared_employee.employee.specialization|default:"غير محدد" }}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">القسم الأصلي:</span>
                        <span class="detail-value">{{ shared_employee.original_department }}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">القسم المشترك:</span>
                        <span class="detail-value">{{ shared_employee.shared_department }}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">تاريخ الإشراك:</span>
                        <span class="detail-value">{{ shared_employee.sharing_date|date:"Y-m-d" }}</span>
                    </div>
                    
                    {% if shared_employee.end_date %}
                    <div class="detail-row">
                        <span class="detail-label">تاريخ الانتهاء:</span>
                        <span class="detail-value">{{ shared_employee.end_date|date:"Y-m-d" }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="detail-row">
                        <span class="detail-label">الحالة:</span>
                        <span class="detail-value">
                            {% if shared_employee.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-danger">منتهي</span>
                            {% endif %}
                        </span>
                    </div>
                    
                    {% if shared_employee.notes %}
                    <div class="detail-row">
                        <span class="detail-label">ملاحظات:</span>
                        <span class="detail-value">{{ shared_employee.notes }}</span>
                    </div>
                    {% endif %}
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-center gap-3 mt-4">
                    <a href="{% url 'employees:shared_employees_list' %}" class="btn btn-secondary btn-custom">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger btn-custom">
                            <i class="fas fa-trash me-2"></i>تأكيد الحذف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation dialog for extra safety
    $('form').submit(function(e) {
        if (!confirm('هل أنت متأكد من رغبتك في حذف بيانات الإشراك؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
