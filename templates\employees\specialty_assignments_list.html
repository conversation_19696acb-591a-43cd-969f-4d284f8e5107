{% extends 'base.html' %}
{% load static %}

{% block title %}المعلمين المحملين على تخصص آخر{% endblock %}

{% block extra_css %}
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
    /* تنسيق البطاقات مثل لوحة التحكم */
    .dashboard-card {
        border-right: 4px solid;
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
        background-color: white;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }

    .dashboard-card .card-body {
        padding: 1.5rem;
    }

    .dashboard-card .text-xs {
        font-size: 0.85rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        display: block;
    }

    .dashboard-card .h3 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0;
    }

    /* تنسيق الأيقونات */
    .dashboard-card .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    .dashboard-card .icon-circle i {
        font-size: 1.8rem;
    }

    /* ألوان البطاقات */
    .card-primary {
        border-color: #4e73df;
    }

    .card-primary .text-xs {
        color: #4e73df;
    }

    .card-primary .icon-circle {
        background-color: rgba(78, 115, 223, 0.1);
        color: #4e73df;
    }

    .card-success {
        border-color: #1cc88a;
    }

    .card-success .text-xs {
        color: #1cc88a;
    }

    .card-success .icon-circle {
        background-color: rgba(28, 200, 138, 0.1);
        color: #1cc88a;
    }

    .card-info {
        border-color: #36b9cc;
    }

    .card-info .text-xs {
        color: #36b9cc;
    }

    .card-info .icon-circle {
        background-color: rgba(54, 185, 204, 0.1);
        color: #36b9cc;
    }

    .card-warning {
        border-color: #f6c23e;
    }

    .card-warning .text-xs {
        color: #f6c23e;
    }

    .card-warning .icon-circle {
        background-color: rgba(246, 194, 62, 0.1);
        color: #f6c23e;
    }

    /* تنسيق الفلاتر */
    .filter-control {
        font-size: 0.95rem;
        padding: 8px 12px;
        height: auto;
        border: 1px solid #ced4da;
    }

    .filter-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-label.small {
        font-weight: 600;
        margin-bottom: 5px;
    }

    #filterInfo {
        background-color: #f8f9fc;
        padding: 8px 12px;
        border-radius: 0.35rem;
        border-left: 4px solid #4e73df;
    }

    /* Select2 styling */
    .select2-container--default .select2-selection--single {
        height: 31px;
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 29px;
        font-size: 13px;
        color: #5a5c69;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 29px;
    }

    .select2-dropdown {
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
    }

    .select2-container.filter-active .select2-selection--single {
        background-color: #e3f2fd !important;
        border-color: #2196f3 !important;
        box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25) !important;
    }

    .select2-container.filter-active .select2-selection__rendered {
        color: #1976d2 !important;
        font-weight: 600 !important;
    }

    .select2-search--dropdown .select2-search__field {
        border: 1px solid #d1d3e2 !important;
        border-radius: 0.25rem !important;
        padding: 4px 8px !important;
        font-size: 13px !important;
    }

    .select2-results__option {
        padding: 6px 12px !important;
        font-size: 13px !important;
    }

    /* Search input styling */
    #searchInput {
        transition: all 0.3s ease;
    }

    #searchInput:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .search-active #searchInput {
        background-color: #fff3cd;
        border-color: #ffc107;
    }

    .table-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        padding: 20px;
        margin-top: 20px;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 10px;
    }

    .btn-group .btn {
        margin-right: 10px;
        margin-bottom: 10px;
    }

    .status-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
    }

    .status-active {
        background-color: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }

    /* تنسيق الظهور التدريجي */
    .fade-in {
        animation: fadeIn 0.5s ease-in-out forwards;
        opacity: 0;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1><i class="fas fa-chalkboard-teacher"></i> المعلمين المحملين على تخصص آخر</h1>
        <p class="lead">إدارة المعلمين الذين يدرسون تخصصات مختلفة عن تخصصهم الأصلي</p>

        <!-- Action Buttons -->
        <div class="mt-4">
            <a href="{% url 'employees:specialty_assignment_create' %}" class="btn btn-success btn-lg me-3">
                <i class="fas fa-plus"></i> إضافة معلم محمل على تخصص آخر
            </a>
            <a href="{% url 'employees:specialty_assignments_export' %}" class="btn btn-warning btn-lg me-3">
                <i class="fas fa-file-excel"></i> تصدير إلى Excel
            </a>
            <a href="{% url 'employment:employment_list' %}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-arrow-left"></i> العودة لإدارة الكادر
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card card-primary fade-in" style="animation-delay: 0.1s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-xs text-uppercase">إجمالي المعلمين</span>
                            <div class="h3 text-dark">{{ total_assignments }}</div>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card card-success fade-in" style="animation-delay: 0.2s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-xs text-uppercase">المعلمين النشطين</span>
                            <div class="h3 text-dark">{{ active_assignments }}</div>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card card-warning fade-in" style="animation-delay: 0.3s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-xs text-uppercase">المعلمين غير النشطين</span>
                            <div class="h3 text-dark">{{ inactive_assignments }}</div>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card card-info fade-in" style="animation-delay: 0.4s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-xs text-uppercase">التخصصات المختلفة</span>
                            <div class="h3 text-dark">{{ unique_specialties }}</div>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h6 class="mb-0">
                <i class="fas fa-filter"></i> البحث والفلترة
            </h6>
        </div>
        <div class="card-body py-3">
            <form method="GET" id="filterForm">
                <div class="row g-3">
                    <!-- Search Input -->
                    <div class="col-md-3">
                        <label class="form-label small">
                            <i class="fas fa-search text-primary"></i> البحث العام
                        </label>
                        <input type="text" name="search" class="form-control form-control-sm"
                               placeholder="ابحث بالاسم، الرقم الوزاري..."
                               value="{{ search_query|default:'' }}" id="searchInput">
                    </div>

                    <!-- Original Specialty Filter -->
                    <div class="col-md-2">
                        <label class="form-label small">
                            <i class="fas fa-graduation-cap text-info"></i> التخصص الأصلي
                        </label>
                        <select class="form-select form-select-sm select2-filter" name="original_specialty"
                                data-placeholder="جميع التخصصات">
                            <option value="">جميع التخصصات</option>
                            {% for specialty in original_specialties %}
                                <option value="{{ specialty }}"
                                        {% if original_specialty_filter == specialty %}selected{% endif %}>
                                    {{ specialty }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Assigned Specialty Filter -->
                    <div class="col-md-2">
                        <label class="form-label small">
                            <i class="fas fa-chalkboard-teacher text-success"></i> التخصص المحمل عليه
                        </label>
                        <select class="form-select form-select-sm select2-filter" name="assigned_specialty"
                                data-placeholder="جميع التخصصات">
                            <option value="">جميع التخصصات</option>
                            {% for specialty in assigned_specialties %}
                                <option value="{{ specialty }}"
                                        {% if assigned_specialty_filter == specialty %}selected{% endif %}>
                                    {{ specialty }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Department Filter -->
                    <div class="col-md-2">
                        <label class="form-label small">
                            <i class="fas fa-building text-warning"></i> القسم
                        </label>
                        <select class="form-select form-select-sm select2-filter" name="department"
                                data-placeholder="جميع الأقسام">
                            <option value="">جميع الأقسام</option>
                            {% for dept in departments %}
                                <option value="{{ dept }}"
                                        {% if department_filter == dept %}selected{% endif %}>
                                    {{ dept }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div class="col-md-2">
                        <label class="form-label small">
                            <i class="fas fa-toggle-on text-danger"></i> الحالة
                        </label>
                        <select class="form-select form-select-sm select2-filter" name="status"
                                data-placeholder="جميع الحالات">
                            <option value="">جميع الحالات</option>
                            <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                            <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                        </select>
                    </div>

                    <!-- Action Buttons -->
                    <div class="col-md-1">
                        <label class="form-label small">&nbsp;</label>
                        <div class="d-flex gap-1">
                            <button type="submit" class="btn btn-primary btn-sm" title="تطبيق الفلاتر">
                                <i class="fas fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="clearFilters" title="مسح الفلاتر">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Filter Info -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div id="filterInfo" class="small text-muted"></div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Data Table -->
    <div class="table-container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">قائمة المعلمين المحملين على تخصصات أخرى</h5>
            <div class="text-muted">
                <span id="assignmentCounter">{{ assignments.count }} معلم</span>
            </div>
        </div>
        <div class="table-responsive">
            <table id="assignmentsTable" class="table table-striped table-bordered" style="width:100%">
                <thead class="thead-dark">
                    <tr>
                        <th>الرقم الوزاري</th>
                        <th>الاسم الكامل</th>
                        <th>التخصص الأصلي</th>
                        <th>التخصص المحمل عليه</th>
                        <th>القسم</th>
                        <th>المواد التي يدرسها</th>
                        <th>تاريخ التحميل</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for assignment in assignments %}
                    <tr>
                        <td>{{ assignment.employee.ministry_number }}</td>
                        <td>{{ assignment.employee.full_name }}</td>
                        <td>{{ assignment.original_specialty }}</td>
                        <td>{{ assignment.assigned_specialty }}</td>
                        <td>{{ assignment.department }}</td>
                        <td>{{ assignment.subjects_taught|truncatechars:50 }}</td>
                        <td>{{ assignment.assignment_date|date:"Y-m-d" }}</td>
                        <td>
                            {% if assignment.is_active %}
                                <span class="status-badge status-active">نشط</span>
                            {% else %}
                                <span class="status-badge status-inactive">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'employees:specialty_assignment_detail' assignment.pk %}" 
                                   class="btn btn-sm btn-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'employees:specialty_assignment_update' assignment.pk %}" 
                                   class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'employees:specialty_assignment_delete' assignment.pk %}" 
                                   class="btn btn-sm btn-danger" title="حذف"
                                   onclick="return confirm('هل أنت متأكد من حذف هذا التحميل؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                لا توجد بيانات معلمين محملين على تخصصات أخرى
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable with Arabic language
    var table = $('#assignmentsTable').DataTable({
        "language": {
            "sProcessing": "جاري التحميل...",
            "sLengthMenu": "أظهر _MENU_ مدخلات",
            "sZeroRecords": "لم يعثر على أية سجلات",
            "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
            "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
            "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
            "sInfoPostFix": "",
            "sSearch": "ابحث:",
            "sUrl": "",
            "oPaginate": {
                "sFirst": "الأول",
                "sPrevious": "السابق",
                "sNext": "التالي",
                "sLast": "الأخير"
            }
        },
        "pageLength": 25,
        "order": [[ 6, "desc" ]], // Sort by assignment date descending
        "columnDefs": [
            { "orderable": false, "targets": 8 } // Disable sorting for actions column
        ],
        "responsive": true,
        "autoWidth": false,
        "searching": false, // Disable the default search box
        "dom": 'lrtip' // Remove the search box from the DOM layout
    });

    // Initialize Select2 for filters
    $('.select2-filter').select2({
        theme: 'bootstrap-5',
        placeholder: function() {
            return $(this).data('placeholder') || $(this).find('option:first').text();
        },
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: true,
        language: {
            noResults: function() {
                return "لا توجد نتائج مطابقة";
            },
            searching: function() {
                return "جاري البحث...";
            },
            inputTooShort: function() {
                return "ابدأ بكتابة للبحث...";
            },
            loadingMore: function() {
                return "جاري تحميل المزيد...";
            }
        },
        minimumInputLength: 0,
        templateResult: function(data) {
            return data.text;
        },
        templateSelection: function(data) {
            return data.text;
        },
        minimumResultsForSearch: 0
    });

    // Filter functionality
    function applyFilters() {
        var searchValue = $('#searchInput').val().toLowerCase();
        var originalSpecialty = $('select[name="original_specialty"]').val();
        var assignedSpecialty = $('select[name="assigned_specialty"]').val();
        var department = $('select[name="department"]').val();
        var status = $('select[name="status"]').val();

        var visibleCount = 0;
        var totalCount = 0;

        $('#assignmentsTable tbody tr').each(function() {
            var $row = $(this);
            var show = true;
            totalCount++;

            // Get row text for search
            var rowText = $row.text().toLowerCase();

            // Search filter
            if (searchValue && rowText.indexOf(searchValue) === -1) {
                show = false;
            }

            // Original specialty filter
            if (originalSpecialty && $row.find('td:eq(2)').text().trim() !== originalSpecialty) {
                show = false;
            }

            // Assigned specialty filter
            if (assignedSpecialty && $row.find('td:eq(3)').text().trim() !== assignedSpecialty) {
                show = false;
            }

            // Department filter
            if (department && $row.find('td:eq(4)').text().trim() !== department) {
                show = false;
            }

            // Status filter
            if (status) {
                var statusText = $row.find('td:eq(7)').text().trim();
                if ((status === 'active' && statusText !== 'نشط') ||
                    (status === 'inactive' && statusText !== 'غير نشط')) {
                    show = false;
                }
            }

            if (show) {
                $row.show();
                visibleCount++;
            } else {
                $row.hide();
            }
        });

        // Update counter
        $('#assignmentCounter').text(visibleCount + ' من ' + totalCount + ' معلم');

        // Update filter info
        updateFilterInfo();
    }

    // Update filter info
    function updateFilterInfo() {
        var activeFilters = [];

        // Check search input
        var searchValue = $('#searchInput').val();
        if (searchValue) {
            activeFilters.push('البحث: "' + searchValue + '"');
        }

        // Check Select2 filters
        $('.select2-filter').each(function() {
            var $this = $(this);
            var value = $this.val();
            var label = $this.prev('label').text().replace(/[^\u0600-\u06FF\s]/g, '').trim();

            if (value) {
                var selectedText = $this.find('option:selected').text();
                activeFilters.push(label + ': ' + selectedText);
            }
        });

        var infoText = '';
        if (activeFilters.length > 0) {
            infoText = '<i class="fas fa-filter text-primary"></i> مُطبق: ' + activeFilters.join(' | ');
        } else {
            infoText = '<i class="fas fa-info-circle text-muted"></i> لا توجد فلاتر مطبقة';
        }

        $('#filterInfo').html(infoText);

        // Add visual feedback for active filters
        $('.select2-filter').each(function() {
            var $container = $(this).next('.select2-container');
            if ($(this).val()) {
                $container.addClass('filter-active');
            } else {
                $container.removeClass('filter-active');
            }
        });

        // Search input active state
        if ($('#searchInput').val().trim()) {
            $('#searchInput').addClass('search-active');
        } else {
            $('#searchInput').removeClass('search-active');
        }
    }

    // Search with delay
    let searchTimeout;
    $('#searchInput').on('keyup input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            applyFilters();
        }, 300);
    });

    // Filter change events
    $('.select2-filter').on('change', function() {
        applyFilters();
    });

    // Clear filters
    $('#clearFilters').on('click', function() {
        $('#filterForm')[0].reset();
        $('.select2-filter').val(null).trigger('change');
        $('#searchInput').val('');
        $('.select2-container').removeClass('filter-active');
        $('#searchInput').removeClass('search-active');
        applyFilters();
    });

    // Form submit
    $('#filterForm').on('submit', function(e) {
        e.preventDefault();
        applyFilters();
    });

    // Initialize filter info on page load
    updateFilterInfo();
    applyFilters();
});
</script>
{% endblock %}
