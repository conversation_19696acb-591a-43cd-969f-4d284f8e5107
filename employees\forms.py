from django import forms
from .models import Employee, TeacherSpecialtyAssignment, EmployeeAssignment, AnnualReport, Penalty, RetiredEmployee, ExternalTransfer, MaternityLeave, SharedEmployee, EmployeeGrade, JobGrade, PromotionType
from employment.models import Department

class EmployeeForm(forms.ModelForm):
    # إضافة حقل منفصل للقسم مع دعم البحث
    department_choice = forms.ModelChoiceField(
        queryset=Department.objects.all().order_by('name'),
        empty_label="اختر القسم...",
        label="القسم",
        widget=forms.Select(attrs={
            'class': 'form-control select2',
            'data-placeholder': 'ابحث عن القسم...',
            'data-allow-clear': 'true'
        }),
        required=True
    )
    
    class Meta:
        model = Employee
        fields = [
            'ministry_number', 'national_id', 'full_name', 'gender', 'qualification',
            'post_graduate_diploma', 'masters_degree', 'phd_degree',
            'specialization', 'hire_date', 'birth_date',
            'address', 'phone_number'
        ]
        widgets = {
            'hire_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'birth_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'address': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'gender': forms.Select(attrs={'class': 'form-control'}),
            'ministry_number': forms.TextInput(attrs={'class': 'form-control'}),
            'national_id': forms.TextInput(attrs={'class': 'form-control'}),
            'full_name': forms.TextInput(attrs={'class': 'form-control'}),
            'qualification': forms.TextInput(attrs={'class': 'form-control'}),
            'post_graduate_diploma': forms.TextInput(attrs={'class': 'form-control'}),
            'masters_degree': forms.TextInput(attrs={'class': 'form-control'}),
            'phd_degree': forms.TextInput(attrs={'class': 'form-control'}),
            'specialization': forms.TextInput(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # إذا كان هناك موظف موجود، اضبط القسم الحالي
        if self.instance and self.instance.pk and self.instance.school:
            try:
                department = Department.objects.filter(name=self.instance.school).first()
                if department:
                    self.fields['department_choice'].initial = department
            except:
                pass
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # تعيين اسم القسم من الاختيار
        if self.cleaned_data.get('department_choice'):
            instance.school = self.cleaned_data['department_choice'].name
        
        if commit:
            instance.save()
        
        return instance

class AnnualReportForm(forms.Form):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Other fields
    year = forms.IntegerField(
        label='السنة',
        required=True,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )

    score = forms.DecimalField(
        label='الدرجة',
        required=True,
        max_digits=5,
        decimal_places=2,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'})
    )

    notes = forms.CharField(
        label='ملاحظات',
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
    )

class PenaltyForm(forms.ModelForm):
    class Meta:
        model = Penalty
        fields = ['date', 'description']
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date'}),
            'description': forms.Textarea(attrs={'rows': 3}),
        }

class EmployeeImportForm(forms.Form):
    excel_file = forms.FileField(
        label='ملف Excel',
        help_text='يجب أن يحتوي الملف على الأعمدة المطلوبة'
    )


class RetiredEmployeeForm(forms.ModelForm):
    class Meta:
        model = RetiredEmployee
        fields = ['retirement_date', 'retirement_reason', 'notes']
        widgets = {
            'retirement_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'retirement_reason': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }


class RetireEmployeeForm(forms.Form):
    """Form for selecting an employee to retire"""
    # Search field for employee
    employee_search = forms.CharField(
        label='البحث عن الموظف (الرقم الوزاري أو الاسم)',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control', 
            'placeholder': 'اكتب الرقم الوزاري أو اسم الموظف...',
            'id': 'employee_search_input'
        })
    )
    
    # Hidden field to store selected employee ID
    employee_id = forms.IntegerField(
        widget=forms.HiddenInput(),
        required=True
    )
    
    # Display field for selected employee name
    selected_employee_display = forms.CharField(
        label='الموظف المحدد',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control', 
            'readonly': 'readonly',
            'id': 'selected_employee_display',
            'placeholder': 'لم يتم اختيار موظف بعد'
        })
    )
    
    retirement_date = forms.DateField(
        label='تاريخ التقاعد',
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    retirement_reason = forms.CharField(
        label='سبب التقاعد',
        initial='تقاعد عادي',
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    notes = forms.CharField(
        label='ملاحظات',
        required=False,
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'})
    )

    def clean_employee_id(self):
        employee_id = self.cleaned_data.get('employee_id')
        if not employee_id:
            raise forms.ValidationError('يجب اختيار موظف للتقاعد')
        
        try:
            employee = Employee.objects.get(id=employee_id)
            # Check if employee is already retired
            if hasattr(employee, 'retirement'):
                raise forms.ValidationError('هذا الموظف متقاعد بالفعل')
            return employee_id
        except Employee.DoesNotExist:
            raise forms.ValidationError('الموظف المحدد غير موجود')

    def clean(self):
        cleaned_data = super().clean()
        employee_id = cleaned_data.get('employee_id')
        
        if employee_id:
            # Store the employee object for easy access in views
            try:
                cleaned_data['employee'] = Employee.objects.get(id=employee_id)
            except Employee.DoesNotExist:
                raise forms.ValidationError('الموظف المحدد غير موجود')
        
        return cleaned_data


class ExternalTransferForm(forms.ModelForm):
    """Form for editing external transfer information"""
    class Meta:
        model = ExternalTransfer
        fields = ['transfer_date', 'destination_directorate', 'transfer_reason', 'notes']
        widgets = {
            'transfer_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'destination_directorate': forms.TextInput(attrs={'class': 'form-control'}),
            'transfer_reason': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }


class TransferEmployeeForm(forms.Form):
    """Form for selecting an employee to transfer externally"""
    # Search field for employee
    employee_search = forms.CharField(
        label='البحث عن الموظف (الرقم الوزاري أو الاسم)',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control', 
            'placeholder': 'اكتب الرقم الوزاري أو اسم الموظف...',
            'id': 'employee_search_input_transfer'
        })
    )
    
    # Hidden field to store selected employee ID
    employee_id = forms.IntegerField(
        widget=forms.HiddenInput(),
        required=True
    )
    
    # Display field for selected employee name
    selected_employee_display = forms.CharField(
        label='الموظف المحدد',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control', 
            'readonly': 'readonly',
            'id': 'selected_employee_display_transfer',
            'placeholder': 'لم يتم اختيار موظف بعد'
        })
    )
    
    transfer_date = forms.DateField(
        label='تاريخ النقل',
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    destination_directorate = forms.CharField(
        label='المديرية المنقول إليها',
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    
    transfer_reason = forms.CharField(
        label='سبب النقل',
        initial='نقل خارجي',
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    
    notes = forms.CharField(
        label='ملاحظات',
        required=False,
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'})
    )

    def clean_employee_id(self):
        employee_id = self.cleaned_data.get('employee_id')
        if not employee_id:
            raise forms.ValidationError('يجب اختيار موظف للنقل')
        
        try:
            employee = Employee.objects.get(id=employee_id)
            # Check if employee is already transferred
            if hasattr(employee, 'external_transfer'):
                raise forms.ValidationError('هذا الموظف منقول خارجياً بالفعل')
            # Check if employee is retired
            if hasattr(employee, 'retirement'):
                raise forms.ValidationError('لا يمكن نقل موظف متقاعد')
            return employee_id
        except Employee.DoesNotExist:
            raise forms.ValidationError('الموظف المحدد غير موجود')

    def clean(self):
        cleaned_data = super().clean()
        employee_id = cleaned_data.get('employee_id')
        
        if employee_id:
            # Store the employee object for easy access in views
            try:
                cleaned_data['employee'] = Employee.objects.get(id=employee_id)
            except Employee.DoesNotExist:
                raise forms.ValidationError('الموظف المحدد غير موجود')
        
        return cleaned_data


class MaternityLeaveForm(forms.ModelForm):
    """Form for adding/editing maternity leave"""
    class Meta:
        model = MaternityLeave
        fields = ['employee', 'start_date', 'notes']
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-control'}),
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filter to show only female employees
        self.fields['employee'].queryset = Employee.objects.filter(gender='female').order_by('full_name')
        self.fields['employee'].empty_label = "اختر الموظفة..."


class AddMaternityLeaveForm(forms.Form):
    """Form for selecting a female employee for maternity leave"""
    # Search field for employee
    employee_search = forms.CharField(
        label='البحث عن الموظفة (الرقم الوزاري أو الاسم)',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control', 
            'placeholder': 'اكتب الرقم الوزاري أو اسم الموظفة...',
            'id': 'employee_search_input_maternity'
        })
    )
    
    # Hidden field to store selected employee ID
    employee_id = forms.IntegerField(
        widget=forms.HiddenInput(),
        required=True
    )
    
    # Display field for selected employee name
    selected_employee_display = forms.CharField(
        label='الموظفة المحددة',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control', 
            'readonly': 'readonly',
            'id': 'selected_employee_display_maternity',
            'placeholder': 'لم يتم اختيار موظفة بعد'
        })
    )
    
    start_date = forms.DateField(
        label='تاريخ بداية الإجازة',
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    notes = forms.CharField(
        label='ملاحظات',
        required=False,
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'})
    )

    def clean_employee_id(self):
        employee_id = self.cleaned_data.get('employee_id')
        if not employee_id:
            raise forms.ValidationError('يجب اختيار موظفة لإجازة الأمومة')
        
        try:
            employee = Employee.objects.get(id=employee_id)
            # Check if employee is female
            if employee.gender != 'female':
                raise forms.ValidationError('إجازة الأمومة متاحة للموظفات الإناث فقط')
            # Check if employee already has an active maternity leave
            active_leave = MaternityLeave.objects.filter(employee=employee, is_active=True).first()
            if active_leave:
                raise forms.ValidationError('هذه الموظفة لديها إجازة أمومة نشطة بالفعل')
            return employee_id
        except Employee.DoesNotExist:
            raise forms.ValidationError('الموظفة المحددة غير موجودة')

    def clean(self):
        cleaned_data = super().clean()
        employee_id = cleaned_data.get('employee_id')
        
        if employee_id:
            # Store the employee object for easy access in views
            try:
                cleaned_data['employee'] = Employee.objects.get(id=employee_id)
            except Employee.DoesNotExist:
                raise forms.ValidationError('الموظفة المحددة غير موجودة')
        
        return cleaned_data


class TeacherSpecialtyAssignmentForm(forms.ModelForm):
    """Form for teacher specialty assignment"""

    # Campo para buscar por número ministerial
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        widget=forms.TextInput(attrs={
            'class': 'form-control search-input',
            'placeholder': 'أدخل الرقم الوزاري للبحث',
            'id': 'ministry_number_input'
        })
    )

    class Meta:
        model = TeacherSpecialtyAssignment
        fields = [
            'ministry_number', 'employee', 'original_specialty', 'assigned_specialty',
            'department', 'subjects_taught', 'notes', 'assignment_date', 'is_active'
        ]
        widgets = {
            'employee': forms.HiddenInput(),
            'original_specialty': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'سيتم تعبئته تلقائياً بعد البحث',
                'readonly': 'readonly'
            }),
            'assigned_specialty': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل التخصص المحمل عليه'
            }),
            'department': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل القسم'
            }),
            'subjects_taught': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل المواد التي يدرسها'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية (اختياري)'
            }),
            'assignment_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        }

    def clean(self):
        cleaned_data = super().clean()
        ministry_number = cleaned_data.get('ministry_number')

        if ministry_number:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                cleaned_data['employee'] = employee
            except Employee.DoesNotExist:
                raise forms.ValidationError('لم يتم العثور على موظف بهذا الرقم الوزاري')

        return cleaned_data


class EmployeeAssignmentForm(forms.ModelForm):
    """Form for creating and editing employee assignments"""
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['employee'].required = False
        self.fields['assignment_date'].required = True
        self.fields['assigned_role'].required = True
        self.fields['department'].required = True

    class Meta:
        model = EmployeeAssignment
        fields = ['employee', 'department', 'assigned_role', 'assignment_date', 'end_date', 'notes']
        widgets = {
            'employee': forms.HiddenInput(attrs={'required': False}),
            'department': forms.TextInput(attrs={'class': 'form-control'}),
            'assigned_role': forms.TextInput(attrs={'class': 'form-control'}),
            'assignment_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        ministry_number = cleaned_data.get('ministry_number')
        employee_id = cleaned_data.get('employee_id')
        assigned_role = cleaned_data.get('assigned_role')
        assignment_date = cleaned_data.get('assignment_date')

        # Validate employee
        if not employee_id and not ministry_number:
            raise forms.ValidationError('الرجاء إدخال الرقم الوزاري للموظف')

        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                cleaned_data['employee'] = employee
            except Employee.DoesNotExist:
                raise forms.ValidationError('لم يتم العثور على موظف بهذا الرقم الوزاري')
        elif employee_id:
            try:
                employee = Employee.objects.get(id=employee_id)
                cleaned_data['employee'] = employee
            except Employee.DoesNotExist:
                raise forms.ValidationError('الموظف المحدد غير موجود')

        # Validate required fields
        if not assigned_role:
            raise forms.ValidationError('الرجاء إدخال الوظيفة المكلف بها')

        if not assignment_date:
            raise forms.ValidationError('الرجاء إدخال تاريخ التكليف')

        return cleaned_data


class SharedEmployeeForm(forms.ModelForm):
    """Form for creating and editing shared employee assignments"""
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['employee'].required = False
        self.fields['sharing_date'].required = True
        self.fields['original_department'].required = True
        self.fields['shared_department'].required = True

        # Get all departments for shared_department choices
        departments = Department.objects.all().order_by('name')
        department_choices = [(dept.name, dept.name) for dept in departments]
        department_choices.insert(0, ('', 'اختر القسم المشترك...'))

        # Update shared_department widget to be a select field
        self.fields['shared_department'].widget = forms.Select(
            choices=department_choices,
            attrs={'class': 'form-control select2', 'data-placeholder': 'اختر القسم المشترك...'}
        )

        # If editing existing record, set initial values
        if self.instance and self.instance.pk:
            self.fields['ministry_number'].initial = self.instance.employee.ministry_number
            self.fields['employee_name'].initial = self.instance.employee.full_name
            self.fields['employee_id'].initial = self.instance.employee.id

    class Meta:
        model = SharedEmployee
        fields = ['employee', 'original_department', 'shared_department', 'sharing_date', 'notes']
        widgets = {
            'employee': forms.HiddenInput(attrs={'required': False}),
            'original_department': forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly'}),
            'shared_department': forms.Select(attrs={'class': 'form-control select2'}),
            'sharing_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        ministry_number = cleaned_data.get('ministry_number')
        employee_id = cleaned_data.get('employee_id')
        shared_department = cleaned_data.get('shared_department')
        sharing_date = cleaned_data.get('sharing_date')

        # Validate employee
        if not employee_id and not ministry_number:
            raise forms.ValidationError('الرجاء إدخال الرقم الوزاري للموظف')

        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                cleaned_data['employee'] = employee
                # Set original department from employee's current school
                cleaned_data['original_department'] = employee.school or 'غير محدد'
            except Employee.DoesNotExist:
                raise forms.ValidationError('لم يتم العثور على موظف بهذا الرقم الوزاري')
        elif employee_id:
            try:
                employee = Employee.objects.get(id=employee_id)
                cleaned_data['employee'] = employee
                # Set original department from employee's current school
                cleaned_data['original_department'] = employee.school or 'غير محدد'
            except Employee.DoesNotExist:
                raise forms.ValidationError('الموظف المحدد غير موجود')

        # Validate required fields
        if not shared_department:
            raise forms.ValidationError('الرجاء إدخال القسم المشترك')

        if not sharing_date:
            raise forms.ValidationError('الرجاء إدخال تاريخ الإشراك')

        return cleaned_data


class EmployeeGradeForm(forms.ModelForm):
    """Form for creating and editing employee grades"""
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control ministry-input',
            'id': 'id_ministry_number',
            'placeholder': 'أدخل الرقم الوزاري'
        })
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'readonly': 'readonly',
            'id': 'id_employee_name',
            'placeholder': 'سيتم عرض اسم الموظف هنا'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Hide the employee field from the form
        self.fields['employee'].widget = forms.HiddenInput()
        self.fields['employee'].required = False
        self.fields['grade_date'].required = True
        self.fields['grade'].required = True

        # Set widget attributes for other fields
        self.fields['grade_date'].widget.attrs.update({
            'class': 'form-control',
            'type': 'date',
            'id': 'id_grade_date'
        })

        self.fields['grade'].widget.attrs.update({
            'class': 'form-control',
            'id': 'id_grade'
        })

        self.fields['notes'].widget.attrs.update({
            'class': 'form-control',
            'id': 'id_notes',
            'rows': 3
        })

        self.fields['promotion_type'].widget.attrs.update({
            'class': 'form-control',
            'id': 'id_promotion_type'
        })

        self.fields['years_in_grade'].widget.attrs.update({
            'class': 'form-control',
            'id': 'id_years_in_grade',
            'min': '0'
        })

        # If editing existing record, set initial values
        if self.instance and self.instance.pk:
            self.fields['ministry_number'].initial = self.instance.employee.ministry_number
            self.fields['employee_name'].initial = self.instance.employee.full_name
            self.fields['employee_id'].initial = self.instance.employee.id

    def clean(self):
        cleaned_data = super().clean()
        employee_id = cleaned_data.get('employee_id')
        ministry_number = cleaned_data.get('ministry_number')

        # If no employee_id provided, try to find employee by ministry number
        if not employee_id and ministry_number:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                cleaned_data['employee'] = employee
            except Employee.DoesNotExist:
                raise forms.ValidationError(f'لم يتم العثور على موظف بالرقم الوزاري: {ministry_number}')
        elif employee_id:
            try:
                employee = Employee.objects.get(id=employee_id)
                cleaned_data['employee'] = employee
            except Employee.DoesNotExist:
                raise forms.ValidationError('الموظف المحدد غير موجود')
        else:
            raise forms.ValidationError('الرجاء تحديد الموظف')

        return cleaned_data

    class Meta:
        model = EmployeeGrade
        fields = ['employee', 'grade', 'promotion_type', 'years_in_grade', 'grade_date', 'notes']


class JobGradeForm(forms.ModelForm):
    """Form for creating and editing job grades"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['name'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'أدخل اسم الدرجة الوظيفية'
        })
        self.fields['description'].widget.attrs.update({
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'أدخل وصف الدرجة (اختياري)'
        })

    class Meta:
        model = JobGrade
        fields = ['name', 'description']


class PromotionTypeForm(forms.ModelForm):
    """Form for creating and editing promotion types"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['name'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'أدخل نوع الترفيع'
        })
        self.fields['description'].widget.attrs.update({
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'أدخل وصف نوع الترفيع (اختياري)'
        })

    class Meta:
        model = PromotionType
        fields = ['name', 'description']
