{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .btn-group .btn {
        margin-right: 2px;
    }

    .btn-group .btn:last-child {
        margin-right: 0;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    /* Custom button styles for better consistency */
    .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        color: #fff;
        transform: translateY(-1px);
        transition: all 0.2s ease-in-out;
    }

    .btn-outline-success:hover {
        background-color: #28a745;
        border-color: #28a745;
        color: #fff;
        transform: translateY(-1px);
        transition: all 0.2s ease-in-out;
    }

    .btn-outline-primary:hover {
        transform: translateY(-1px);
        transition: all 0.2s ease-in-out;
    }

    /* Button loading state */
    .btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    /* Form buttons spacing */
    .text-end .btn {
        margin-left: 0.5rem;
    }

    .text-end .btn:first-child {
        margin-left: 0;
    }
    }
    
    .search-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Search input enhancements */
    #searchTerm {
        transition: all 0.3s ease;
    }

    #searchTerm:focus {
        transform: scale(1.02);
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    #searchTerm:disabled {
        background-color: #f8f9fa;
        opacity: 0.8;
    }

    /* Search hint text */
    .form-text {
        font-size: 0.875rem;
        margin-top: 0.25rem;
        transition: color 0.3s ease;
    }

    .form-text .fas {
        margin-right: 0.25rem;
    }

    /* Input group focus effect */
    .input-group-focus {
        transform: scale(1.01);
        transition: transform 0.2s ease;
    }

    /* Success text color */
    .text-success {
        color: #28a745 !important;
    }

    /* New Department Select2 dropdown width - Fixed positioning */
    .select2-container {
        width: 100% !important;
        position: relative !important;
    }

    .select2-dropdown {
        min-width: 300px !important;
        max-width: 450px !important;
        width: auto !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .select2-results__option {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 6px 10px;
        font-size: 13px;
    }

    /* Specific styling for new department dropdown */
    #newDepartment + .select2-container .select2-dropdown {
        min-width: 350px !important;
        max-width: 500px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
    }

    #newDepartment + .select2-container .select2-results__option {
        font-size: 13px;
        line-height: 1.3;
        padding: 8px 12px;
    }

    /* Make the dropdown appear above other elements - Simple and working */
    .select2-dropdown {
        z-index: 9999 !important;
        max-height: 300px !important;
        overflow-y: auto !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: absolute !important;
    }

    /* Ensure dropdown is visible when opened */
    .select2-container--open .select2-dropdown {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Wide dropdown class for better visibility - Simplified */
    .wide-dropdown {
        min-width: 380px !important;
        max-width: 520px !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 0.375rem !important;
        background: white !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Force visibility for all select2 dropdowns */
    .select2-dropdown[style*="display: none"] {
        display: block !important;
    }

    .select2-dropdown[style*="visibility: hidden"] {
        visibility: visible !important;
    }

    .wide-dropdown .select2-results__option {
        padding: 8px 12px;
        font-size: 13px;
        line-height: 1.4;
        border-bottom: 1px solid #f8f9fa;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .wide-dropdown .select2-results__option:hover {
        background-color: #e3f2fd;
    }

    .wide-dropdown .select2-results__option--highlighted {
        background-color: #007bff !important;
        color: white !important;
    }

    /* Ensure dropdown doesn't exceed viewport */
    .wide-dropdown {
        max-width: calc(100vw - 40px) !important;
    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 768px) {
        .select2-dropdown {
            min-width: 250px !important;
            max-width: calc(100vw - 20px) !important;
        }

        #newDepartment + .select2-container .select2-dropdown {
            min-width: 280px !important;
            max-width: calc(100vw - 20px) !important;
        }

        .wide-dropdown {
            min-width: 280px !important;
            max-width: calc(100vw - 20px) !important;
        }
    }

    /* Better text handling for long department names */
    .select2-results__option {
        word-wrap: break-word;
        max-height: 50px;
        overflow: hidden;
    }


    }
    
    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 36px;
        padding-left: 12px;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{{ title }}</h6>
                    <div>
                        <a href="{% url 'home:add_employee_transfer' %}" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus"></i> نقل موظف
                        </a>
                        <a href="{% url 'home:print_transfer_letters' %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-right"></i> العودة لطباعة كتب النقل
                        </a>
                    </div>
                </div>
                
                <!-- Search and Add Section -->
                <div class="card-body">
                    <div class="search-section" id="searchSection" style="display: none;">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-plus-circle"></i> إضافة نقل موظف جديد
                        </h6>
                        
                        <form id="addTransferForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="searchTerm" class="form-label">البحث عن الموظف</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="searchTerm"
                                               placeholder="أدخل الرقم الوزاري أو اسم الموظف واضغط Enter">
                                        <button class="btn btn-primary" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle"></i> يمكنك الضغط على مفتاح الإدخال (Enter) للبحث السريع
                                    </small>
                                </div>
                            </div>
                            
                            <div id="employeeDetails" style="display: none;">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الرقم الوزاري</label>
                                        <input type="text" class="form-control" id="ministryNumber" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control" id="employeeName" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">التخصص</label>
                                        <input type="text" class="form-control" id="specialization" readonly>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الخدمة الفعلية</label>
                                        <input type="text" class="form-control" id="actualService" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">القسم الحالي</label>
                                        <input type="text" class="form-control" id="currentDepartment" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">القسم الجديد <span class="text-danger">*</span></label>
                                        <select class="form-control select2" id="newDepartment" required>
                                            <option value="">اختر القسم الجديد...</option>
                                            {% for department in departments %}
                                            <option value="{{ department.id }}">{{ department.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">صفة النقل <span class="text-danger">*</span></label>
                                        <select class="form-control" id="transferType" required>
                                            <option value="">اختر صفة النقل...</option>
                                            <option value="internal_transfer">النقل الداخلي</option>
                                            <option value="temporary_assignment">التكليف المؤقت</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">التنسيب <span class="text-danger">*</span></label>
                                        <select class="form-control" id="endorsement" required>
                                            <option value="">اختر التنسيب...</option>
                                            <option value="admin_financial_manager">تنسيب مدير الشؤون الادارية والمالية</option>
                                            <option value="hr_committee">تنسيب لجنة الموارد البشرية</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" id="notes" rows="2" placeholder="أدخل الملاحظات (اختياري)"></textarea>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button type="button" class="btn btn-outline-danger" id="cancelBtn">
                                        <i class="fas fa-times"></i> إلغاء
                                    </button>
                                    <button type="submit" class="btn btn-outline-success">
                                        <i class="fas fa-save"></i> حفظ النقل
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Transfers Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>الرقم الوزاري</th>
                                    <th>الاسم الكامل</th>
                                    <th>التخصص</th>
                                    <th>الخدمة الفعلية</th>
                                    <th>القسم الحالي</th>
                                    <th>القسم الجديد</th>
                                    <th>صفة النقل</th>
                                    <th>التنسيب</th>
                                    <th>ملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="transfersTableBody">
                                {% for transfer in transfers %}
                                <tr>
                                    <td>{{ transfer.ministry_number }}</td>
                                    <td>{{ transfer.employee_name }}</td>
                                    <td>{{ transfer.specialization|default:"-" }}</td>
                                    <td>{{ transfer.calculated_actual_service|default:transfer.actual_service|default:"-" }}</td>
                                    <td>{{ transfer.current_department }}</td>
                                    <td>{{ transfer.new_department.name }}</td>
                                    <td>
                                        {% if transfer.transfer_type == 'internal_transfer' %}
                                            النقل الداخلي
                                        {% else %}
                                            التكليف المؤقت
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transfer.endorsement == 'admin_financial_manager' %}
                                            تنسيب مدير الشؤون الادارية والمالية
                                        {% else %}
                                            تنسيب لجنة الموارد البشرية
                                        {% endif %}
                                    </td>
                                    <td>{{ transfer.notes|default:"-" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="/print-transfer-letter/{{ transfer.id }}/"
                                               class="btn btn-primary btn-sm"
                                               title="طباعة كتاب النقل">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <a href="{% url 'home:edit_employee_transfer' transfer.id %}"
                                               class="btn btn-warning btn-sm" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'home:delete_employee_transfer_confirm' transfer.id %}"
                                               class="btn btn-danger btn-sm" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="10" class="text-center">لا توجد عمليات نقل مسجلة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<script>
$(document).ready(function() {
    // Destroy any existing Select2 instances first
    $('.select2').select2('destroy');

    // Initialize Select2 - Clean approach
    $('.select2').select2({
        placeholder: 'ابحث عن القسم...',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });

    // Re-initialize new department dropdown specifically
    $('#newDepartment').select2('destroy').select2({
        placeholder: 'ابحث عن القسم الجديد...',
        allowClear: true,
        width: '100%',
        dropdownCssClass: 'wide-dropdown',
        language: {
            noResults: function() {
                return "لا توجد أقسام متاحة";
            },
            searching: function() {
                return "جاري البحث عن الأقسام...";
            }
        }
    });

    // Test dropdown functionality
    $('#newDepartment').on('select2:open', function() {
        console.log('New department dropdown opened successfully');
    });

    $('#newDepartment').on('select2:close', function() {
        console.log('New department dropdown closed');
    });

    // Additional Enter key handler using jQuery's on method with namespace
    $('#searchTerm').off('keydown.search').on('keydown.search', function(e) {
        if (e.keyCode === 13) {
            console.log('Enter key detected - namespaced event');
            e.preventDefault();
            e.stopImmediatePropagation();

            // Add small delay to ensure DOM is ready
            setTimeout(function() {
                searchEmployee();
            }, 50);

            return false;
        }
    });

    // Vanilla JavaScript approach as backup
    setTimeout(function() {
        const searchInput = document.getElementById('searchTerm');
        if (searchInput) {
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.keyCode === 13) {
                    console.log('Enter key detected - vanilla JS');
                    e.preventDefault();
                    searchEmployee();
                }
            }, true);
        }
    }, 1000);


    
    // Search employee function
    function searchEmployee() {
        const searchTerm = $('#searchTerm').val().trim();
        if (!searchTerm) {
            alert('يرجى إدخال الرقم الوزاري أو اسم الموظف');
            $('#searchTerm').focus();
            return;
        }

        // Add loading state to search button
        const $searchBtn = $('#searchBtn');
        const originalBtnHtml = $searchBtn.html();
        $searchBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري البحث...');
        $searchBtn.prop('disabled', true);

        // Also disable the search input during search
        $('#searchTerm').prop('disabled', true);

        $.post('{% url "home:search_employee_for_transfer" %}', {
            'search_term': searchTerm,
            'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
        }, function(response) {
            if (response.success) {
                const emp = response.employee;
                $('#ministryNumber').val(emp.ministry_number);
                $('#employeeName').val(emp.full_name);
                $('#specialization').val(emp.specialization);
                $('#actualService').val(emp.actual_service);
                $('#currentDepartment').val(emp.current_department);
                $('#employeeDetails').show();

                // Show success message
                const successMsg = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                    '<i class="fas fa-check-circle"></i> تم العثور على الموظف: ' + emp.full_name +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                    '</div>');
                $('#searchSection .card-body').prepend(successMsg);

                // Auto-hide success message after 3 seconds
                setTimeout(function() {
                    successMsg.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);

                // Focus on next input field
                setTimeout(function() {
                    $('#newDepartment').focus();
                }, 100);
            } else {
                alert(response.error);
                $('#employeeDetails').hide();
                $('#searchTerm').focus();
            }
        }).fail(function() {
            alert('حدث خطأ أثناء البحث');
            $('#searchTerm').focus();
        }).always(function() {
            // Reset button and input state
            $searchBtn.html(originalBtnHtml);
            $searchBtn.prop('disabled', false);
            $('#searchTerm').prop('disabled', false);
        });
    }

    // Search employee on button click
    $('#searchBtn').click(function() {
        searchEmployee();
    });

    // Search employee on Enter key press - Multiple approaches for maximum compatibility
    $(document).on('keydown', '#searchTerm', function(e) {
        if (e.keyCode === 13 || e.which === 13) {
            console.log('Enter key detected on search term - keydown');
            e.preventDefault();
            e.stopPropagation();
            searchEmployee();
            return false;
        }
    });

    // Alternative approach using direct binding
    $('#searchTerm').keydown(function(e) {
        if (e.keyCode === 13) {
            console.log('Enter key detected - direct binding');
            e.preventDefault();
            searchEmployee();
            return false;
        }
    });

    // Form submission prevention
    $('#searchTerm').closest('form').on('submit', function(e) {
        e.preventDefault();
        console.log('Form submission prevented, triggering search');
        searchEmployee();
        return false;
    });

    // Alternative Enter key handler using input event
    $('#searchTerm').on('input', function(e) {
        if (e.originalEvent && e.originalEvent.inputType === 'insertLineBreak') {
            console.log('Enter detected via input event');
            searchEmployee();
        }
        // Auto-clear search when starting new search
        if ($(this).val().length === 0) {
            $('#employeeDetails').hide();
        }
    });

    // Search input focus and blur effects
    $('#searchTerm').on('focus', function() {
        $(this).parent().addClass('input-group-focus');
        $('.form-text').addClass('text-success').removeClass('text-muted');
        console.log('Search input focused');
    });

    $('#searchTerm').on('blur', function() {
        $(this).parent().removeClass('input-group-focus');
        $('.form-text').addClass('text-muted').removeClass('text-success');
    });



    // Prevent form submission on Enter in the main form
    $('#addTransferForm').on('keypress', function(e) {
        if (e.which === 13 || e.keyCode === 13) {
            // Only allow Enter in textarea fields
            if (e.target.tagName.toLowerCase() !== 'textarea') {
                e.preventDefault();
            }
        }
    });
    

    
    // Submit form
    $('#addTransferForm').submit(function(e) {
        e.preventDefault();

        const editId = $(this).attr('data-edit-id');
        const isEdit = editId && editId !== '';

        const formData = {
            'ministry_number': $('#ministryNumber').val(),
            'employee_name': $('#employeeName').val(),
            'specialization': $('#specialization').val(),
            'actual_service': $('#actualService').val(),
            'current_department': $('#currentDepartment').val(),
            'new_department_id': $('#newDepartment').val(),
            'transfer_type': $('#transferType').val(),
            'endorsement': $('#endorsement').val(),
            'notes': $('#notes').val(),
            'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
        };

        if (isEdit) {
            formData['transfer_id'] = editId;
        }

        const url = isEdit ? '{% url "home:update_employee_transfer" %}' : '{% url "home:add_employee_transfer" %}';

        // Add loading state to submit button
        const $submitBtn = $('#addTransferForm button[type="submit"]');
        const originalSubmitHtml = $submitBtn.html();
        $submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
        $submitBtn.prop('disabled', true);

        $.post(url, formData, function(response) {
            if (response.success) {
                // Show success message
                const successMsg = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                    '<i class="fas fa-check-circle"></i> ' + response.message +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                    '</div>');
                $('.card-body').prepend(successMsg);

                // Auto-hide success message and reload
                setTimeout(function() {
                    successMsg.fadeOut(300, function() {
                        location.reload();
                    });
                }, 2000);
            } else {
                // Reset submit button on error
                $submitBtn.html(originalSubmitHtml);
                $submitBtn.prop('disabled', false);
                alert(response.error);
            }
        }).fail(function() {
            // Reset submit button on failure
            $submitBtn.html(originalSubmitHtml);
            $submitBtn.prop('disabled', false);
            alert('حدث خطأ أثناء حفظ البيانات');
        });
    });










});
</script>
{% endblock %}
