from django.urls import path
from . import views
from . import views_unpaid

app_name = 'leaves'

urlpatterns = [
    # AJAX endpoints
    path('get-employee/', views.get_employee_by_ministry_number, name='get_employee_by_ministry_number'),
    path('get-used-leave-days/', views.get_used_leave_days, name='get_used_leave_days'),
    path('get-leave-balance-whatsapp/<int:employee_id>/', views.get_leave_balance_whatsapp, name='get_leave_balance_whatsapp'),
    path('check-annual-balance/', views.check_annual_balance, name='check_annual_balance'),

    path('', views.leave_list, name='leave_list'),
    path('add/', views.leave_create, name='leave_create'),
    path('<int:pk>/', views.leave_detail, name='leave_detail'),
    path('<int:pk>/edit/', views.leave_update, name='leave_update'),
    path('<int:pk>/delete/', views.leave_delete, name='leave_delete'),
    path('types/', views.leave_type_list, name='leave_type_list'),
    path('types/add/', views.leave_type_create, name='leave_type_create'),
    path('types/<int:pk>/edit/', views.leave_type_update, name='leave_type_update'),
    path('balance/', views.leave_balance_list, name='leave_balance_list'),
    path('balance/add/', views.leave_balance_create, name='leave_balance_create'),
    path('balance/<int:pk>/edit/', views.leave_balance_update, name='leave_balance_update'),
    path('balance/<int:pk>/delete/', views.leave_balance_delete, name='leave_balance_delete'),
    path('balance/rollover/', views.balance_rollover, name='balance_rollover'),
    path('departures/', views.departure_list, name='departure_list'),
    path('departures/add/', views.departure_create, name='departure_create'),
    path('departures/<int:pk>/', views.departure_detail, name='departure_detail'),
    path('departures/<int:pk>/edit/', views.departure_update, name='departure_update'),
    path('departures/<int:pk>/delete/', views.departure_delete, name='departure_delete'),
    path('reports/', views.leave_reports, name='leave_reports'),
    path('statistics/', views.leave_statistics, name='leave_statistics'),

    # Unpaid leave URLs
    path('unpaid/', views_unpaid.unpaid_leave_list, name='unpaid_leave_list'),
    path('unpaid/add/', views_unpaid.unpaid_leave_create, name='unpaid_leave_create'),
    path('unpaid/<int:pk>/', views_unpaid.unpaid_leave_detail, name='unpaid_leave_detail'),
    path('unpaid/<int:pk>/edit/', views_unpaid.unpaid_leave_update, name='unpaid_leave_update'),
    path('unpaid/get-employee/', views_unpaid.get_employee_by_ministry_number, name='get_employee_by_ministry_number'),
]
