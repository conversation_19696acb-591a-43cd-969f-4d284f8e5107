#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريپت تحميل XAMPP
XAMPP Download Script
"""

import requests
import os
import sys
from pathlib import Path

def download_xampp():
    """تحميل XAMPP"""
    print("تحميل XAMPP...")
    print("=" * 30)
    
    # رابط تحميل XAMPP (النسخة الأحدث)
    xampp_url = "https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download"
    filename = "xampp-installer.exe"
    
    try:
        print("بدء التحميل...")
        print("هذا قد يستغرق بضع دقائق حسب سرعة الإنترنت...")
        
        response = requests.get(xampp_url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\rالتقدم: {percent:.1f}% ({downloaded // 1024 // 1024} MB)", end='')
        
        print(f"\n✓ تم تحميل XAMPP بنجاح: {filename}")
        print(f"حجم الملف: {os.path.getsize(filename) // 1024 // 1024} MB")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ خطأ في التحميل: {str(e)}")
        return False
    except Exception as e:
        print(f"✗ خطأ عام: {str(e)}")
        return False

def install_xampp():
    """تثبيت XAMPP"""
    filename = "xampp-installer.exe"
    
    if not os.path.exists(filename):
        print("✗ ملف XAMPP غير موجود")
        return False
    
    print("\nبدء تثبيت XAMPP...")
    print("سيتم فتح معالج التثبيت...")
    print("اتبع التعليمات في معالج التثبيت")
    
    try:
        import subprocess
        subprocess.run([filename], check=False)
        return True
    except Exception as e:
        print(f"✗ خطأ في تشغيل المثبت: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("تحميل وتثبيت XAMPP")
    print("=" * 25)
    
    # التحقق من وجود XAMPP
    xampp_paths = [
        "C:\\xampp\\xampp-control.exe",
        "C:\\Program Files\\XAMPP\\xampp-control.exe"
    ]
    
    for path in xampp_paths:
        if os.path.exists(path):
            print(f"✓ تم العثور على XAMPP في: {path}")
            print("XAMPP مثبت بالفعل!")
            return True
    
    print("لم يتم العثور على XAMPP، سيتم تحميله...")
    
    # تحميل XAMPP
    if download_xampp():
        print("\nهل تريد تثبيت XAMPP الآن؟")
        choice = input("اكتب 'y' للمتابعة أو أي شيء آخر للإلغاء: ")
        
        if choice.lower() == 'y':
            install_xampp()
            print("\nبعد اكتمال التثبيت:")
            print("1. شغل XAMPP Control Panel")
            print("2. اضغط Start بجانب MySQL")
            print("3. شغل: python setup_mysql_complete.py")
        else:
            print("يمكنك تشغيل xampp-installer.exe لاحقاً لإكمال التثبيت")
    
    return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nتم إلغاء العملية")
    except Exception as e:
        print(f"خطأ: {str(e)}")
        sys.exit(1)
