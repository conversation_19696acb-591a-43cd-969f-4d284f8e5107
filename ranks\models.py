from django.db import models
from django.utils.translation import gettext_lazy as _
from employees.models import Employee

class RankType(models.Model):
    """Model for storing rank types"""
    name = models.CharField(_('Name'), max_length=100)
    description = models.TextField(_('Description'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Rank Type')
        verbose_name_plural = _('Rank Types')
        ordering = ['name']

    def __str__(self):
        return self.name

class EmployeeRank(models.Model):
    """Model for storing employee ranks"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='ranks')
    rank_type = models.ForeignKey(RankType, on_delete=models.CASCADE, related_name='employee_ranks')
    date_obtained = models.DateField(_('Date Obtained'))
    notes = models.TextField(_('Notes'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Employee Rank')
        verbose_name_plural = _('Employee Ranks')
        ordering = ['-date_obtained']

    def __str__(self):
        return f"{self.employee.full_name} - {self.rank_type.name}"


class Course(models.Model):
    """Model for storing course types"""
    name = models.CharField(_('Course Name'), max_length=200, unique=True)
    description = models.TextField(_('Description'), blank=True, null=True)
    hours = models.PositiveIntegerField(_('Course Hours'), default=0, help_text=_('Number of course hours'))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Course')
        verbose_name_plural = _('Courses')
        ordering = ['name']

    def __str__(self):
        return self.name


class EmployeeCourse(models.Model):
    """Model for storing employee courses"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='courses')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='employees')
    hours = models.PositiveIntegerField(_('Hours'))
    completion_date = models.DateField(_('Completion Date'))
    certificate_number = models.CharField(_('Certificate Number'), max_length=100, blank=True, null=True)
    notes = models.TextField(_('Notes'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Employee Course')
        verbose_name_plural = _('Employee Courses')
        ordering = ['-completion_date']
        unique_together = ['employee', 'course', 'completion_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.course.name}"
