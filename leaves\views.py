from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Sum
from django.utils import timezone
from django.http import JsonResponse
from .models import LeaveT<PERSON>, LeaveBalance, Leave, Departure
from .forms import LeaveTypeForm, LeaveBalanceForm, LeaveForm, DepartureForm
from employees.models import Employee

@login_required
def leave_list(request):
    search_query = request.GET.get('search', '')
    if search_query:
        leaves = Leave.objects.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query) |
            Q(reason__icontains=search_query)
        )
    else:
        leaves = Leave.objects.all()

    return render(request, 'leaves/leave_list.html', {
        'leaves': leaves,
        'search_query': search_query
    })

@login_required
def leave_create(request):
    if request.method == 'POST':
        print('POST data:', request.POST)

        # Create a copy of POST data to modify
        post_data = request.POST.copy()

        # Get employee_id from POST data
        employee_id = post_data.get('employee_id')
        ministry_number = post_data.get('ministry_number')

        # If we have ministry_number but no employee_id, try to get employee_id
        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ministry number: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                return render(request, 'leaves/leave_form.html', {'form': LeaveForm(post_data)})

        # If we have employee_id but no employee, set employee
        if employee_id and not post_data.get('employee'):
            try:
                employee = Employee.objects.get(id=employee_id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ID: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                return render(request, 'leaves/leave_form.html', {'form': LeaveForm(post_data)})

        # Create form with modified POST data
        form = LeaveForm(post_data)

        print('Modified POST data:', post_data)

        if form.is_valid():
            try:
                # Get the employee
                employee_id = post_data.get('employee')
                if not employee_id:
                    employee_id = post_data.get('employee_id')

                if not employee_id:
                    messages.error(request, 'الرجاء اختيار موظف')
                    return render(request, 'leaves/leave_form.html', {'form': form})

                try:
                    employee = Employee.objects.get(id=employee_id)
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                    return render(request, 'leaves/leave_form.html', {'form': form})

                # Create a new leave
                leave = form.save(commit=False)
                leave.employee = employee

                # Calculate days count if not provided
                if not leave.days_count:
                    delta = leave.end_date - leave.start_date
                    leave.days_count = delta.days + 1

                # Set status to 'approved' by default
                leave.status = 'approved'

                # Save the leave
                leave.save()
                print('Leave saved successfully with ID:', leave.id)

                # Update leave balance
                try:
                    balance = LeaveBalance.objects.get(
                        employee=leave.employee,
                        leave_type=leave.leave_type,
                        year=timezone.now().year
                    )
                    balance.used_balance += leave.days_count
                    balance.save()
                    print(f'Updated leave balance: {balance.id}, new used balance: {balance.used_balance}')
                except LeaveBalance.DoesNotExist:
                    print('No leave balance found for this employee, leave type and year')

                messages.success(request, 'تم إضافة الإجازة بنجاح.')

                # Create notification for new leave request
                try:
                    from notifications.utils import notify_leave_request
                    notify_leave_request(leave, request.user)
                except ImportError:
                    pass  # Notifications app not available

                return redirect('leaves:leave_list')

            except Exception as e:
                print('Error saving leave:', str(e))
                messages.error(request, f'حدث خطأ أثناء حفظ الإجازة: {str(e)}')
        else:
            print('Form is invalid. Errors:', form.errors)
            # Display form errors to the user
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{error}')
                print(f'Field {field} errors: {errors}')
    else:
        form = LeaveForm()

    return render(request, 'leaves/leave_form.html', {'form': form})

@login_required
def leave_detail(request, pk):
    leave = get_object_or_404(Leave, pk=pk)
    return render(request, 'leaves/leave_detail.html', {'leave': leave})

@login_required
def leave_update(request, pk):
    leave = get_object_or_404(Leave, pk=pk)
    old_days_count = leave.days_count

    if request.method == 'POST':
        form = LeaveForm(request.POST, instance=leave)
        if form.is_valid():
            updated_leave = form.save(commit=False)
            # Calculate days count if not provided
            if not updated_leave.days_count:
                delta = updated_leave.end_date - updated_leave.start_date
                updated_leave.days_count = delta.days + 1

            # Update leave balance
            try:
                balance = LeaveBalance.objects.get(
                    employee=leave.employee,
                    leave_type=leave.leave_type,
                    year=timezone.now().year
                )
                balance.used_balance = balance.used_balance - old_days_count + updated_leave.days_count
                balance.save()
            except LeaveBalance.DoesNotExist:
                pass

            updated_leave.save()
            messages.success(request, 'تم تحديث بيانات الإجازة بنجاح.')
            return redirect('leaves:leave_detail', pk=leave.pk)
        else:
            # Display form errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{error}')
    else:
        # Initialize form with employee data for the template
        form = LeaveForm(instance=leave, initial={
            'ministry_number': leave.employee.ministry_number,
            'employee_name': leave.employee.full_name,
            'employee_id': leave.employee.id
        })
    return render(request, 'leaves/leave_form.html', {'form': form, 'leave': leave})

@login_required
def leave_delete(request, pk):
    leave = get_object_or_404(Leave, pk=pk)
    if request.method == 'POST':
        # Update leave balance
        try:
            balance = LeaveBalance.objects.get(
                employee=leave.employee,
                leave_type=leave.leave_type,
                year=timezone.now().year
            )
            balance.used_balance -= leave.days_count
            balance.save()
        except LeaveBalance.DoesNotExist:
            pass

        leave.delete()
        messages.success(request, 'تم حذف الإجازة بنجاح.')
        return redirect('leaves:leave_list')
    return render(request, 'leaves/leave_confirm_delete.html', {'leave': leave})

@login_required
def leave_type_list(request):
    leave_types = LeaveType.objects.all()
    return render(request, 'leaves/leave_type_list.html', {'leave_types': leave_types})

@login_required
def leave_type_create(request):
    if request.method == 'POST':
        form = LeaveTypeForm(request.POST)
        if form.is_valid():
            leave_type = form.save()
            messages.success(request, 'تم إضافة نوع الإجازة بنجاح.')
            return redirect('leaves:leave_type_list')
    else:
        form = LeaveTypeForm()
    return render(request, 'leaves/leave_type_form.html', {'form': form})

@login_required
def leave_type_update(request, pk):
    leave_type = get_object_or_404(LeaveType, pk=pk)
    if request.method == 'POST':
        form = LeaveTypeForm(request.POST, instance=leave_type)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث نوع الإجازة بنجاح.')
            return redirect('leaves:leave_type_list')
    else:
        form = LeaveTypeForm(instance=leave_type)
    return render(request, 'leaves/leave_type_form.html', {'form': form, 'leave_type': leave_type})

@login_required
def leave_balance_list(request):
    balances = LeaveBalance.objects.all()
    return render(request, 'leaves/leave_balance_list.html', {'balances': balances})

@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee details by ministry number"""
    ministry_number = request.GET.get('ministry_number', '')
    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)
        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})

@login_required
def get_used_leave_days(request):
    """AJAX view to get used leave days for an employee"""
    employee_id = request.GET.get('employee_id', '')
    leave_type_id = request.GET.get('leave_type_id', '')
    year = request.GET.get('year', '')

    if not all([employee_id, leave_type_id, year]):
        return JsonResponse({'success': False, 'error': 'الرجاء توفير جميع البيانات المطلوبة'})

    try:
        # Get all leaves for this employee, leave type and year
        leaves = Leave.objects.filter(
            employee_id=employee_id,
            leave_type_id=leave_type_id,
            start_date__year=year,
            status='approved'
        )

        # Calculate total used days
        used_days = sum(leave.days_count for leave in leaves)

        return JsonResponse({
            'success': True,
            'used_days': used_days
        })
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def get_leave_balance_whatsapp(request, employee_id):
    """Get leave balance and last leave date for WhatsApp message"""
    try:
        # Get employee
        employee = Employee.objects.get(pk=employee_id)

        # Get current year
        current_year = timezone.now().year

        # Get all leave balances for this employee for the current year
        balances = LeaveBalance.objects.filter(
            employee_id=employee_id,
            year=current_year
        )

        # Get the latest leave for this employee
        latest_leave = Leave.objects.filter(
            employee_id=employee_id,
            status='approved'
        ).order_by('-end_date').first()

        # Format phone number (remove any non-digit characters and ensure it starts with 962)
        phone = employee.phone_number
        if phone:
            # Remove any non-digit characters
            phone = ''.join(filter(str.isdigit, phone))
            # Ensure it starts with 962 (Jordan country code)
            if phone.startswith('0'):
                phone = '962' + phone[1:]
            elif not phone.startswith('962'):
                phone = '962' + phone
        else:
            return JsonResponse({'success': False, 'error': 'لا يوجد رقم هاتف لهذا الموظف'})

        # Construct message
        message = f"مرحباً {employee.full_name}،\n\nأرصدة الإجازات الخاصة بك لعام {current_year}:\n"

        for balance in balances:
            message += f"- {balance.leave_type.get_name_display()}: {balance.remaining_balance} يوم\n"

        if latest_leave:
            message += f"\nتاريخ آخر إجازة: من {latest_leave.start_date} إلى {latest_leave.end_date}\n"
        else:
            message += "\nلا يوجد إجازات سابقة\n"

        message += "\nمع تحيات قسم شؤون الموظفين"

        # Create WhatsApp URL
        whatsapp_url = f"https://wa.me/{phone}?text={message}"

        return JsonResponse({
            'success': True,
            'whatsapp_url': whatsapp_url,
            'phone': phone,
            'message': message
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على الموظف'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def leave_balance_create(request):
    if request.method == 'POST':
        print('POST data:', request.POST)

        # Create a copy of POST data to modify
        post_data = request.POST.copy()

        # Get employee_id from POST data
        employee_id = post_data.get('employee_id')
        ministry_number = post_data.get('ministry_number')

        # If we have ministry_number but no employee_id, try to get employee_id
        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                post_data['employee_id'] = str(employee.id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ministry number: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                return render(request, 'leaves/leave_balance_form.html', {'form': LeaveBalanceForm(post_data)})

        # If we have employee_id but no employee, set employee
        if employee_id and not post_data.get('employee'):
            try:
                employee = Employee.objects.get(id=employee_id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ID: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                return render(request, 'leaves/leave_balance_form.html', {'form': LeaveBalanceForm(post_data)})

        # Create form with modified POST data
        form = LeaveBalanceForm(post_data)
        print('Modified POST data:', post_data)

        if form.is_valid():
            print('Form is valid. Cleaned data:', form.cleaned_data)

            try:
                # Get the employee
                employee_id = post_data.get('employee')
                if not employee_id:
                    employee_id = post_data.get('employee_id')

                if not employee_id:
                    messages.error(request, 'الرجاء اختيار موظف')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                try:
                    employee = Employee.objects.get(id=employee_id)
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                # Get other fields
                leave_type = form.cleaned_data.get('leave_type')
                year = form.cleaned_data.get('year')
                initial_balance = form.cleaned_data.get('initial_balance')
                used_balance = form.cleaned_data.get('used_balance', 0)

                if not leave_type:
                    messages.error(request, 'الرجاء اختيار نوع الإجازة')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                if not year:
                    messages.error(request, 'الرجاء إدخال السنة')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                if initial_balance is None:
                    messages.error(request, 'الرجاء إدخال الرصيد الأولي')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                # Check for duplicate
                existing_balance = LeaveBalance.objects.filter(
                    employee=employee,
                    leave_type=leave_type,
                    year=year
                ).first()

                if existing_balance:
                    messages.error(request, 'يوجد بالفعل رصيد إجازة لهذا الموظف ونوع الإجازة والسنة.')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                # Create a new balance
                balance = LeaveBalance(
                    employee=employee,
                    leave_type=leave_type,
                    year=year,
                    initial_balance=initial_balance,
                    used_balance=used_balance
                )

                # Save the balance
                balance.save()
                print('Balance saved successfully with ID:', balance.id)

                messages.success(request, 'تم إضافة رصيد الإجازة بنجاح.')
                return redirect('leaves:leave_balance_list')

            except Exception as e:
                print('Error saving balance:', str(e))
                messages.error(request, f'حدث خطأ أثناء حفظ رصيد الإجازة: {str(e)}')
                return render(request, 'leaves/leave_balance_form.html', {'form': form})
        else:
            print('Form is invalid. Errors:', form.errors)
            for field, errors in form.errors.items():
                print(f'Field {field} errors: {errors}')
    else:
        form = LeaveBalanceForm()

    return render(request, 'leaves/leave_balance_form.html', {'form': form})

@login_required
def leave_balance_update(request, pk):
    balance = get_object_or_404(LeaveBalance, pk=pk)

    if request.method == 'POST':
        print('POST data received:', request.POST)

        # Create a mutable copy of POST data
        post_data = request.POST.copy()

        # Get ministry number and employee_id from POST data
        ministry_number = post_data.get('ministry_number', '').strip()
        employee_id = post_data.get('employee_id')

        print(f'Ministry number: {ministry_number}, Employee ID: {employee_id}')

        # If we have ministry_number but no employee_id, try to get employee_id
        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                post_data['employee_id'] = str(employee.id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ministry number: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                form = LeaveBalanceForm(post_data, instance=balance)
                # Populate form with existing data
                form.fields['ministry_number'].initial = balance.employee.ministry_number
                form.fields['employee_name'].initial = balance.employee.full_name
                return render(request, 'leaves/leave_balance_form.html', {'form': form, 'balance': balance})

        # If we have employee_id but no employee, set employee
        if employee_id and not post_data.get('employee'):
            try:
                employee = Employee.objects.get(id=employee_id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ID: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                form = LeaveBalanceForm(post_data, instance=balance)
                # Populate form with existing data
                form.fields['ministry_number'].initial = balance.employee.ministry_number
                form.fields['employee_name'].initial = balance.employee.full_name
                return render(request, 'leaves/leave_balance_form.html', {'form': form, 'balance': balance})

        # Create form with modified POST data
        form = LeaveBalanceForm(post_data, instance=balance)
        print('Modified POST data:', post_data)

        if form.is_valid():
            print('Form is valid. Cleaned data:', form.cleaned_data)
            try:
                # Save the balance
                updated_balance = form.save()
                print(f'Balance updated successfully: {updated_balance.id}')
                messages.success(request, 'تم تحديث رصيد الإجازة بنجاح.')
                return redirect('leaves:leave_balance_list')
            except Exception as e:
                print('Error updating balance:', str(e))
                messages.error(request, f'حدث خطأ أثناء تحديث رصيد الإجازة: {str(e)}')
                return render(request, 'leaves/leave_balance_form.html', {'form': form, 'balance': balance})
        else:
            print('Form is invalid. Errors:', form.errors)
            for field, errors in form.errors.items():
                print(f'Field {field} errors: {errors}')
    else:
        # GET request - populate form with existing data
        form = LeaveBalanceForm(instance=balance)
        # Set the display fields
        form.fields['ministry_number'].initial = balance.employee.ministry_number
        form.fields['employee_name'].initial = balance.employee.full_name
        form.fields['employee_id'].initial = balance.employee.id

    return render(request, 'leaves/leave_balance_form.html', {'form': form, 'balance': balance})

@login_required
def leave_balance_delete(request, pk):
    balance = get_object_or_404(LeaveBalance, pk=pk)
    if request.method == 'POST':
        balance.delete()
        messages.success(request, 'تم حذف رصيد الإجازة بنجاح.')
        return redirect('leaves:leave_balance_list')
    return render(request, 'leaves/leave_balance_confirm_delete.html', {'balance': balance})

@login_required
def departure_list(request):
    """عرض قائمة المغادرات مع الإحصائيات والفلاتر"""
    from django.db.models import Q, Count
    from datetime import datetime

    # الحصول على المعاملات من الطلب
    departure_type = request.GET.get('departure_type', '')
    year = request.GET.get('year', '')
    status = request.GET.get('status', '')
    search = request.GET.get('search', '')

    # بناء الاستعلام الأساسي
    departures = Departure.objects.all()

    # تطبيق الفلاتر
    if departure_type:
        departures = departures.filter(departure_type=departure_type)

    if year:
        departures = departures.filter(date__year=year)

    if status:
        departures = departures.filter(status=status)

    if search:
        departures = departures.filter(
            Q(employee__full_name__icontains=search) |
            Q(employee__ministry_number__icontains=search) |
            Q(reason__icontains=search)
        )

    # ترتيب النتائج
    departures = departures.order_by('-date')

    # حساب الإحصائيات
    total_departures = Departure.objects.count()
    personal_departures = Departure.objects.filter(departure_type='personal').count()
    official_departures = Departure.objects.filter(departure_type='official').count()
    pending_departures = Departure.objects.filter(status='pending').count()
    approved_departures = Departure.objects.filter(status='approved').count()
    rejected_departures = Departure.objects.filter(status='rejected').count()

    # إحصائيات هذا الشهر
    current_month = datetime.now().month
    current_year = datetime.now().year
    this_month_departures = Departure.objects.filter(
        date__month=current_month,
        date__year=current_year
    ).count()

    # الحصول على السنوات المتاحة للفلتر
    available_years = Departure.objects.dates('date', 'year', order='DESC')

    context = {
        'departures': departures,
        'total_departures': total_departures,
        'personal_departures': personal_departures,
        'official_departures': official_departures,
        'pending_departures': pending_departures,
        'approved_departures': approved_departures,
        'rejected_departures': rejected_departures,
        'this_month_departures': this_month_departures,
        'available_years': available_years,
        'current_filters': {
            'departure_type': departure_type,
            'year': year,
            'status': status,
            'search': search,
        }
    }

    return render(request, 'leaves/departure_list.html', context)

@login_required
def departure_create(request):
    if request.method == 'POST':
        form = DepartureForm(request.POST)
        if form.is_valid():
            departure = form.save(commit=False)

            # Get employee from employee_id
            employee_id = request.POST.get('employee_id')
            if employee_id:
                try:
                    employee = Employee.objects.get(id=employee_id)
                    departure.employee = employee
                    departure.save()
                    messages.success(request, 'تم إضافة المغادرة بنجاح.')
                    return redirect('leaves:departure_list')
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على الموظف.')
                    return render(request, 'leaves/departure_form.html', {'form': form})
            else:
                messages.error(request, 'يجب اختيار موظف أولاً.')
                return render(request, 'leaves/departure_form.html', {'form': form})
        else:
            # إذا كان النموذج غير صالح، عرض الأخطاء
            messages.error(request, 'يرجى تصحيح الأخطاء في النموذج.')
    else:
        form = DepartureForm()
    return render(request, 'leaves/departure_form.html', {'form': form})

@login_required
def departure_detail(request, pk):
    """عرض تفاصيل المغادرة"""
    departure = get_object_or_404(Departure, pk=pk)
    return render(request, 'leaves/departure_detail.html', {'departure': departure})

@login_required
def departure_update(request, pk):
    """تعديل المغادرة"""
    departure = get_object_or_404(Departure, pk=pk)
    old_status = departure.status  # حفظ الحالة القديمة

    if request.method == 'POST':
        form = DepartureForm(request.POST, instance=departure)
        if form.is_valid():
            updated_departure = form.save(commit=False)

            # Get employee from employee_id
            employee_id = request.POST.get('employee_id')
            if employee_id:
                try:
                    employee = Employee.objects.get(id=employee_id)
                    updated_departure.employee = employee

                    # تحديث رصيد الإجازات إذا تغيرت الحالة إلى معتمدة
                    if old_status != 'approved' and updated_departure.status == 'approved':
                        updated_departure.save()
                        updated_departure.update_annual_leave_balance()
                    else:
                        updated_departure.save()

                    messages.success(request, 'تم تحديث المغادرة بنجاح.')
                    return redirect('leaves:departure_list')
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على الموظف.')
                    return render(request, 'leaves/departure_form.html', {'form': form, 'departure': departure})
            else:
                messages.error(request, 'يجب اختيار موظف أولاً.')
                return render(request, 'leaves/departure_form.html', {'form': form, 'departure': departure})
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء في النموذج.')
    else:
        form = DepartureForm(instance=departure)
        # Set initial values for employee search
        form.fields['ministry_number'].initial = departure.employee.ministry_number if departure.employee else ''
        form.fields['employee_id'].initial = departure.employee.id if departure.employee else ''

    return render(request, 'leaves/departure_form.html', {
        'form': form,
        'departure': departure,
        'employee_name': departure.employee.full_name if departure.employee else ''
    })

@login_required
def departure_delete(request, pk):
    """حذف المغادرة"""
    departure = get_object_or_404(Departure, pk=pk)

    if request.method == 'POST':
        departure.delete()
        messages.success(request, 'تم حذف المغادرة بنجاح.')
        return redirect('leaves:departure_list')

    return render(request, 'leaves/departure_confirm_delete.html', {'departure': departure})

@login_required
def leave_reports(request):
    """View for generating leave reports for directorate employees only"""
    from django.db.models import Sum
    from employment.models import Department, Employment

    # Get current year
    current_year = timezone.now().year

    # Get only directorate employees (same as directorate report)
    directorate_departments = Department.objects.filter(workplace='directorate')
    employments = Employment.objects.filter(
        department__in=directorate_departments,
        is_current=True
    ).select_related('employee')
    employees = [emp.employee for emp in employments]

    # Define leave types in specific order with casual leave included
    leave_types = LeaveType.objects.filter(
        name__in=['annual', 'sick', 'casual', 'hajj']
    ).exclude(name='unpaid')

    # Order leave types: annual, sick, casual, hajj
    leave_types_ordered = []
    leave_types_dict = {lt.name: lt for lt in leave_types}

    preferred_order = ['annual', 'sick', 'casual', 'hajj']
    for leave_name in preferred_order:
        if leave_name in leave_types_dict:
            leave_types_ordered.append(leave_types_dict[leave_name])

    leave_types = leave_types_ordered

    # Ensure casual leave type exists
    casual_leave_type, created = LeaveType.objects.get_or_create(
        name='casual',
        defaults={
            'description': 'إجازة عرضية للموظفين',
            'max_days_per_year': 7
        }
    )

    # Re-fetch leave types to include the newly created one
    if created:
        leave_types = LeaveType.objects.filter(
            name__in=['annual', 'sick', 'casual', 'hajj']
        ).exclude(name='unpaid')

        leave_types_ordered = []
        leave_types_dict = {lt.name: lt for lt in leave_types}

        for leave_name in preferred_order:
            if leave_name in leave_types_dict:
                leave_types_ordered.append(leave_types_dict[leave_name])

        leave_types = leave_types_ordered

    # Prepare data in a format that doesn't require custom template filters
    employee_balances = []
    for employee in employees:
        employee_data = {'employee': employee, 'type_balances': []}

        # حساب مجموع المغادرات الخاصة فقط للموظف خلال السنة (بالأيام)
        departures_total_days = 0
        departures = Departure.objects.filter(
            employee=employee,
            date__year=current_year,
            status='approved',
            departure_type='personal'  # المغادرات الخاصة فقط
        )

        for departure in departures:
            departures_total_days += departure.calculate_duration_days()

        for leave_type in leave_types:
            try:
                balance = LeaveBalance.objects.get(
                    employee=employee,
                    leave_type=leave_type,
                    year=current_year
                )

                # حساب الإجازات المستخدمة من جدول الإجازات مباشرة
                used_leaves = Leave.objects.filter(
                    employee=employee,
                    leave_type=leave_type,
                    start_date__year=current_year,
                    status='approved'
                ).aggregate(total_days=Sum('days_count'))

                used_days = used_leaves['total_days'] or 0

                # تحديد نوع الإجازة وطريقة العرض
                is_annual = leave_type.name == 'annual' or 'سنوية' in leave_type.get_name_display()
                is_decimal_type = is_annual  # فقط الإجازة السنوية تحتاج أرقام عشرية

                # للإجازات السنوية، نطرح المغادرات أيضاً
                if is_annual:
                    remaining = balance.initial_balance - used_days - departures_total_days
                    type_balance = {
                        'leave_type': leave_type,
                        'initial': balance.initial_balance,
                        'used': used_days,
                        'departures_used': departures_total_days,
                        'remaining': remaining if remaining >= 0 else 0,
                        'is_annual': True,
                        'is_decimal': is_decimal_type
                    }
                else:
                    remaining = balance.initial_balance - used_days
                    type_balance = {
                        'leave_type': leave_type,
                        'initial': balance.initial_balance,
                        'used': used_days,
                        'departures_used': 0,
                        'remaining': remaining if remaining >= 0 else 0,
                        'is_annual': False,
                        'is_decimal': is_decimal_type
                    }
            except LeaveBalance.DoesNotExist:
                is_annual = leave_type.name == 'annual' or 'سنوية' in leave_type.get_name_display()
                type_balance = {
                    'leave_type': leave_type,
                    'initial': 0,
                    'used': 0,
                    'departures_used': 0,
                    'remaining': 0,
                    'is_annual': is_annual,
                    'is_decimal': is_annual  # فقط الإجازة السنوية تحتاج أرقام عشرية
                }

            employee_data['type_balances'].append(type_balance)

        # إضافة مجموع المغادرات لبيانات الموظف
        employee_data['total_departures'] = departures_total_days
        employee_balances.append(employee_data)

    # حساب عدد الموظفين الذين لديهم مغادرات
    employees_with_departures = 0
    for item in employee_balances:
        if item['total_departures'] > 0:
            employees_with_departures += 1

    return render(request, 'leaves/leave_reports.html', {
        'employee_balances': employee_balances,
        'leave_types': leave_types,
        'current_year': current_year,
        'employees_with_departures': employees_with_departures
    })


@login_required
def leave_statistics(request):
    """View for displaying comprehensive leave statistics"""
    from django.db.models import Sum, Count, Avg
    from employment.models import Department, Employment
    import calendar

    # Get current year
    current_year = timezone.now().year

    # Get only directorate employees
    directorate_departments = Department.objects.filter(workplace='directorate')
    employments = Employment.objects.filter(
        department__in=directorate_departments,
        is_current=True
    ).select_related('employee')
    employees = [emp.employee for emp in employments]

    # Get leave types
    leave_types = LeaveType.objects.filter(
        name__in=['annual', 'sick', 'casual', 'hajj']
    ).exclude(name='unpaid')

    # Calculate overall statistics
    total_employees = len(employees)
    total_leaves = Leave.objects.filter(
        employee__in=employees,
        start_date__year=current_year,
        status='approved'
    ).count()

    total_leave_days = Leave.objects.filter(
        employee__in=employees,
        start_date__year=current_year,
        status='approved'
    ).aggregate(total=Sum('days_count'))['total'] or 0

    # Statistics by leave type
    leave_type_stats = []
    for leave_type in leave_types:
        stats = Leave.objects.filter(
            employee__in=employees,
            leave_type=leave_type,
            start_date__year=current_year,
            status='approved'
        ).aggregate(
            count=Count('id'),
            total_days=Sum('days_count'),
            avg_days=Avg('days_count')
        )

        leave_type_stats.append({
            'leave_type': leave_type,
            'count': stats['count'] or 0,
            'total_days': stats['total_days'] or 0,
            'avg_days': round(stats['avg_days'] or 0, 1)
        })

    # Monthly statistics
    monthly_stats = []
    for month in range(1, 13):
        month_leaves = Leave.objects.filter(
            employee__in=employees,
            start_date__year=current_year,
            start_date__month=month,
            status='approved'
        ).aggregate(
            count=Count('id'),
            total_days=Sum('days_count')
        )

        monthly_stats.append({
            'month': month,
            'month_name': calendar.month_name[month],
            'month_name_ar': [
                '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ][month],
            'count': month_leaves['count'] or 0,
            'total_days': month_leaves['total_days'] or 0
        })

    # Department statistics
    department_stats = []
    for department in directorate_departments:
        dept_employees = Employment.objects.filter(
            department=department,
            is_current=True
        ).values_list('employee_id', flat=True)

        dept_leaves = Leave.objects.filter(
            employee_id__in=dept_employees,
            start_date__year=current_year,
            status='approved'
        ).aggregate(
            count=Count('id'),
            total_days=Sum('days_count')
        )

        department_stats.append({
            'department': department,
            'employee_count': len(dept_employees),
            'leave_count': dept_leaves['count'] or 0,
            'total_days': dept_leaves['total_days'] or 0,
            'avg_per_employee': round((dept_leaves['total_days'] or 0) / len(dept_employees), 1) if dept_employees else 0
        })

    # Departure statistics
    total_departures = Departure.objects.filter(
        employee__in=employees,
        date__year=current_year,
        status='approved'
    ).count()

    personal_departures = Departure.objects.filter(
        employee__in=employees,
        date__year=current_year,
        status='approved',
        departure_type='personal'
    ).count()

    official_departures = Departure.objects.filter(
        employee__in=employees,
        date__year=current_year,
        status='approved',
        departure_type='official'
    ).count()

    # Top employees by leave days
    top_employees = []
    for employee in employees:
        employee_total = Leave.objects.filter(
            employee=employee,
            start_date__year=current_year,
            status='approved'
        ).aggregate(total=Sum('days_count'))['total'] or 0

        if employee_total > 0:
            top_employees.append({
                'employee': employee,
                'total_days': employee_total
            })

    top_employees = sorted(top_employees, key=lambda x: x['total_days'], reverse=True)[:10]

    context = {
        'current_year': current_year,
        'total_employees': total_employees,
        'total_leaves': total_leaves,
        'total_leave_days': total_leave_days,
        'avg_days_per_employee': round(total_leave_days / total_employees, 1) if total_employees > 0 else 0,
        'leave_type_stats': leave_type_stats,
        'monthly_stats': monthly_stats,
        'department_stats': department_stats,
        'total_departures': total_departures,
        'personal_departures': personal_departures,
        'official_departures': official_departures,
        'top_employees': top_employees,
    }

    return render(request, 'leaves/leave_statistics.html', context)


@login_required
def check_annual_balance(request):
    """AJAX view to check annual leave balance"""
    employee_id = request.GET.get('employee_id')
    leave_type_id = request.GET.get('leave_type_id')
    year = request.GET.get('year')
    days_requested = request.GET.get('days_requested')

    if not all([employee_id, leave_type_id, year, days_requested]):
        return JsonResponse({
            'success': False,
            'error': 'معاملات مفقودة'
        })

    try:
        employee = Employee.objects.get(id=employee_id)
        leave_type = LeaveType.objects.get(id=leave_type_id)
        year = int(year)
        days_requested = int(days_requested)

        # Check if this is an annual leave type
        if not ('سنوية' in leave_type.name or 'annual' in leave_type.name.lower()):
            return JsonResponse({'success': True})

        # Get employee's annual leave balance for this year
        try:
            leave_balance = LeaveBalance.objects.get(
                employee=employee,
                leave_type=leave_type,
                year=year
            )

            # Calculate current remaining balance
            current_remaining = leave_balance.remaining_balance

            # Check if requested days exceed remaining balance
            if days_requested > current_remaining:
                if current_remaining <= 0:
                    return JsonResponse({
                        'success': False,
                        'error': 'لا يمكن إضافة إجازة سنوية. رصيد الموظف من الإجازات السنوية صفر.'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'error': f'عدد أيام الإجازة المطلوبة ({days_requested} يوم) يتجاوز الرصيد المتبقي للموظف ({current_remaining} يوم).'
                    })

            return JsonResponse({
                'success': True,
                'remaining_balance': current_remaining
            })

        except LeaveBalance.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': f'لا يوجد رصيد إجازات سنوية مسجل للموظف للعام {year}. يرجى إضافة رصيد الإجازة أولاً.'
            })

    except (Employee.DoesNotExist, LeaveType.DoesNotExist, ValueError):
        return JsonResponse({
            'success': False,
            'error': 'بيانات غير صحيحة'
        })


@login_required
def balance_rollover(request):
    """View for rolling over annual leave balances to new year"""
    from employment.models import Employment, Department
    from django.utils import timezone

    current_year = timezone.now().year
    available_years = list(range(current_year, current_year + 3))  # Current year + next 2 years
    selected_year = request.GET.get('year', current_year + 1)

    try:
        selected_year = int(selected_year)
    except (ValueError, TypeError):
        selected_year = current_year + 1

    rollover_data = None

    if request.method == 'POST':
        # Get action type and year
        action = request.POST.get('action')
        year = request.POST.get('year')

        if year:
            try:
                year = int(year)

                if action == 'rollover':
                    # Execute rollover (update existing balances)
                    result = execute_balance_rollover(year)
                    if result['success']:
                        messages.success(request, f'تم تدوير الرصيد بنجاح للسنة {year}. تم تحديث {result["updated_count"]} موظف.')
                    else:
                        messages.error(request, f'حدث خطأ أثناء تدوير الرصيد: {result["error"]}')

                elif action == 'migrate':
                    # Migrate data (create new balance records)
                    result = migrate_balance_data(year)
                    if result['success']:
                        messages.success(request, f'تم ترحيل البيانات بنجاح للسنة {year}. تم إنشاء {result["created_count"]} سجل جديد في أرصدة الإجازات.')
                    else:
                        messages.error(request, f'حدث خطأ أثناء ترحيل البيانات: {result["error"]}')

            except ValueError:
                messages.error(request, 'السنة المحددة غير صحيحة')

        return redirect('leaves:balance_rollover')

    if request.GET.get('year'):
        # Calculate rollover data for display
        rollover_data = calculate_rollover_data(selected_year, request.GET)

        # Get filter options
        filter_data = get_rollover_filter_options()
    else:
        filter_data = get_rollover_filter_options()

    context = {
        'available_years': available_years,
        'selected_year': selected_year,
        'rollover_data': rollover_data,
        'search_query': request.GET.get('search', ''),
        'department_filter': request.GET.get('department', ''),
        'balance_range_filter': request.GET.get('balance_range', ''),
        'specialty_filter': request.GET.get('specialty', ''),
        **filter_data,
    }

    return render(request, 'leaves/balance_rollover.html', context)


def calculate_rollover_data(year, filters=None):
    """Calculate rollover data for directorate employees with filters"""
    from employment.models import Employment, Department

    if filters is None:
        filters = {}

    # Get directorate employees only
    directorate_departments = Department.objects.filter(workplace='directorate')
    employments = Employment.objects.filter(
        department__in=directorate_departments,
        is_current=True
    ).select_related('employee', 'department')

    # Apply search filter
    search_query = filters.get('search', '').strip()
    if search_query:
        employments = employments.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query) |
            Q(employee__specialization__icontains=search_query)
        )

    # Apply department filter
    department_filter = filters.get('department', '').strip()
    if department_filter:
        employments = employments.filter(department__name=department_filter)

    # Apply specialty filter
    specialty_filter = filters.get('specialty', '').strip()
    if specialty_filter:
        employments = employments.filter(employee__specialization=specialty_filter)

    rollover_data = []
    previous_year = year - 1

    # Get annual leave type
    try:
        annual_leave_type = LeaveType.objects.filter(
            Q(name__icontains='سنوية') | Q(name__icontains='annual')
        ).first()
        if not annual_leave_type:
            return rollover_data
    except LeaveType.DoesNotExist:
        return rollover_data

    for employment in employments:
        employee = employment.employee

        # Get previous year balance
        try:
            previous_balance = LeaveBalance.objects.get(
                employee=employee,
                leave_type=annual_leave_type,
                year=previous_year
            )
            previous_remaining = previous_balance.remaining_balance
        except LeaveBalance.DoesNotExist:
            previous_remaining = 0

        # Calculate new balance (previous + 30, max 60)
        added_days = 30
        new_balance = min(previous_remaining + added_days, 60)

        # Determine notes
        notes = ""
        if previous_remaining + added_days > 60:
            notes = f"تم تحديد الرصيد بـ 60 يوم (الحد الأقصى) بدلاً من {previous_remaining + added_days}"
        elif previous_remaining == 0:
            notes = "رصيد جديد كامل"
        else:
            notes = "تم إضافة الرصيد الجديد للرصيد السابق"

        data_item = {
            'employee': employee,
            'department': employment.department.name if employment.department else 'غير محدد',
            'previous_balance': previous_remaining,
            'added_days': added_days,
            'new_balance': new_balance,
            'notes': notes
        }

        rollover_data.append(data_item)

    # Apply balance range filter after calculation
    balance_range_filter = filters.get('balance_range', '').strip()
    if balance_range_filter:
        if balance_range_filter == '0':
            rollover_data = [item for item in rollover_data if item['previous_balance'] == 0]
        elif balance_range_filter == '1-30':
            rollover_data = [item for item in rollover_data if 1 <= item['previous_balance'] <= 30]
        elif balance_range_filter == '31-60':
            rollover_data = [item for item in rollover_data if 31 <= item['previous_balance'] <= 60]
        elif balance_range_filter == '60+':
            rollover_data = [item for item in rollover_data if item['previous_balance'] > 60]

    return rollover_data


def get_rollover_filter_options():
    """Get filter options for rollover page"""
    from employment.models import Employment, Department

    # Get directorate employees only
    directorate_departments = Department.objects.filter(workplace='directorate')
    employments = Employment.objects.filter(
        department__in=directorate_departments,
        is_current=True
    ).select_related('employee', 'department')

    # Get unique departments
    departments = list(set([
        emp.department.name for emp in employments
        if emp.department and emp.department.name
    ]))
    departments.sort()

    # Get unique specialties
    specialties = list(set([
        emp.employee.specialization for emp in employments
        if emp.employee.specialization
    ]))
    specialties.sort()

    return {
        'departments': departments,
        'specialties': specialties,
    }


def execute_balance_rollover(year):
    """Execute the actual balance rollover"""
    from employment.models import Employment, Department
    from django.db import transaction

    try:
        with transaction.atomic():
            # Get directorate employees only
            directorate_departments = Department.objects.filter(workplace='directorate')
            employments = Employment.objects.filter(
                department__in=directorate_departments,
                is_current=True
            ).select_related('employee')

            # Get annual leave type
            annual_leave_type = LeaveType.objects.filter(
                Q(name__icontains='سنوية') | Q(name__icontains='annual')
            ).first()
            if not annual_leave_type:
                return {'success': False, 'error': 'نوع الإجازة السنوية غير موجود'}

            updated_count = 0
            previous_year = year - 1

            for employment in employments:
                employee = employment.employee

                # Get previous year balance
                try:
                    previous_balance = LeaveBalance.objects.get(
                        employee=employee,
                        leave_type=annual_leave_type,
                        year=previous_year
                    )
                    previous_remaining = previous_balance.remaining_balance
                except LeaveBalance.DoesNotExist:
                    previous_remaining = 0

                # Calculate new balance (previous + 30, max 60)
                new_balance = min(previous_remaining + 30, 60)

                # Create or update balance for new year
                balance, created = LeaveBalance.objects.get_or_create(
                    employee=employee,
                    leave_type=annual_leave_type,
                    year=year,
                    defaults={
                        'initial_balance': new_balance,
                        'used_balance': 0
                    }
                )

                if not created:
                    # Update existing balance
                    balance.initial_balance = new_balance
                    balance.used_balance = 0
                    balance.save()

                updated_count += 1

            return {'success': True, 'updated_count': updated_count}

    except Exception as e:
        return {'success': False, 'error': str(e)}


def migrate_balance_data(year):
    """Migrate rollover data to create new balance records"""
    from employment.models import Employment, Department
    from django.db import transaction

    try:
        with transaction.atomic():
            # Get directorate employees only
            directorate_departments = Department.objects.filter(workplace='directorate')
            employments = Employment.objects.filter(
                department__in=directorate_departments,
                is_current=True
            ).select_related('employee')

            # Get annual leave type
            annual_leave_type = LeaveType.objects.filter(
                Q(name__icontains='سنوية') | Q(name__icontains='annual')
            ).first()
            if not annual_leave_type:
                return {'success': False, 'error': 'نوع الإجازة السنوية غير موجود'}

            created_count = 0
            previous_year = year - 1

            for employment in employments:
                employee = employment.employee

                # Get previous year balance
                try:
                    previous_balance = LeaveBalance.objects.get(
                        employee=employee,
                        leave_type=annual_leave_type,
                        year=previous_year
                    )
                    previous_remaining = previous_balance.remaining_balance
                except LeaveBalance.DoesNotExist:
                    previous_remaining = 0

                # Calculate new balance (previous + 30, max 60)
                new_balance = min(previous_remaining + 30, 60)

                # Create new balance record for new year (only if doesn't exist)
                balance, created = LeaveBalance.objects.get_or_create(
                    employee=employee,
                    leave_type=annual_leave_type,
                    year=year,
                    defaults={
                        'initial_balance': new_balance,
                        'used_balance': 0
                    }
                )

                if created:
                    created_count += 1
                else:
                    # Update existing balance if needed
                    if balance.initial_balance != new_balance:
                        balance.initial_balance = new_balance
                        balance.used_balance = 0
                        balance.save()
                        created_count += 1

            return {'success': True, 'created_count': created_count}

    except Exception as e:
        return {'success': False, 'error': str(e)}