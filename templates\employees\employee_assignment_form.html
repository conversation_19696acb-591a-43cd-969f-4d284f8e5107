{% extends 'base.html' %}

{% block title %}
{% if assignment %}تعديل التكليف{% else %}إضافة تكليف جديد{% endif %}
{% endblock %}

{% block content %}
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">
                {% if assignment %}تعديل التكليف{% else %}إضافة تكليف جديد{% endif %}
            </h6>
            {% if assignment %}
            <div class="text-muted">
                <small>
                    <i class="fas fa-user"></i> {{ assignment.employee.full_name }} 
                    | <i class="fas fa-id-card"></i> {{ assignment.employee.ministry_number }}
                </small>
            </div>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        <form method="post" id="assignment_form" action="{% if assignment %}{% url 'employees:employee_assignment_update' assignment.id %}{% else %}{% url 'employees:employee_assignment_create' %}{% endif %}" onsubmit="return validateForm()">
            {% csrf_token %}

            <div class="row">
                <div class="col-md-6">
                    {{ form.employee }}
                    <input type="hidden" name="employee_id" id="id_employee_id" value="{{ form.employee_id.value|default:'' }}">

                    <div class="mb-3">
                        <label for="{{ form.ministry_number.id_for_label }}" class="form-label">الرقم الوزاري</label>
                        <div class="input-group">
                            <input type="text" name="ministry_number" id="ministry_number_input" class="form-control" required>
                            <button class="btn btn-primary" type="button" id="search_employee_btn">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                        <div id="ministry_number_error" class="invalid-feedback d-none"></div>
                        {% if form.ministry_number.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.ministry_number.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.employee_name.id_for_label }}" class="form-label">اسم الموظف</label>
                        <input type="text" name="employee_name" id="employee_name_display" class="form-control" readonly>
                        {% if form.employee_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.employee_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.department.id_for_label }}" class="form-label">القسم</label>
                        {{ form.department }}
                        {% if form.department.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.department.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.assigned_role.id_for_label }}" class="form-label">الوظيفة المكلف بها</label>
                        {{ form.assigned_role }}
                        {% if form.assigned_role.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.assigned_role.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.assignment_date.id_for_label }}" class="form-label">تاريخ التكليف</label>
                        {{ form.assignment_date }}
                        {% if form.assignment_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.assignment_date.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.end_date.id_for_label }}" class="form-label">تاريخ انتهاء التكليف (اختياري)</label>
                        {{ form.end_date }}
                        {% if form.end_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.end_date.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.notes.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'employees:employee_assignments_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        <button type="submit" class="btn btn-success" id="saveButton">
                            <i class="fas fa-save"></i> 
                            {% if assignment %}تحديث التكليف{% else %}حفظ التكليف{% endif %}
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing assignment form...');

    // Employee search functionality
    const ministryNumberInput = document.getElementById('ministry_number_input');
    const employeeNameDisplay = document.getElementById('employee_name_display');
    const employeeIdInput = document.getElementById('id_employee_id');
    const searchButton = document.getElementById('search_employee_btn');

    // Function to search for employee
    function searchEmployee() {
        const ministryNumber = ministryNumberInput.value.trim();
        if (!ministryNumber) {
            alert('الرجاء إدخال الرقم الوزاري');
            return;
        }

        // Show loading indicator
        searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        searchButton.disabled = true;

        // Make AJAX request
        fetch(`/employment/get-employee-by-ministry-number/?ministry_number=${ministryNumber}`)
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    if (response.status === 403) {
                        // Redirect to login page if not authenticated
                        window.location.href = '/accounts/login/?next=' + encodeURIComponent(window.location.pathname);
                        throw new Error('يجب تسجيل الدخول للمتابعة');
                    }
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Employee search response:', data);
                if (data.success) {
                    // Update form fields
                    employeeNameDisplay.value = data.employee.full_name;
                    employeeIdInput.value = data.employee.id;
                    
                    // Clear any previous error messages
                    const errorDiv = document.getElementById('ministry_number_error');
                    if (errorDiv) {
                        errorDiv.classList.add('d-none');
                        errorDiv.textContent = '';
                    }
                    
                    console.log('Employee found and form updated');
                } else {
                    // Show error message
                    employeeNameDisplay.value = '';
                    employeeIdInput.value = '';
                    
                    const errorDiv = document.getElementById('ministry_number_error');
                    if (errorDiv) {
                        errorDiv.classList.remove('d-none');
                        errorDiv.textContent = data.error || 'لم يتم العثور على الموظف';
                    }
                    
                    alert(data.error || 'لم يتم العثور على الموظف');
                }
            })
            .catch(error => {
                console.error('Error searching for employee:', error);
                employeeNameDisplay.value = '';
                employeeIdInput.value = '';
                alert('حدث خطأ أثناء البحث عن الموظف: ' + error.message);
            })
            .finally(() => {
                // Reset button
                searchButton.innerHTML = '<i class="fas fa-search"></i> بحث';
                searchButton.disabled = false;
            });
    }

    // Event listeners
    if (searchButton) {
        searchButton.addEventListener('click', searchEmployee);
    }

    if (ministryNumberInput) {
        ministryNumberInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchEmployee();
            }
        });
    }

    // Form validation function
    function validateForm() {
        console.log('Validating form...');

        // Get form elements
        const ministryNumberInput = document.getElementById('ministry_number_input');
        const employeeIdInput = document.getElementById('id_employee_id');
        const employeeNameDisplay = document.getElementById('employee_name_display');
        const departmentInput = document.getElementById('id_department');
        const assignedRoleInput = document.getElementById('id_assigned_role');
        const assignmentDateInput = document.getElementById('id_assignment_date');
        const saveButton = document.getElementById('saveButton');

        // Check if employee is selected
        if (!employeeIdInput || !employeeIdInput.value) {
            alert('الرجاء اختيار موظف أولاً');
            return false;
        }

        // Check if department is entered
        if (!departmentInput || !departmentInput.value.trim()) {
            alert('الرجاء إدخال القسم');
            return false;
        }

        // Check if assigned role is entered
        if (!assignedRoleInput || !assignedRoleInput.value.trim()) {
            alert('الرجاء إدخال الوظيفة المكلف بها');
            return false;
        }

        // Check if assignment date is entered
        if (!assignmentDateInput || !assignmentDateInput.value) {
            alert('الرجاء إدخال تاريخ التكليف');
            return false;
        }

        // Make sure the employee ID is set in the form
        if (employeeIdInput && employeeIdInput.value) {
            const selectElement = document.getElementById('id_employee');
            if (selectElement) {
                // Check if option exists
                let optionExists = false;
                for (let i = 0; i < selectElement.options.length; i++) {
                    if (selectElement.options[i].value == employeeIdInput.value) {
                        selectElement.options[i].selected = true;
                        optionExists = true;
                        break;
                    }
                }

                // If option doesn't exist, create it
                if (!optionExists) {
                    const newOption = new Option(employeeNameDisplay.value, employeeIdInput.value, true, true);
                    selectElement.appendChild(newOption);
                }
            }
        }

        // Disable the save button to prevent double submission
        if (saveButton) {
            saveButton.disabled = true;
            saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        }

        console.log('Form validation passed, submitting...');
        return true;
    }

    // Load existing data when editing
    function loadExistingData() {
        {% if assignment %}
        // We are in edit mode, load the existing employee data
        const ministryNumber = '{{ assignment.employee.ministry_number|default:"" }}';
        const employeeName = '{{ assignment.employee.full_name|default:"" }}';
        const employeeId = '{{ assignment.employee.id|default:"" }}';

        console.log('Loading existing data:', {
            ministryNumber: ministryNumber,
            employeeName: employeeName,
            employeeId: employeeId
        });

        // Set the values in the form
        if (ministryNumber) {
            const ministryInput = document.getElementById('ministry_number_input');
            if (ministryInput) {
                ministryInput.value = ministryNumber;
                console.log('Set ministry number:', ministryNumber);
            }
        }
        if (employeeName) {
            const nameDisplay = document.getElementById('employee_name_display');
            if (nameDisplay) {
                nameDisplay.value = employeeName;
                console.log('Set employee name:', employeeName);
            }
        }
        if (employeeId) {
            const employeeIdInput = document.getElementById('id_employee_id');
            if (employeeIdInput) {
                employeeIdInput.value = employeeId;
                console.log('Set employee ID:', employeeId);
            }
        }
        {% endif %}
    }

    // Call loadExistingData when page loads with a delay to ensure DOM is ready
    setTimeout(loadExistingData, 100);
    
    // Also call it when DOM is fully loaded
    document.addEventListener('DOMContentLoaded', loadExistingData);

    // Make validateForm globally accessible
    window.validateForm = validateForm;
});
</script>
{% endblock %}
