#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريپت إعداد MySQL مبسط
Simple MySQL Setup Script
"""

import os
import sys
import subprocess
import time

def check_xampp_running():
    """فحص تشغيل XAMPP"""
    try:
        # فحص العمليات الجارية
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq mysqld.exe'], 
                              capture_output=True, text=True)
        
        if 'mysqld.exe' in result.stdout:
            print("✓ MySQL يعمل بالفعل")
            return True
        else:
            print("! MySQL غير نشط")
            return False
    except:
        return False

def start_xampp_mysql():
    """تشغيل MySQL من XAMPP"""
    xampp_paths = [
        "C:\\xampp\\xampp-control.exe",
        "C:\\Program Files\\XAMPP\\xampp-control.exe",
        "C:\\xampp\\mysql\\bin\\mysqld.exe"
    ]
    
    for path in xampp_paths:
        if os.path.exists(path):
            print(f"✓ تم العثور على XAMPP في: {path}")
            
            if "xampp-control.exe" in path:
                print("تشغيل XAMPP Control Panel...")
                try:
                    subprocess.Popen([path])
                    print("✓ تم تشغيل XAMPP Control Panel")
                    print("يرجى الضغط على 'Start' بجانب MySQL في النافذة المفتوحة")
                    return True
                except Exception as e:
                    print(f"خطأ في تشغيل XAMPP: {e}")
            
            elif "mysqld.exe" in path:
                print("تشغيل MySQL مباشرة...")
                try:
                    subprocess.Popen([path, "--console"])
                    time.sleep(3)
                    return True
                except Exception as e:
                    print(f"خطأ في تشغيل MySQL: {e}")
    
    return False

def create_database_simple():
    """إنشاء قاعدة البيانات بطريقة مبسطة"""
    try:
        import mysql.connector
        
        # محاولة الاتصال بدون كلمة مرور (XAMPP الافتراضي)
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',  # XAMPP افتراضي بدون كلمة مرور
            port=3306
        )
        
        if connection.is_connected():
            print("✓ تم الاتصال بـ MySQL بنجاح")
            
            cursor = connection.cursor()
            
            # إنشاء قاعدة البيانات
            cursor.execute("CREATE DATABASE IF NOT EXISTS hr_system_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✓ تم إنشاء قاعدة البيانات: hr_system_db")
            
            # إنشاء المستخدم
            cursor.execute("CREATE USER IF NOT EXISTS 'hr_user'@'localhost' IDENTIFIED BY 'hr_password_2024'")
            print("✓ تم إنشاء المستخدم: hr_user")
            
            # منح الصلاحيات
            cursor.execute("GRANT ALL PRIVILEGES ON hr_system_db.* TO 'hr_user'@'localhost'")
            cursor.execute("FLUSH PRIVILEGES")
            print("✓ تم منح الصلاحيات")
            
            connection.close()
            return True
            
    except mysql.connector.Error as e:
        if "Access denied" in str(e):
            print("! يحتاج MySQL كلمة مرور")
            password = input("أدخل كلمة مرور MySQL root (أو اضغط Enter إذا لم تكن هناك كلمة مرور): ")
            
            try:
                connection = mysql.connector.connect(
                    host='localhost',
                    user='root',
                    password=password,
                    port=3306
                )
                
                cursor = connection.cursor()
                cursor.execute("CREATE DATABASE IF NOT EXISTS hr_system_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                cursor.execute("CREATE USER IF NOT EXISTS 'hr_user'@'localhost' IDENTIFIED BY 'hr_password_2024'")
                cursor.execute("GRANT ALL PRIVILEGES ON hr_system_db.* TO 'hr_user'@'localhost'")
                cursor.execute("FLUSH PRIVILEGES")
                
                connection.close()
                print("✓ تم إعداد قاعدة البيانات بنجاح")
                return True
                
            except Exception as e2:
                print(f"✗ خطأ في الاتصال: {e2}")
                return False
        else:
            print(f"✗ خطأ في MySQL: {e}")
            return False
    except Exception as e:
        print(f"✗ خطأ عام: {e}")
        return False

def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        import mysql.connector
        
        connection = mysql.connector.connect(
            host='localhost',
            database='hr_system_db',
            user='hr_user',
            password='hr_password_2024',
            port=3306
        )
        
        if connection.is_connected():
            print("✓ اختبار الاتصال نجح!")
            connection.close()
            return True
    except Exception as e:
        print(f"✗ فشل اختبار الاتصال: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("إعداد MySQL مبسط")
    print("=" * 20)
    
    # فحص تشغيل MySQL
    if not check_xampp_running():
        print("\nمحاولة تشغيل MySQL...")
        if not start_xampp_mysql():
            print("\n✗ لم يتم العثور على XAMPP أو MySQL")
            print("يرجى تثبيت XAMPP أولاً:")
            print("1. شغل: python download_xampp.py")
            print("2. أو حمل من: https://www.apachefriends.org/")
            return False
        
        print("\nانتظار تشغيل MySQL...")
        time.sleep(5)
    
    # إنشاء قاعدة البيانات
    print("\nإنشاء قاعدة البيانات...")
    if not create_database_simple():
        return False
    
    # اختبار الاتصال
    print("\nاختبار الاتصال...")
    if not test_connection():
        return False
    
    print("\n" + "=" * 40)
    print("✓ تم إعداد MySQL بنجاح!")
    print("=" * 40)
    
    return True

if __name__ == "__main__":
    if main():
        print("\nيمكنك الآن تشغيل:")
        print("python switch_to_mysql.py")
    else:
        print("\nفشل الإعداد. جرب:")
        print("1. تثبيت XAMPP")
        print("2. تشغيل MySQL من XAMPP Control Panel")
        print("3. إعادة تشغيل هذا السكريپت")
