{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة موظف - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="text-primary">
        <i class="fas fa-user-tie me-2"></i>إدارة الكادر
    </h2>
    <div class="d-flex gap-2">
        <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEmploymentModal">
            <i class="fas fa-plus me-2"></i>إضافة موظف جديد
        </a>
        <a href="{% url 'employment:department_list' %}" class="btn btn-success">
            <i class="fas fa-building me-2"></i>الأقسام
        </a>
        <a href="{% url 'employees:employee_assignments_list' %}" class="btn btn-info">
            <i class="fas fa-tasks me-2"></i>الموظفين المكلفين
        </a>
        <a href="{% url 'employees:specialty_assignments_list' %}" class="btn btn-warning">
            <i class="fas fa-chalkboard-teacher me-2"></i>المعلمين المحملين على تخصص آخر
        </a>
        <a href="{% url 'employees:shared_employees_list' %}" class="btn btn-secondary">
            <i class="fas fa-share-alt me-2"></i>المعلمين المشركين بأكثر من مدرسة
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-users me-2"></i>قائمة الموظفين
        </h6>
        <div class="d-flex align-items-center gap-3">
            <!-- Per page selector -->
            <div class="d-flex align-items-center">
                <label for="perPageSelect" class="form-label mb-0 me-2 text-nowrap text-white fw-bold">عرض:</label>
                <select id="perPageSelect" class="form-select form-select-sm" style="width: auto; min-width: 80px; font-size: 14px; font-weight: 600;">
                    <option value="10" {% if per_page == '10' %}selected{% endif %}>10</option>
                    <option value="25" {% if per_page == '25' %}selected{% endif %}>25</option>
                    <option value="50" {% if per_page == '50' %}selected{% endif %}>50</option>
                    <option value="100" {% if per_page == '100' %}selected{% endif %}>100</option>
                    <option value="all" {% if per_page == 'all' or not per_page %}selected{% endif %}>الكل</option>
                </select>
            </div>

            <!-- Employee counter -->
            <span class="badge bg-info fs-6" id="employmentCounter">
                <i class="fas fa-users me-1"></i>
                {% if total_employments %}
                    {{ total_employments }}
                {% elif page_obj %}
                    {{ page_obj.paginator.count }}
                {% else %}
                    {{ employments|length }}
                {% endif %}
                موظف
            </span>

            <!-- Search form -->
            <form class="d-flex align-items-center gap-2" method="get" id="searchForm">
                <div class="input-group input-group-sm" style="width: 250px;">
                    <span class="input-group-text">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input class="form-control" type="search"
                           placeholder="بحث في الموظفين..."
                           name="search"
                           id="searchInput"
                           value="{{ search_query }}">
                </div>
                <button class="btn btn-outline-primary btn-sm" type="submit">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                {% if search_query %}
                <a href="{% url 'employment:employment_list' %}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-times me-1"></i>إلغاء
                </a>
                {% endif %}
            </form>
        </div>
    </div>
    <div class="card-body">
        {% if search_query %}
        <div class="alert alert-info mb-3">
            <i class="fas fa-info-circle me-2"></i>
            <strong>نتائج البحث عن:</strong> "{{ search_query }}" -
            <span class="badge bg-primary">{{ employments|length }} نتيجة</span>
        </div>
        {% endif %}

        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>
                            <i class="fas fa-id-card me-2 text-primary"></i>
                            الرقم الوزاري
                        </th>
                        <th>
                            <i class="fas fa-user me-2 text-success"></i>
                            الاسم الرباعي
                        </th>
                        <th>
                            <i class="fas fa-building me-2 text-info"></i>
                            القسم
                        </th>
                        <th>
                            <i class="fas fa-briefcase me-2 text-warning"></i>
                            المسمى الوظيفي
                        </th>
                        <th>
                            <i class="fas fa-calendar-plus me-2 text-secondary"></i>
                            تاريخ التعيين
                        </th>
                        <th>
                            <i class="fas fa-cogs me-2 text-dark"></i>
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for employment in employments %}
                    <tr>
                        <td>
                            <span class="badge bg-primary fs-6">
                                <i class="fas fa-id-card me-1"></i>
                                {{ employment.employee.ministry_number }}
                            </span>
                        </td>
                        <td>
                            <a href="{% url 'employees:employee_detail' employment.employee.pk %}"
                               class="text-decoration-none fw-bold text-success">
                                <i class="fas fa-user me-1"></i>
                                {{ employment.employee.full_name }}
                            </a>
                        </td>
                        <td>
                            <a href="{% url 'employment:department_detail' employment.department.pk %}"
                               class="text-decoration-none text-info">
                                <i class="fas fa-building me-1"></i>
                                {{ employment.department.name }}
                            </a>
                        </td>
                        <td>
                            <span class="badge bg-secondary">
                                <i class="fas fa-briefcase me-1"></i>
                                {{ employment.position.name }}
                            </span>
                        </td>
                        <td>
                            <span class="text-primary fw-bold">
                                <i class="fas fa-calendar me-1"></i>
                                {{ employment.start_date|date:"d/m/Y" }}
                            </span>
                            <br>
                            <small class="text-muted">
                                {{ employment.start_date|date:"l" }}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-info btn-sm"
                                        onclick="window.location.href='{% url 'employees:employee_detail' employment.employee.pk %}'"
                                        title="عرض تفاصيل الموظف">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-warning btn-sm edit-employment"
                                        data-id="{{ employment.pk }}"
                                        title="تعديل بيانات التوظيف">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger btn-sm delete-employment"
                                        data-id="{{ employment.pk }}"
                                        title="حذف بيانات التوظيف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center">لا يوجد موظفين</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="card-footer">
        {% if page_obj and page_obj.has_other_pages %}
        <nav aria-label="Employment pagination">
            <ul class="pagination justify-content-center mb-0">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link pagination-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link pagination-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link pagination-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link pagination-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link pagination-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        <div class="text-center mt-2">
            <small class="text-muted">
                {% if page_obj %}
                    عرض {{ page_obj.start_index }} - {{ page_obj.end_index }} من {{ page_obj.paginator.count }} موظف
                    {% if page_obj.has_other_pages %}
                    | صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                    {% endif %}
                {% else %}
                    عرض جميع الموظفين ({{ total_employments }} موظف)
                {% endif %}
            </small>
        </div>
    </div>
</div>

<!-- Add Employment Modal -->
<div class="modal fade" id="addEmploymentModal" tabindex="-1" aria-labelledby="addEmploymentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEmploymentModalLabel">إضافة موظف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'employment:employment_list' %}">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="employee" class="form-label">الرقم الوزاري والاسم الرباعي</label>
                            <select class="form-select" id="employee" name="employee" required>
                                <option value="">اختر الموظف</option>
                                {% for employee in employees %}
                                <option value="{{ employee.pk }}">{{ employee.ministry_number }} - {{ employee.full_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="department" class="form-label">القسم</label>
                            <select class="form-select" id="department" name="department" required>
                                <option value="">اختر القسم</option>
                                {% for department in departments %}
                                <option value="{{ department.pk }}">{{ department.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="position" class="form-label">المسمى الوظيفي</label>
                            <select class="form-select" id="position" name="position" required>
                                <option value="">اختر المسمى الوظيفي</option>
                                {% for position in positions %}
                                <option value="{{ position.pk }}">{{ position.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">تاريخ التعيين</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" required>
                        </div>
                    </div>
                    <!-- Hidden fields -->
                    <input type="hidden" id="end_date" name="end_date" value="">
                    <input type="hidden" id="is_current" name="is_current" value="true" checked>
                    <input type="hidden" id="status" name="status" value="1">
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة موظف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .table th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
    }

    .table td {
        vertical-align: middle;
        text-align: center;
    }

    .btn-group .btn {
        margin: 0 1px;
    }

    .badge.fs-6 {
        font-size: 0.9rem !important;
        padding: 0.5rem 0.75rem;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
    }

    /* تصميم شريط العنوان مثل صفحة بيانات الموظفين */
    .card-header {
        background-color: #f8f9fa !important;
        border-bottom: 1px solid #dee2e6;
        padding: 1rem 1.25rem;
    }

    .card-header .text-primary {
        color: #007bff !important;
    }

    .card-header h6 {
        color: #007bff !important;
        font-weight: 600;
        margin-bottom: 0;
    }

    .card-header .badge {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }

    .card-header .btn-outline-primary {
        border-color: #007bff;
        color: #007bff;
    }

    .card-header .btn-outline-primary:hover {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .card-header .input-group-text {
        background-color: #f8f9fa;
        border-color: #ced4da;
    }

    .card-header .form-control {
        border-color: #ced4da;
    }

    .card-header .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .card-header .badge.fs-6 {
        font-size: 0.875rem !important;
        padding: 0.5rem 0.75rem;
        font-weight: 500;
    }

    /* تحسين مظهر فلتر عرض الأرقام */
    #perPageSelect {
        background-color: #fff;
        border: 2px solid #007bff;
        border-radius: 6px;
        font-weight: 600;
        color: #007bff;
        padding: 6px 10px;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.15);
        transition: all 0.3s ease;
    }

    #perPageSelect:focus {
        border-color: #0056b3;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        outline: none;
    }

    #perPageSelect:hover {
        background-color: #f8f9fa;
        border-color: #0056b3;
    }

    /* تحسين مظهر label عرض */
    .card-header label[for="perPageSelect"] {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white !important;
        padding: 6px 10px;
        border-radius: 6px;
        font-size: 13px;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
        border: none;
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Add event listeners for edit and delete buttons
    document.addEventListener('DOMContentLoaded', function() {
        const editButtons = document.querySelectorAll('.edit-employment');
        const deleteButtons = document.querySelectorAll('.delete-employment');

        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                window.location.href = `{% url 'employment:employment_list' %}${id}/edit/`;
            });
        });

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف بيانات التوظيف؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                    window.location.href = `{% url 'employment:employment_list' %}${id}/delete/`;
                }
            });
        });

        // Handle per page selector change with AJAX
        $('#perPageSelect').on('change', function() {
            const perPage = $(this).val();
            const currentUrl = new URL(window.location);

            if (perPage === 'all') {
                currentUrl.searchParams.set('per_page', 'all');
                currentUrl.searchParams.delete('page');
            } else {
                currentUrl.searchParams.set('per_page', perPage);
                currentUrl.searchParams.set('page', '1'); // Reset to first page
            }

            // Use AJAX to update table instead of reloading page
            loadEmploymentsWithAjax(currentUrl.toString());
        });

        // Function to load employments with AJAX
        function loadEmploymentsWithAjax(url) {
            console.log('Loading employments with AJAX:', url);

            const $tableBody = $('table tbody');
            const $cardFooter = $('.card-footer');
            const $employmentCounter = $('#employmentCounter');
            const $perPageSelect = $('#perPageSelect');

            // Show loading indicator in table
            $tableBody.html('<tr><td colspan="6" class="text-center py-5"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-3" role="status"><span class="visually-hidden">جاري التحميل...</span></div><h5 class="text-primary">جاري تحديث البيانات...</h5><small class="text-muted">يرجى الانتظار</small></div></td></tr>');

            $.ajax({
                url: url,
                type: 'GET',
                success: function(data) {
                    try {
                        console.log('AJAX response received for per page change');

                        // Extract content from response
                        const $newContent = $(data);
                        const $newTableBody = $newContent.find('table tbody');
                        const $newCardFooter = $newContent.find('.card-footer');
                        const $newEmploymentCounter = $newContent.find('#employmentCounter');
                        const $newPerPageSelect = $newContent.find('#perPageSelect');

                        console.log('New table body found:', $newTableBody.length > 0);
                        console.log('New card footer found:', $newCardFooter.length > 0);
                        console.log('New employment counter found:', $newEmploymentCounter.length > 0);

                        // Update table body content
                        if ($newTableBody.length > 0) {
                            $tableBody.html($newTableBody.html());
                            console.log('Table body updated successfully');
                        } else {
                            console.warn('No table body found in response');
                            $tableBody.html('<tr><td colspan="6" class="text-center text-muted">لا توجد موظفين</td></tr>');
                        }

                        // Update pagination footer
                        if ($newCardFooter.length > 0) {
                            $cardFooter.html($newCardFooter.html());
                            console.log('Card footer updated successfully');
                        } else {
                            $cardFooter.empty();
                            console.log('Card footer cleared');
                        }

                        // Update employment counter
                        if ($newEmploymentCounter.length > 0) {
                            $employmentCounter.html($newEmploymentCounter.html());
                            console.log('Employment counter updated successfully');
                        }

                        // Update per page selector to maintain selection
                        if ($newPerPageSelect.length > 0) {
                            const selectedValue = $newPerPageSelect.val();
                            $perPageSelect.val(selectedValue);
                            console.log('Per page selector updated to:', selectedValue);
                        }

                        // Update browser URL without reloading
                        window.history.pushState({}, '', url);

                        console.log('AJAX per page change completed successfully');
                    } catch (error) {
                        console.error('Error processing AJAX response:', error);
                        $tableBody.html('<tr><td colspan="6" class="text-center text-danger">حدث خطأ أثناء تحميل البيانات</td></tr>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error);
                    console.error('Response text:', xhr.responseText);
                    $tableBody.html('<tr><td colspan="6" class="text-center text-danger">حدث خطأ أثناء تحميل البيانات: ' + error + '</td></tr>');
                }
            });
        }

        // AJAX Pagination for Employment Data
        $(document).on('click', '.pagination-link', function(e) {
            e.preventDefault();

            const url = $(this).attr('href');
            console.log('AJAX Pagination URL:', url);

            // Use the shared AJAX function
            loadEmploymentsWithAjax(url);

            // Scroll to top of table after a short delay
            setTimeout(function() {
                $('html, body').animate({
                    scrollTop: $('.card').offset().top - 100
                }, 500);
            }, 100);
        });
    });
</script>
{% endblock %}
