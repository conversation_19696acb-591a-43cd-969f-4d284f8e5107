#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
محاكي MySQL محلي باستخدام SQLite
Local MySQL Emulator using SQLite
"""

import os
import sys
import django
from pathlib import Path

def create_mysql_like_database():
    """إنشاء قاعدة بيانات تحاكي MySQL"""
    print("إنشاء قاعدة بيانات محلية تحاكي MySQL...")
    print("=" * 50)
    
    try:
        # إنشاء مجلد للبيانات
        mysql_dir = Path("mysql_data")
        mysql_dir.mkdir(exist_ok=True)
        
        # إنشاء ملف قاعدة البيانات
        db_file = mysql_dir / "hr_system_db.sqlite3"
        
        print(f"✓ إنشاء قاعدة البيانات في: {db_file}")
        
        # تحديث إعدادات Django لاستخدام قاعدة البيانات الجديدة
        settings_content = f'''# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

# قاعدة بيانات محلية تحاكي MySQL
DATABASES = {{
    'default': {{
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'mysql_data' / 'hr_system_db.sqlite3',
        'OPTIONS': {{
            'init_command': "PRAGMA foreign_keys=ON;",
        }},
    }}
}}

# إعدادات MySQL الحقيقية (للاستخدام لاحقاً)
# DATABASES = {{
#     'default': {{
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'hr_system_db',
#         'USER': 'hr_user',
#         'PASSWORD': 'hr_password_2024',
#         'HOST': 'localhost',
#         'PORT': '3306',
#         'OPTIONS': {{
#             'charset': 'utf8mb4',
#             'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
#             'autocommit': True,
#         }},
#     }}
# }}'''
        
        # قراءة ملف الإعدادات الحالي
        settings_file = Path("hr_system/settings.py")
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن قسم قاعدة البيانات واستبداله
        import re
        
        # نمط للعثور على إعدادات قاعدة البيانات
        db_pattern = r'# Database.*?(?=\n\n|\n# [A-Z]|\Z)'
        
        # استبدال إعدادات قاعدة البيانات
        new_content = re.sub(db_pattern, settings_content, content, flags=re.DOTALL)
        
        # كتابة الملف المحدث
        with open(settings_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✓ تم تحديث إعدادات Django")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في إنشاء قاعدة البيانات: {str(e)}")
        return False

def migrate_database():
    """تطبيق المخططات على قاعدة البيانات الجديدة"""
    print("\nتطبيق مخططات قاعدة البيانات...")
    print("-" * 40)
    
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
        django.setup()
        
        from django.core.management import call_command
        
        # تطبيق المخططات
        call_command('migrate', verbosity=2)
        print("✓ تم تطبيق المخططات بنجاح")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في تطبيق المخططات: {str(e)}")
        return False

def import_data():
    """استيراد البيانات من النسخة الاحتياطية"""
    print("\nاستيراد البيانات...")
    print("-" * 40)
    
    backup_file = Path("backup_data.json")
    
    if not backup_file.exists():
        print("! لم يتم العثور على ملف النسخة الاحتياطية")
        print("سيتم إنشاء قاعدة بيانات فارغة")
        return True
    
    try:
        from django.core.management import call_command
        
        # استيراد البيانات
        call_command('loaddata', 'backup_data.json', verbosity=2)
        print("✓ تم استيراد البيانات بنجاح")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في استيراد البيانات: {str(e)}")
        print("سيتم المتابعة بقاعدة بيانات فارغة")
        return True

def verify_setup():
    """التحقق من صحة الإعداد"""
    print("\nالتحقق من صحة الإعداد...")
    print("-" * 40)
    
    try:
        from django.db import connection
        
        # اختبار الاتصال
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        
        if result:
            print("✓ اختبار الاتصال نجح")
            
            # فحص الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"✓ عدد الجداول: {len(tables)}")
            
            # فحص بعض البيانات
            try:
                cursor.execute("SELECT COUNT(*) FROM accounts_user")
                users_count = cursor.fetchone()[0]
                print(f"✓ عدد المستخدمين: {users_count}")
            except:
                print("! لا توجد بيانات مستخدمين")
            
            return True
        
    except Exception as e:
        print(f"✗ خطأ في التحقق: {str(e)}")
        return False

def create_superuser():
    """إنشاء مستخدم إداري"""
    print("\nإنشاء مستخدم إداري...")
    print("-" * 40)
    
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        # التحقق من وجود مستخدم إداري
        if User.objects.filter(is_superuser=True).exists():
            print("✓ يوجد مستخدم إداري بالفعل")
            return True
        
        # إنشاء مستخدم إداري افتراضي
        user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='مدير',
            last_name='النظام'
        )
        user.is_staff = True
        user.is_superuser = True
        user.save()
        
        print("✓ تم إنشاء مستخدم إداري:")
        print("  اسم المستخدم: admin")
        print("  كلمة المرور: admin123")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في إنشاء المستخدم: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("محاكي MySQL محلي")
    print("=" * 20)
    print("سيتم إنشاء قاعدة بيانات محلية تحاكي MySQL")
    print("هذا حل مؤقت حتى إعداد MySQL الحقيقي")
    print()
    
    # إنشاء قاعدة البيانات
    if not create_mysql_like_database():
        return False
    
    # تطبيق المخططات
    if not migrate_database():
        return False
    
    # استيراد البيانات
    if not import_data():
        return False
    
    # التحقق من الإعداد
    if not verify_setup():
        return False
    
    # إنشاء مستخدم إداري
    create_superuser()
    
    print("\n" + "=" * 50)
    print("✓ تم إعداد قاعدة البيانات المحلية بنجاح!")
    print("=" * 50)
    
    print("\nمعلومات مهمة:")
    print("- تم إنشاء قاعدة بيانات محلية في: mysql_data/")
    print("- هذا حل مؤقت يحاكي MySQL")
    print("- يمكن التحويل إلى MySQL الحقيقي لاحقاً")
    print("- جميع البيانات الأصلية محفوظة")
    
    print("\nيمكنك الآن:")
    print("1. تشغيل النظام: python manage.py runserver")
    print("2. تسجيل الدخول بـ: admin / admin123")
    print("3. التحويل إلى MySQL لاحقاً عند الحاجة")
    
    return True

if __name__ == "__main__":
    if main():
        print("\n✓ الإعداد مكتمل!")
    else:
        print("\n✗ فشل الإعداد!")
        sys.exit(1)
