#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت التحقق من صحة عملية التحويل
Migration Verification Script
"""

import os
import sys
import django
from django.db import connection
from django.apps import apps

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

def verify_database_structure():
    """التحقق من بنية قاعدة البيانات"""
    print("التحقق من بنية قاعدة البيانات...")
    print("-" * 50)
    
    try:
        with connection.cursor() as cursor:
            # فحص الجداول
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            
            print(f"✓ عدد الجداول: {len(tables)}")
            
            # فحص بعض الجداول المهمة
            important_tables = [
                'django_migrations',
                'accounts_user',
                'employees_employee',
                'employment_department',
                'leaves_leave',
                'performance_evaluation',
                'reports_report'
            ]
            
            missing_tables = []
            for table in important_tables:
                if table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"✓ {table}: {count} سجل")
                else:
                    missing_tables.append(table)
                    print(f"✗ {table}: غير موجود")
            
            if missing_tables:
                print(f"\nجداول مفقودة: {missing_tables}")
                return False
            
            return True
            
    except Exception as e:
        print(f"✗ خطأ في فحص بنية قاعدة البيانات: {str(e)}")
        return False

def verify_data_integrity():
    """التحقق من سلامة البيانات"""
    print("\nالتحقق من سلامة البيانات...")
    print("-" * 50)
    
    try:
        # استيراد النماذج
        from accounts.models import User
        from employees.models import Employee
        from employment.models import Department
        from leaves.models import Leave
        
        # فحص البيانات الأساسية
        users_count = User.objects.count()
        employees_count = Employee.objects.count()
        departments_count = Department.objects.count()
        leaves_count = Leave.objects.count()
        
        print(f"✓ المستخدمون: {users_count}")
        print(f"✓ الموظفون: {employees_count}")
        print(f"✓ الأقسام: {departments_count}")
        print(f"✓ الإجازات: {leaves_count}")
        
        # فحص العلاقات
        if employees_count > 0:
            employees_with_departments = Employee.objects.filter(department__isnull=False).count()
            print(f"✓ الموظفون المرتبطون بأقسام: {employees_with_departments}/{employees_count}")
        
        # فحص المستخدمين الإداريين
        admin_users = User.objects.filter(is_staff=True).count()
        print(f"✓ المستخدمون الإداريون: {admin_users}")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في فحص سلامة البيانات: {str(e)}")
        return False

def test_crud_operations():
    """اختبار عمليات CRUD الأساسية"""
    print("\nاختبار عمليات قاعدة البيانات...")
    print("-" * 50)
    
    try:
        from employment.models import Department
        
        # اختبار الإنشاء (Create)
        test_dept = Department.objects.create(
            name="قسم اختبار",
            code="TEST001",
            description="قسم للاختبار"
        )
        print("✓ اختبار الإنشاء (Create) - نجح")
        
        # اختبار القراءة (Read)
        retrieved_dept = Department.objects.get(id=test_dept.id)
        assert retrieved_dept.name == "قسم اختبار"
        print("✓ اختبار القراءة (Read) - نجح")
        
        # اختبار التحديث (Update)
        retrieved_dept.name = "قسم اختبار محدث"
        retrieved_dept.save()
        updated_dept = Department.objects.get(id=test_dept.id)
        assert updated_dept.name == "قسم اختبار محدث"
        print("✓ اختبار التحديث (Update) - نجح")
        
        # اختبار الحذف (Delete)
        test_dept.delete()
        try:
            Department.objects.get(id=test_dept.id)
            print("✗ اختبار الحذف (Delete) - فشل")
            return False
        except Department.DoesNotExist:
            print("✓ اختبار الحذف (Delete) - نجح")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار عمليات CRUD: {str(e)}")
        return False

def verify_mysql_specific_features():
    """التحقق من ميزات MySQL المحددة"""
    print("\nالتحقق من ميزات MySQL...")
    print("-" * 50)
    
    try:
        with connection.cursor() as cursor:
            # فحص ترميز قاعدة البيانات
            cursor.execute("SELECT @@character_set_database")
            charset = cursor.fetchone()[0]
            print(f"✓ ترميز قاعدة البيانات: {charset}")
            
            # فحص ترتيب قاعدة البيانات
            cursor.execute("SELECT @@collation_database")
            collation = cursor.fetchone()[0]
            print(f"✓ ترتيب قاعدة البيانات: {collation}")
            
            # فحص إصدار MySQL
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"✓ إصدار MySQL: {version}")
            
            # فحص محرك التخزين
            cursor.execute("SHOW TABLE STATUS LIKE 'accounts_user'")
            result = cursor.fetchone()
            if result:
                engine = result[1]  # Engine column
                print(f"✓ محرك التخزين: {engine}")
            
            return True
            
    except Exception as e:
        print(f"✗ خطأ في فحص ميزات MySQL: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للتحقق"""
    print("بدء التحقق من صحة عملية التحويل إلى MySQL")
    print("=" * 60)
    
    all_tests_passed = True
    
    # اختبار بنية قاعدة البيانات
    if not verify_database_structure():
        all_tests_passed = False
    
    # اختبار سلامة البيانات
    if not verify_data_integrity():
        all_tests_passed = False
    
    # اختبار عمليات CRUD
    if not test_crud_operations():
        all_tests_passed = False
    
    # اختبار ميزات MySQL
    if not verify_mysql_specific_features():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("✓ جميع الاختبارات نجحت! عملية التحويل مكتملة بنجاح.")
        print("يمكنك الآن استخدام النظام مع قاعدة بيانات MySQL.")
    else:
        print("✗ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False
    
    print("=" * 60)
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
