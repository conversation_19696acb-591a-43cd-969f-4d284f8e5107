{% extends 'base.html' %}

{% block title %}
{% if employee_position %}تعديل المسمى الوظيفي{% else %}إضافة مسمى وظيفي جديد{% endif %}
{% endblock %}

{% block content %}
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">
                {% if employee_position %}تعديل المسمى الوظيفي{% else %}إضافة مسمى وظيفي جديد{% endif %}
            </h6>
            {% if employee_position %}
            <div class="text-muted">
                <small>
                    <i class="fas fa-user"></i> {{ employee_position.employee.full_name }}
                    | <i class="fas fa-id-card"></i> {{ employee_position.employee.ministry_number }}
                </small>
            </div>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        <form method="post" id="employee_position_form" action="{% if employee_position %}{% url 'employment:employee_position_update' employee_position.id %}{% else %}{% url 'employment:employee_position_create' %}{% endif %}" onsubmit="return validateForm()">
            {% csrf_token %}

            <div class="row">
                <div class="col-md-6">
                    {{ form.employee }}
                    <input type="hidden" name="employee_id" id="id_employee_id" value="{{ form.employee_id.value|default:'' }}">

                    <div class="mb-3">
                        <label for="{{ form.ministry_number.id_for_label }}" class="form-label">الرقم الوزاري</label>
                        <div class="input-group">
                            <input type="text" name="ministry_number" id="ministry_number_input" class="form-control" required>
                            <button class="btn btn-primary" type="button" id="search_employee_btn">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                        <div id="ministry_number_error" class="invalid-feedback d-none"></div>
                        {% if form.ministry_number.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.ministry_number.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.employee_name.id_for_label }}" class="form-label">اسم الموظف</label>
                        <input type="text" name="employee_name" id="employee_name_display" class="form-control" readonly>
                        {% if form.employee_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.employee_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.position.id_for_label }}" class="form-label">المسمى الوظيفي</label>
                        <select name="position" id="id_position" class="form-control select2" required>
                            <option value="">-- اختر المسمى الوظيفي --</option>
                            {% for position in form.position.field.queryset %}
                            <option value="{{ position.id }}" {% if form.position.value == position.id %}selected{% endif %}>{{ position.name }}</option>
                            {% endfor %}
                        </select>
                        {% if form.position.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.position.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.date_obtained.id_for_label }}" class="form-label">تاريخ الحصول عليه</label>
                        <input type="date" name="date_obtained" id="id_date_obtained" class="form-control" value="{{ form.date_obtained.value|date:'Y-m-d'|default:'' }}" required>
                        {% if form.date_obtained.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.date_obtained.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3" id="school_level_group" style="display: none; background-color: #f8f9fa; padding: 15px; border-radius: 8px; border: 2px solid #007bff; transition: all 0.3s ease; opacity: 0;">
                        <label for="id_school_level" class="form-label" style="font-weight: bold; color: #007bff;">
                            <i class="fas fa-graduation-cap me-2"></i> المرحلة التي يدرسها
                            <span class="text-danger">*</span>
                        </label>
                        <select name="school_level" id="id_school_level" class="form-control" style="border: 2px solid #007bff;">
                            <option value="">-- اختر المرحلة --</option>
                            <option value="primary" {% if form.school_level.value == 'primary' %}selected{% endif %}>اساسي</option>
                            <option value="secondary" {% if form.school_level.value == 'secondary' %}selected{% endif %}>ثانوي</option>
                            <option value="both" {% if form.school_level.value == 'both' %}selected{% endif %}>اساسي + ثانوي</option>
                        </select>
                        <small class="form-text text-primary" style="font-weight: bold;">
                            <i class="fas fa-info-circle me-1"></i> يظهر هذا الحقل فقط عند اختيار وظيفة معلم
                        </small>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                <textarea name="notes" id="id_notes" class="form-control" rows="3">{{ form.notes.value|default:'' }}</textarea>
                {% if form.notes.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.notes.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary" id="saveButton">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'employment:employee_position_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        // Show/hide school level field based on position selection
        function toggleSchoolLevelField() {
            console.log('=== toggleSchoolLevelField called ===');

            const positionSelect = document.getElementById('id_position');
            const schoolLevelGroup = document.getElementById('school_level_group');

            console.log('Position select:', positionSelect);
            console.log('School level group:', schoolLevelGroup);

            if (!positionSelect || !schoolLevelGroup) {
                console.log('Required elements not found!');
                return;
            }

            const selectedIndex = positionSelect.selectedIndex;
            console.log('Selected index:', selectedIndex);

            if (selectedIndex <= 0) {
                // No position selected, hide field
                console.log('No position selected, hiding field');
                schoolLevelGroup.style.display = 'none';
                schoolLevelGroup.style.opacity = '0';
                return;
            }

            const selectedOption = positionSelect.options[selectedIndex];
            const positionName = selectedOption ? selectedOption.text.trim() : '';
            console.log('Selected position name:', positionName);

            // Check if position is "معلم" (exact match or contains "معلم")
            const isTeacher = positionName === 'معلم' ||
                             positionName.includes('معلم') ||
                             positionName.startsWith('معلم') ||
                             positionName.toLowerCase().includes('معلم') ||
                             positionName.toLowerCase() === 'معلم';

            console.log('Is teacher position?', isTeacher);

            if (isTeacher) {
                console.log('SHOWING school level field');
                // Show school level field with smooth animation
                schoolLevelGroup.style.display = 'block';

                // Use setTimeout to ensure display is set before opacity change
                setTimeout(() => {
                    schoolLevelGroup.style.opacity = '1';
                    schoolLevelGroup.style.transform = 'translateY(0)';
                    schoolLevelGroup.style.backgroundColor = '#e8f4fd';
                }, 10);

                // Add required attribute to school level select
                const schoolLevelSelect = document.getElementById('id_school_level');
                if (schoolLevelSelect) {
                    schoolLevelSelect.setAttribute('required', 'required');
                    console.log('Added required attribute to school level select');
                }
            } else {
                console.log('HIDING school level field');
                // Hide school level field with animation
                schoolLevelGroup.style.opacity = '0';
                schoolLevelGroup.style.transform = 'translateY(-10px)';

                // Hide after animation completes
                setTimeout(() => {
                    schoolLevelGroup.style.display = 'none';
                }, 300);

                // Clear the selection and remove required attribute when hiding
                const schoolLevelSelect = document.getElementById('id_school_level');
                if (schoolLevelSelect) {
                    schoolLevelSelect.value = '';
                    schoolLevelSelect.removeAttribute('required');
                    console.log('Cleared school level selection and removed required attribute');
                }
            }

            console.log('=== toggleSchoolLevelField completed ===');
        }



        // Initialize Select2 for position dropdown if available
        if (typeof $.fn.select2 !== 'undefined') {
            $('#id_position').select2({
                theme: 'bootstrap-5',
                placeholder: '-- اختر المسمى الوظيفي --',
                allowClear: true,
                width: '100%'
            });
        }

        // Initialize toggle function after a short delay to ensure DOM is ready
        setTimeout(() => {
            toggleSchoolLevelField();
        }, 200);

        // Additional initialization after longer delay for Select2
        setTimeout(() => {
            toggleSchoolLevelField();
        }, 1000);

        // Event listeners for position selection
        const positionSelect = document.getElementById('id_position');
        if (positionSelect) {
            console.log('Position select found, adding event listeners');

            // Listen for change events
            positionSelect.addEventListener('change', function() {
                console.log('Position changed to:', this.options[this.selectedIndex].text);
                toggleSchoolLevelField();
            });

            // Listen for input events (for select2 compatibility)
            positionSelect.addEventListener('input', function() {
                console.log('Position input changed');
                toggleSchoolLevelField();
            });

            // For Select2 compatibility
            if (typeof $.fn.select2 !== 'undefined') {
                $(positionSelect).on('select2:select', function() {
                    console.log('Select2 selection changed');
                    setTimeout(toggleSchoolLevelField, 50);
                });

                $(positionSelect).on('select2:clear', function() {
                    console.log('Select2 selection cleared');
                    setTimeout(toggleSchoolLevelField, 50);
                });
            }
        } else {
            console.log('Position select not found!');
        }



        // Employee search functionality
        const ministryNumberInput = document.getElementById('ministry_number_input');
        const employeeNameDisplay = document.getElementById('employee_name_display');
        const employeeIdInput = document.getElementById('id_employee_id');
        const searchButton = document.getElementById('search_employee_btn');

        // Function to search for employee
        function searchEmployee() {
            const ministryNumber = ministryNumberInput.value.trim();
            if (!ministryNumber) {
                alert('الرجاء إدخال الرقم الوزاري');
                return;
            }

            // Show loading indicator
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            searchButton.disabled = true;

            // Make AJAX request
            fetch(`/employment/get-employee-by-ministry-number/?ministry_number=${ministryNumber}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        if (response.status === 403) {
                            // Redirect to login page if not authenticated
                            window.location.href = '/accounts/login/?next=' + encodeURIComponent(window.location.pathname);
                            throw new Error('يجب تسجيل الدخول للمتابعة');
                        }
                        return response.json().then(data => {
                            throw new Error(data.error || 'حدث خطأ أثناء البحث');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);

                    // Show debug info if available
                    if (data.debug_info) {
                        console.log('Search debug info:', data.debug_info);
                    }

                    // Check if data is successful and employee data exists
                    if (!data.success) {
                        let errorMessage = data.error || 'بيانات الموظف غير متوفرة';
                        if (data.debug_info) {
                            console.log('Search failed. Debug info:', data.debug_info);
                            if (data.debug_info.total_employees) {
                                errorMessage += `\n\nمعلومات إضافية:\n`;
                                errorMessage += `- إجمالي الموظفين في النظام: ${data.debug_info.total_employees}\n`;
                                errorMessage += `- إجمالي البيانات التعريفية: ${data.debug_info.total_identifications}\n`;
                                errorMessage += `- إجمالي التوظيفات: ${data.debug_info.total_employments}\n`;
                                errorMessage += `- الرقم المبحوث عنه: "${data.debug_info.searched_number}"`;
                            }
                        }
                        throw new Error(errorMessage);
                    }

                    if (!data.employee) {
                        throw new Error('بيانات الموظف غير متوفرة');
                    }

                    // Update form fields
                    employeeNameDisplay.value = data.employee.full_name;
                    employeeIdInput.value = data.employee.id;

                    // Update select field (hidden)
                    const selectElement = document.getElementById('id_employee');
                    if (selectElement && selectElement.options) {
                        // Check if option exists
                        let optionExists = false;
                        for (let i = 0; i < selectElement.options.length; i++) {
                            if (selectElement.options[i].value == data.employee.id) {
                                selectElement.options[i].selected = true;
                                optionExists = true;
                                break;
                            }
                        }

                        // If option doesn't exist, create it
                        if (!optionExists) {
                            const newOption = new Option(data.employee.full_name, data.employee.id, true, true);
                            selectElement.appendChild(newOption);
                        }
                    } else if (selectElement) {
                        // If options is undefined but selectElement exists, create a new option
                        const newOption = new Option(data.employee.full_name, data.employee.id, true, true);
                        selectElement.appendChild(newOption);
                    }
                })
                .catch(error => {
                    alert(error.message);
                    employeeNameDisplay.value = '';
                    employeeIdInput.value = '';
                })
                .finally(() => {
                    // Reset button
                    searchButton.innerHTML = '<i class="fas fa-search"></i> بحث';
                    searchButton.disabled = false;
                });
        }

        // Add event listeners
        if (searchButton) {
            searchButton.addEventListener('click', searchEmployee);
        }

        // Allow searching by pressing Enter in the ministry number field
        if (ministryNumberInput) {
            ministryNumberInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent form submission
                    searchEmployee();
                }
            });
        }

        // If we have a pre-filled ministry number (edit mode), trigger search
        if (ministryNumberInput && ministryNumberInput.value && employeeNameDisplay && !employeeNameDisplay.value) {
            searchEmployee();
        }
    });

    // Form validation function
    function validateForm() {
        console.log('Validating form...');

        // Get form elements
        const ministryNumberInput = document.getElementById('ministry_number_input');
        const employeeIdInput = document.getElementById('id_employee_id');
        const employeeNameDisplay = document.getElementById('employee_name_display');
        const positionSelect = document.getElementById('id_position');
        const dateInput = document.getElementById('id_date_obtained');
        const saveButton = document.getElementById('saveButton');

        // Check if employee is selected
        if (!employeeIdInput || !employeeIdInput.value) {
            alert('الرجاء اختيار موظف أولاً');
            return false;
        }

        // Check if position is selected
        if (!positionSelect || !positionSelect.value) {
            alert('الرجاء اختيار المسمى الوظيفي');
            return false;
        }

        // Check if date is entered
        if (!dateInput || !dateInput.value) {
            alert('الرجاء إدخال تاريخ الحصول على المسمى الوظيفي');
            return false;
        }

        // Check if school level is required and selected
        const selectedOption = positionSelect.options[positionSelect.selectedIndex];
        const positionName = selectedOption ? selectedOption.text.trim() : '';
        const isTeacher = positionName === 'معلم' ||
                         positionName.includes('معلم') ||
                         positionName.startsWith('معلم');

        if (isTeacher) {
            const schoolLevelSelect = document.getElementById('id_school_level');
            if (!schoolLevelSelect || !schoolLevelSelect.value) {
                alert('الرجاء اختيار المرحلة التي يدرسها المعلم');
                return false;
            }
        }

        // Make sure the employee ID is set in the form
        if (employeeIdInput && employeeIdInput.value) {
            const selectElement = document.getElementById('id_employee');
            if (selectElement) {
                // Check if option exists
                let optionExists = false;
                for (let i = 0; i < selectElement.options.length; i++) {
                    if (selectElement.options[i].value == employeeIdInput.value) {
                        selectElement.options[i].selected = true;
                        optionExists = true;
                        break;
                    }
                }

                // If option doesn't exist, create it
                if (!optionExists) {
                    const newOption = new Option(employeeNameDisplay.value, employeeIdInput.value, true, true);
                    selectElement.appendChild(newOption);
                }
            }
        }

        // Disable the save button to prevent double submission
        if (saveButton) {
            saveButton.disabled = true;
            saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        }

        console.log('Form validation passed, submitting...');
        return true;
    }

    // Load existing data when editing
    function loadExistingData() {
        {% if employee_position %}
        // We are in edit mode, load the existing employee data
        const ministryNumber = '{{ employee_position.employee.ministry_number|default:"" }}';
        const employeeName = '{{ employee_position.employee.full_name|default:"" }}';
        const employeeId = '{{ employee_position.employee.id|default:"" }}';

        console.log('Loading existing data:', {
            ministryNumber: ministryNumber,
            employeeName: employeeName,
            employeeId: employeeId
        });

        // Set the values in the form
        if (ministryNumber) {
            const ministryInput = document.getElementById('ministry_number_input');
            if (ministryInput) {
                ministryInput.value = ministryNumber;
                console.log('Set ministry number:', ministryNumber);
            }
        }
        if (employeeName) {
            const nameDisplay = document.getElementById('employee_name_display');
            if (nameDisplay) {
                nameDisplay.value = employeeName;
                console.log('Set employee name:', employeeName);
            }
        }
        if (employeeId) {
            const employeeIdInput = document.getElementById('id_employee_id');
            if (employeeIdInput) {
                employeeIdInput.value = employeeId;
                console.log('Set employee ID:', employeeId);
            }
        }
        {% endif %}
    }

    // Call loadExistingData when page loads with a delay to ensure DOM is ready
    setTimeout(loadExistingData, 100);

    // Also call it when DOM is fully loaded
    document.addEventListener('DOMContentLoaded', loadExistingData);
</script>
{% endblock %}
