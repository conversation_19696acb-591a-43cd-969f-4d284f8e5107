@echo off
chcp 65001 > nul
echo ========================================
echo إعداد MySQL لنظام الموارد البشرية
echo MySQL Setup for HR System
echo ========================================
echo.

echo الخطوة 1: فحص حالة MySQL الحالية...
echo ========================================
venv\Scripts\python.exe check_mysql_status.py
echo.

echo ========================================
echo تحليل النتائج...
echo ========================================
echo.

echo إذا كانت جميع الفحوصات نجحت، يمكنك الانتقال مباشرة للخطوة 4
echo إذا فشلت بعض الفحوصات، اتبع الخطوات التالية:
echo.

echo ========================================
echo الخطوة 2: تثبيت MySQL (إذا لم يكن مثبت)
echo ========================================
echo.
echo اختر إحدى الطرق التالية:
echo.
echo أ) تثبيت XAMPP (الأسهل):
echo    1. اذهب إلى: https://www.apachefriends.org/download.html
echo    2. حمل وثبت XAMPP
echo    3. شغل XAMPP Control Panel
echo    4. اضغط Start بجانب MySQL
echo.
echo ب) تثبيت MySQL Server:
echo    1. اذهب إلى: https://dev.mysql.com/downloads/mysql/
echo    2. حمل MySQL Installer
echo    3. اتبع معالج التثبيت
echo.
set /p mysql_ready="هل تم تثبيت وتشغيل MySQL؟ (y/n): "
if /i not "%mysql_ready%"=="y" (
    echo.
    echo يرجى تثبيت وتشغيل MySQL أولاً، ثم إعادة تشغيل هذا السكريپت
    echo راجع ملف: install_mysql_guide.md للتفاصيل
    pause
    exit /b 1
)

echo.
echo ========================================
echo الخطوة 3: إعداد قاعدة البيانات
echo ========================================
echo.

echo فحص الاتصال بـ MySQL...
venv\Scripts\python.exe test_mysql_connection.py
if errorlevel 1 (
    echo.
    echo لم يتم إعداد قاعدة البيانات بعد...
    echo.
    echo اختر طريقة الإعداد:
    echo 1. إعداد تلقائي (يتطلب كلمة مرور root)
    echo 2. إعداد يدوي (phpMyAdmin أو MySQL Command Line)
    echo.
    set /p setup_choice="اختر (1 أو 2): "
    
    if "%setup_choice%"=="1" (
        echo.
        echo تشغيل الإعداد التلقائي...
        venv\Scripts\python.exe setup_mysql.py
        if errorlevel 1 (
            echo فشل الإعداد التلقائي
            goto manual_setup
        )
    ) else (
        :manual_setup
        echo.
        echo للإعداد اليدوي:
        echo 1. افتح phpMyAdmin: http://localhost/phpmyadmin
        echo 2. أو افتح MySQL Command Line
        echo 3. نفذ الأوامر التالية:
        echo.
        echo CREATE DATABASE hr_system_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        echo CREATE USER 'hr_user'@'localhost' IDENTIFIED BY 'hr_password_2024';
        echo GRANT ALL PRIVILEGES ON hr_system_db.* TO 'hr_user'@'localhost';
        echo FLUSH PRIVILEGES;
        echo.
        set /p db_ready="هل تم إنشاء قاعدة البيانات؟ (y/n): "
        if /i not "%db_ready%"=="y" (
            echo يرجى إعداد قاعدة البيانات أولاً
            pause
            exit /b 1
        )
    )
)

echo.
echo ========================================
echo الخطوة 4: تحديث إعدادات النظام
echo ========================================
echo.

echo تحديث إعدادات Django لاستخدام MySQL...
venv\Scripts\python.exe switch_to_mysql.py
if errorlevel 1 (
    echo فشل في تحديث الإعدادات
    pause
    exit /b 1
)

echo.
echo اختبار الاتصال النهائي...
venv\Scripts\python.exe test_mysql_connection.py
if errorlevel 1 (
    echo فشل اختبار الاتصال النهائي
    echo تحقق من:
    echo 1. تشغيل خدمة MySQL
    echo 2. صحة معلومات قاعدة البيانات
    echo 3. صحة إعدادات الاتصال
    pause
    exit /b 1
)

echo.
echo ========================================
echo الخطوة 5: تطبيق المخططات واستيراد البيانات
echo ========================================
echo.

echo تطبيق مخططات قاعدة البيانات...
venv\Scripts\python.exe manage.py migrate
if errorlevel 1 (
    echo فشل في تطبيق المخططات
    pause
    exit /b 1
)

echo.
echo فحص وجود النسخة الاحتياطية...
if exist "backup_data.json" (
    echo تم العثور على النسخة الاحتياطية
    set /p import_data="هل تريد استيراد البيانات من SQLite؟ (y/n): "
    if /i "%import_data%"=="y" (
        echo استيراد البيانات...
        venv\Scripts\python.exe manage.py loaddata backup_data.json
        if errorlevel 1 (
            echo تحذير: فشل في استيراد بعض البيانات
            echo يمكنك المتابعة أو إعادة المحاولة لاحقاً
        ) else (
            echo تم استيراد البيانات بنجاح
        )
    )
) else (
    echo لم يتم العثور على نسخة احتياطية
    echo سيتم إنشاء قاعدة بيانات فارغة
)

echo.
echo ========================================
echo الخطوة 6: التحقق النهائي
echo ========================================
echo.

echo تشغيل اختبارات التحقق...
venv\Scripts\python.exe verify_migration.py
if errorlevel 1 (
    echo تحذير: بعض اختبارات التحقق فشلت
    echo يمكنك المتابعة ولكن راجع الأخطاء
)

echo.
echo ========================================
echo اكتمل الإعداد!
echo ========================================
echo.

echo تم إعداد MySQL بنجاح!
echo.
echo يمكنك الآن:
echo 1. تشغيل النظام: python manage.py runserver
echo 2. زيارة: http://localhost:8000
echo 3. إنشاء مستخدم إداري جديد: python manage.py createsuperuser
echo.

echo ملاحظات مهمة:
echo - تم حفظ إعدادات SQLite في حالة الحاجة للعودة
echo - يمكن العودة لـ SQLite باستخدام: rollback_to_sqlite.bat
echo - قم بإنشاء نسخ احتياطية منتظمة من MySQL
echo.

set /p start_server="هل تريد تشغيل النظام الآن؟ (y/n): "
if /i "%start_server%"=="y" (
    echo.
    echo تشغيل النظام...
    echo اضغط Ctrl+C لإيقاف السيرفر
    echo.
    venv\Scripts\python.exe manage.py runserver
)

echo.
echo شكراً لاستخدام نظام الموارد البشرية!
pause
