from django.urls import path
from . import views

app_name = 'home'

urlpatterns = [
    path('', views.home_view, name='home'),
    path('analytics-dashboard/', views.analytics_dashboard_view, name='analytics_dashboard'),
    path('important-links/', views.important_links_view, name='important_links'),
    path('internal-transfer/', views.internal_transfer_view, name='internal_transfer'),
    path('internal-transfer/search/', views.search_employee, name='search_employee'),
    path('internal-transfer/search-request/', views.search_transfer_request, name='search_transfer_request'),
    path('internal-transfer/success/<str:token>/', views.internal_transfer_success, name='internal_transfer_success'),
    path('internal-transfer/edit/<str:token>/', views.internal_transfer_edit, name='internal_transfer_edit'),
    path('about/', views.about_view, name='about'),
    path('search/', views.search_view, name='search'),


    path('internal-transfer-list/', views.internal_transfer_list_view, name='internal_transfer_list'),
    path('internal-transfer-detail/<int:pk>/', views.internal_transfer_detail_view, name='internal_transfer_detail'),
    path('internal-transfer-detail/<int:pk>/delete/', views.internal_transfer_delete, name='internal_transfer_delete'),
    path('admin-internal-transfer-edit/<int:pk>/', views.admin_internal_transfer_edit, name='admin_internal_transfer_edit'),
    path('internal-transfer-toggle/', views.toggle_internal_transfer, name='toggle_internal_transfer'),
    path('internal-transfer-enable/', views.enable_internal_transfer, name='enable_internal_transfer'),
    path('internal-transfer-disable/', views.disable_internal_transfer, name='disable_internal_transfer'),
    path('internal-transfer-delete-all/', views.delete_all_internal_transfers, name='delete_all_internal_transfers'),
    path('internal-transfer-export/', views.export_internal_transfers_to_excel, name='export_internal_transfers'),
    path('internal-transfer-print-letters/', views.print_transfer_letters, name='print_transfer_letters'),
    path('internal-transfer-print-letter/<int:transfer_id>/', views.print_single_transfer_letter, name='print_single_transfer_letter'),
    path('internal-transfer-print-summary/', views.print_transfer_summary, name='print_transfer_summary'),
    path('admin-internal-transfer-create/', views.admin_internal_transfer_create, name='admin_internal_transfer_create'),

    # Technical Transfer URLs
    path('technical-transfer/', views.technical_transfer_view, name='technical_transfer'),
    path('technical-transfer/search/', views.technical_transfer_search, name='technical_transfer_search'),
    path('technical-transfer/list/', views.technical_transfer_list, name='technical_transfer_list'),
    path('technical-transfer/print/<int:pk>/', views.technical_transfer_print, name='technical_transfer_print'),
    path('technical-transfer/delete/<int:pk>/', views.technical_transfer_delete, name='technical_transfer_delete'),

    # Approved Forms URLs
    path('approved-forms/', views.approved_forms_list, name='approved_forms_list'),
    path('approved-forms/admin/', views.approved_forms_admin, name='approved_forms_admin'),
    path('approved-forms/create/', views.approved_form_create, name='approved_form_create'),
    path('approved-forms/<int:pk>/update/', views.approved_form_update, name='approved_form_update'),
    path('approved-forms/<int:pk>/delete/', views.approved_form_delete, name='approved_form_delete'),
    path('approved-forms/<int:pk>/download/', views.approved_form_download, name='approved_form_download'),

    # Leave Balance Inquiry URLs
    path('leave-balance-inquiry/', views.leave_balance_inquiry, name='leave_balance_inquiry'),
    path('get-employee-leave-balance/', views.get_employee_leave_balance, name='get_employee_leave_balance'),

    # Employee Inquiry URLs
    path('employee-inquiry/', views.employee_inquiry, name='employee_inquiry'),
    path('get-employee-details/', views.get_employee_details, name='get_employee_details'),

    # Important Links Management URLs
    path('important-links-admin/', views.important_links_admin, name='important_links_admin'),
    path('important-links-admin/add/', views.important_link_add, name='important_link_add'),
    path('important-links-admin/update/<int:pk>/', views.important_link_update, name='important_link_update'),
    path('important-links-admin/delete/<int:pk>/', views.important_link_delete, name='important_link_delete'),

    # Search Employee for Internal Transfer
    path('search-employee/', views.search_employee, name='search_employee'),
    path('search-transfer-request/', views.search_transfer_request, name='search_transfer_request'),

    # Employee Transfer Management URLs
    path('employee-transfer-management/', views.employee_transfer_management, name='employee_transfer_management'),
    path('search-employee-for-transfer/', views.search_employee_for_transfer, name='search_employee_for_transfer'),
    path('add-employee-transfer/', views.add_employee_transfer, name='add_employee_transfer'),
    path('edit-employee-transfer/<int:transfer_id>/', views.edit_employee_transfer, name='edit_employee_transfer'),
    path('update-employee-transfer/', views.update_employee_transfer, name='update_employee_transfer'),
    path('delete-employee-transfer/<int:transfer_id>/', views.delete_employee_transfer_confirm, name='delete_employee_transfer_confirm'),
    path('delete-employee-transfer/', views.delete_employee_transfer, name='delete_employee_transfer'),
    path('print-transfer-letter/<int:transfer_id>/', views.print_transfer_letter, name='print_transfer_letter'),

    # Organizational Chart URL
    path('organizational-chart/', views.organizational_chart_view, name='organizational_chart'),
]
