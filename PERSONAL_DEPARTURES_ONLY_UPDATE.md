# حساب المغادرات الخاصة فقط في التقارير
# Calculate Personal Departures Only in Reports

## ✅ التحديث المنفذ

### 🎯 الهدف:
تحديث حساب المغادرات في التقارير بحيث تحسب **المغادرات الخاصة فقط** وتستثني **المغادرات الرسمية**.

## 🔧 التغييرات المطبقة

### **1. صفحة تقرير الإجازات (`leaves/views.py`):**

#### **قبل التحديث:**
```python
# حساب مجموع المغادرات للموظف خلال السنة (بالأيام)
departures_total_days = 0
departures = Departure.objects.filter(
    employee=employee,
    date__year=current_year,
    status='approved'
)
```

#### **بعد التحديث:**
```python
# حساب مجموع المغادرات الخاصة فقط للموظف خلال السنة (بالأيام)
departures_total_days = 0
departures = Departure.objects.filter(
    employee=employee,
    date__year=current_year,
    status='approved',
    departure_type='personal'  # المغادرات الخاصة فقط
)
```

### **2. صفحة تقرير موظفي المديرية (`reports/views_directorate.py`):**

#### **قبل التحديث:**
```python
# Get departures for annual leave
from leaves.models import Departure
departures = Departure.objects.filter(
    employee=employee,
    date__year=current_year,
    status='approved'
)
```

#### **بعد التحديث:**
```python
# Get personal departures only for annual leave
from leaves.models import Departure
departures = Departure.objects.filter(
    employee=employee,
    date__year=current_year,
    status='approved',
    departure_type='personal'  # المغادرات الخاصة فقط
)
```

### **3. تحديث تسمية الأعمدة:**

#### **في `templates/leaves/leave_reports.html`:**
```html
<!-- قبل التحديث -->
<th class="bg-purple text-white">المغادرات</th>

<!-- بعد التحديث -->
<th class="bg-purple text-white">المغادرات الخاصة</th>
```

#### **في `templates/reports/directorate_employees_report.html`:**
```html
<!-- قبل التحديث -->
<th class="bg-warning text-dark">المغادرات</th>

<!-- بعد التحديث -->
<th class="bg-warning text-dark">المغادرات الخاصة</th>
```

## 📊 النتائج المحققة

### **المغادرات المحسوبة:**
- ✅ **المغادرات الخاصة**: تُحسب وتُطرح من الإجازة السنوية
- ❌ **المغادرات الرسمية**: لا تُحسب ولا تؤثر على الإجازة السنوية

### **المنطق:**
- **المغادرات الخاصة**: شخصية ويجب أن تُطرح من رصيد الإجازة السنوية
- **المغادرات الرسمية**: عمل رسمي ولا يجب أن تؤثر على الإجازة الشخصية

## 🎨 التصميم في الجدول

### **عمود المغادرات الخاصة:**
| الصفحة | اللون | التسمية | المحتوى |
|---------|--------|---------|---------|
| **تقرير الإجازات** | 🟣 بنفسجي | "المغادرات الخاصة" | مجموع المغادرات الخاصة بالأيام |
| **تقرير موظفي المديرية** | 🟡 أصفر | "المغادرات الخاصة" | مجموع المغادرات الخاصة بالأيام |

### **حساب الرصيد المتبقي:**
```
الرصيد المتبقي = الرصيد الأولي - الإجازات المستخدمة - المغادرات الخاصة
```

## 🔍 الفرق بين قبل وبعد التحديث

### **قبل التحديث:**
- ❌ **جميع المغادرات**: خاصة + رسمية تُحسب
- ❌ **غير منطقي**: المغادرات الرسمية تؤثر على الإجازة الشخصية
- ❌ **رصيد أقل**: بسبب حساب المغادرات الرسمية

### **بعد التحديث:**
- ✅ **المغادرات الخاصة فقط**: تُحسب وتُطرح
- ✅ **منطقي**: المغادرات الرسمية لا تؤثر على الإجازة الشخصية
- ✅ **رصيد أدق**: يعكس الاستخدام الشخصي فقط

## 📋 أمثلة عملية

### **مثال موظف لديه:**
- **رصيد إجازة سنوية**: 30 يوم
- **إجازات مستخدمة**: 10 أيام
- **مغادرات خاصة**: 5 أيام (2.5 ساعة × 10 مرات)
- **مغادرات رسمية**: 8 أيام (4 ساعات × 14 مرة)

#### **قبل التحديث:**
```
الرصيد المتبقي = 30 - 10 - 5 - 8 = 7 أيام
```

#### **بعد التحديث:**
```
الرصيد المتبقي = 30 - 10 - 5 = 15 يوم
```

**الفرق**: 8 أيام إضافية لأن المغادرات الرسمية لم تعد تُحسب!

## 🧪 للتحقق

### **خطوات التحقق:**

#### **1. صفحة تقرير الإجازات:**
1. **افتح الصفحة**: http://localhost:8000/leaves/reports/
2. **تحقق من العمود**: يجب أن يظهر "المغادرات الخاصة"
3. **تحقق من القيم**: يجب أن تكون أقل من السابق (لا تشمل الرسمية)

#### **2. صفحة تقرير موظفي المديرية:**
1. **افتح الصفحة**: http://localhost:8000/reports/directorate-employees/
2. **تحقق من العمود**: يجب أن يظهر "المغادرات الخاصة"
3. **تحقق من القيم**: يجب أن تكون أقل من السابق

#### **3. اختبار عملي:**
1. **أضف مغادرة خاصة** لموظف
2. **أضف مغادرة رسمية** لنفس الموظف
3. **تحقق من التقرير**: يجب أن تظهر الخاصة فقط

### **النتائج المتوقعة:**
```
✅ عمود "المغادرات الخاصة" يظهر في كلا التقريرين
✅ المغادرات الرسمية لا تُحسب في التقارير
✅ الرصيد المتبقي أعلى من السابق (لعدم حساب الرسمية)
✅ القيم تعكس الاستخدام الشخصي فقط
```

## 🔧 الفوائد من التحديث

### **1. المنطق الصحيح:**
- المغادرات الخاصة تؤثر على الإجازة الشخصية
- المغادرات الرسمية لا تؤثر على الإجازة الشخصية

### **2. العدالة:**
- الموظفون لا يُعاقبون على المغادرات الرسمية
- الرصيد يعكس الاستخدام الشخصي الفعلي

### **3. الوضوح:**
- تسمية العمود توضح نوع المغادرات المحسوبة
- التقارير أكثر دقة ووضوحاً

## 📋 ملاحظات مهمة

### **1. البيانات الموجودة:**
- التحديث يؤثر على الحسابات الجديدة فقط
- البيانات السابقة تبقى كما هي في قاعدة البيانات

### **2. أنواع المغادرات:**
- **personal**: خاصة (تُحسب)
- **official**: رسمية (لا تُحسب)

### **3. التوافق:**
- التحديث متوافق مع باقي النظام
- لا يؤثر على صفحات المغادرات الأخرى

## الملفات المحدثة

1. **`leaves/views.py`**:
   - إضافة `departure_type='personal'` لتصفية المغادرات

2. **`reports/views_directorate.py`**:
   - إضافة `departure_type='personal'` لتصفية المغادرات

3. **`templates/leaves/leave_reports.html`**:
   - تحديث تسمية العمود إلى "المغادرات الخاصة"

4. **`templates/reports/directorate_employees_report.html`**:
   - تحديث تسمية العمود إلى "المغادرات الخاصة"

## الخلاصة

✅ **المغادرات الخاصة فقط تُحسب في التقارير**
✅ **المغادرات الرسمية لا تؤثر على الإجازة السنوية**
✅ **تسمية الأعمدة محدثة لتوضيح النوع**
✅ **الحسابات أكثر دقة ومنطقية**
✅ **العدالة في التعامل مع أنواع المغادرات المختلفة**

**التقارير الآن تحسب المغادرات الخاصة فقط بشكل منطقي وعادل! 🎉**
