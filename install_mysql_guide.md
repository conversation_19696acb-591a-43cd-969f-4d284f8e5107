# دليل تثبيت MySQL لنظام Windows
# MySQL Installation Guide for Windows

## الطريقة الأولى: تثبيت XAMPP (الأسهل والأسرع)

### 1. تحميل XAMPP
- اذهب إلى: https://www.apachefriends.org/download.html
- حمل النسخة الأحدث لـ Windows
- حجم الملف حوالي 150 ميجابايت

### 2. تثبيت XAMPP
1. شغل ملف التثبيت كمدير (Run as Administrator)
2. اختر المكونات التالية على الأقل:
   - ✅ Apache
   - ✅ MySQL
   - ✅ PHP
   - ✅ phpMyAdmin
3. اختر مجلد التثبيت (افتراضي: C:\xampp)
4. أكمل التثبيت

### 3. تشغيل MySQL
1. افتح XAMPP Control Panel
2. اضغط "Start" بجانب MySQL
3. يج<PERSON> أن ترى "Running" باللون الأخضر

### 4. إعداد قاعدة البيانات
1. اضغ<PERSON> "Admin" بجانب MySQL (سيفتح phpMyAdmin)
2. أو اذهب إلى: http://localhost/phpmyadmin
3. اضغط "SQL" في الأعلى
4. انسخ والصق الكود التالي:

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE hr_system_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم جديد
CREATE USER 'hr_user'@'localhost' IDENTIFIED BY 'hr_password_2024';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON hr_system_db.* TO 'hr_user'@'localhost';

-- تطبيق التغييرات
FLUSH PRIVILEGES;
```

5. اضغط "Go" لتنفيذ الأوامر

## الطريقة الثانية: تثبيت MySQL Server مباشرة

### 1. تحميل MySQL
- اذهب إلى: https://dev.mysql.com/downloads/mysql/
- اختر "MySQL Installer for Windows"
- حمل النسخة الكاملة

### 2. التثبيت
1. شغل MySQL Installer
2. اختر "Developer Default" أو "Server only"
3. اتبع معالج التثبيت
4. اختر كلمة مرور قوية لـ root

### 3. إعداد قاعدة البيانات
1. افتح MySQL Command Line Client
2. أدخل كلمة مرور root
3. نفذ الأوامر SQL المذكورة أعلاه

## التحقق من التثبيت

### اختبار الاتصال
```bash
# في Command Prompt
mysql -u hr_user -p
# أدخل كلمة المرور: hr_password_2024
```

### أو استخدم سكريپت الاختبار
```bash
python test_mysql_connection.py
```

## إعداد النظام للعمل مع MySQL

### 1. تحديث إعدادات Django
قم بتشغيل:
```bash
python switch_to_mysql.py
```

### 2. تطبيق المخططات
```bash
python manage.py migrate
```

### 3. استيراد البيانات
```bash
python manage.py loaddata backup_data.json
```

### 4. اختبار النظام
```bash
python manage.py runserver
```

## استكشاف الأخطاء الشائعة

### خطأ: "Can't connect to MySQL server"
**الحل:**
1. تأكد من تشغيل خدمة MySQL
2. في XAMPP: تأكد من أن MySQL يظهر "Running"
3. في Windows Services: تأكد من تشغيل "MySQL80" أو "MySQL"

### خطأ: "Access denied for user"
**الحل:**
1. تأكد من صحة اسم المستخدم وكلمة المرور
2. تأكد من إنشاء المستخدم بالأوامر الصحيحة
3. جرب إعادة إنشاء المستخدم

### خطأ: "Unknown database"
**الحل:**
1. تأكد من إنشاء قاعدة البيانات
2. تحقق من اسم قاعدة البيانات في settings.py

## ملاحظات مهمة

### للتطوير (XAMPP):
- MySQL يعمل على المنفذ 3306
- لا يحتاج كلمة مرور لـ root افتراضياً
- يمكن إيقاف وتشغيل MySQL من XAMPP Control Panel

### للإنتاج (MySQL Server):
- استخدم كلمات مرور قوية
- قم بتأمين التثبيت
- فعل SSL إذا أمكن

## الخطوات التالية

بعد إعداد MySQL بنجاح:
1. شغل `python switch_to_mysql.py`
2. شغل `python migrate_to_mysql.py`
3. اختبر النظام
4. أنشئ نسخة احتياطية من MySQL

## الدعم

إذا واجهت مشاكل:
1. تأكد من تشغيل MySQL
2. استخدم `python test_mysql_connection.py`
3. راجع سجلات الأخطاء في XAMPP أو MySQL
4. يمكن العودة إلى SQLite باستخدام `python rollback_to_sqlite.py`
