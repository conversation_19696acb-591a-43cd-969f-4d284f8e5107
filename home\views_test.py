from django.shortcuts import render
from django.conf import settings
import os

def test_static_files(request):
    """
    View to test static files loading
    """
    # Get all logo files
    logos_dir = os.path.join(settings.BASE_DIR, 'static', 'img', 'logos')
    logo_files = []

    if os.path.exists(logos_dir):
        logo_files = [f for f in os.listdir(logos_dir) if os.path.isfile(os.path.join(logos_dir, f))]

    # Get ID image files
    id_images_dir = os.path.join(settings.BASE_DIR, 'static', 'img')
    id_image_files = []

    if os.path.exists(id_images_dir):
        id_image_files = [f for f in os.listdir(id_images_dir) if f.startswith('jordan_id') and os.path.isfile(os.path.join(id_images_dir, f))]

    context = {
        'logo_files': logo_files,
        'id_image_files': id_image_files,
        'static_url': settings.STATIC_URL,
        'staticfiles_dirs': settings.STATICFILES_DIRS,
        'static_root': settings.STATIC_ROOT,
    }

    return render(request, 'home/test_static.html', context)

def image_test(request):
    """
    Simple view to test image loading
    """
    # Print debug information
    import os
    from django.conf import settings

    # Check if the image files exist
    logos_fixed_dir = os.path.join(settings.BASE_DIR, 'static', 'img', 'logos_fixed')
    staticfiles_logos_fixed_dir = os.path.join(settings.STATIC_ROOT, 'img', 'logos_fixed')

    debug_info = {
        'static_url': settings.STATIC_URL,
        'static_root': str(settings.STATIC_ROOT),
        'staticfiles_dirs': [str(d) for d in settings.STATICFILES_DIRS],
        'logos_fixed_exists': os.path.exists(logos_fixed_dir),
        'staticfiles_logos_fixed_exists': os.path.exists(staticfiles_logos_fixed_dir),
        'logos_fixed_files': os.listdir(logos_fixed_dir) if os.path.exists(logos_fixed_dir) else [],
        'staticfiles_logos_fixed_files': os.listdir(staticfiles_logos_fixed_dir) if os.path.exists(staticfiles_logos_fixed_dir) else [],
    }

    # Print debug information to console
    print("\n\n=== DEBUG INFORMATION ===")
    for key, value in debug_info.items():
        print(f"{key}: {value}")
    print("=========================\n\n")

    return render(request, 'home/image_test.html', {'debug_info': debug_info})
