{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if is_update %}تعديل الدرجة الوظيفية{% else %}إضافة درجة وظيفية{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .form-container {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #667eea;
    }

    .section-title {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-action {
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        min-width: 120px;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
    }

    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .form-floating > label {
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label {
        color: #667eea;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1 class="mb-3">
            <i class="fas fa-cogs"></i>
            {% if is_update %}تعديل الدرجة الوظيفية{% else %}إضافة درجة وظيفية جديدة{% endif %}
        </h1>
        <p class="lead mb-0">
            {% if is_update %}
                تعديل بيانات الدرجة الوظيفية
            {% else %}
                إضافة درجة وظيفية جديدة للنظام
            {% endif %}
        </p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container fade-in">
                <form method="post" id="jobGradeForm">
                    {% csrf_token %}
                    
                    <!-- Grade Information Section -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-info-circle"></i>
                            معلومات الدرجة الوظيفية
                        </h5>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="form-floating mb-3">
                                    {{ form.name }}
                                    <label for="{{ form.name.id_for_label }}" class="required-field">{{ form.name.label }}</label>
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.name.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-floating mb-3">
                                    {{ form.description }}
                                    <label for="{{ form.description.id_for_label }}">{{ form.description.label }}</label>
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.description.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'employees:job_grades_list' %}" class="btn btn-outline-secondary btn-action">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                        <button type="submit" class="btn btn-primary btn-action">
                            <i class="fas fa-save"></i>
                            {% if is_update %}تحديث الدرجة{% else %}حفظ الدرجة{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    console.log('Job Grade Form loaded');

    // Form validation
    $('#jobGradeForm').on('submit', function(e) {
        const name = $('#id_name').val().trim();

        if (!name) {
            e.preventDefault();
            alert('الرجاء إدخال اسم الدرجة الوظيفية');
            $('#id_name').focus();
            return false;
        }

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
        submitBtn.prop('disabled', true);

        // Re-enable button after 3 seconds (in case of error)
        setTimeout(function() {
            submitBtn.html(originalText);
            submitBtn.prop('disabled', false);
        }, 3000);
    });

    // Auto-resize textarea
    $('#id_description').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Focus on first input
    $('#id_name').focus();
});
</script>
{% endblock %}
