# إضافة الإحصائيات والفلاتر لصفحة المغادرات
# Adding Statistics and Filters to Departures Page

## التحسينات المضافة

### ✅ 1. بطاقات الإحصائيات

#### أ) الإحصائيات الرئيسية:
```html
<!-- إجمالي المغادرات -->
<div class="dashboard-card card-primary">
    <span class="text-xs">إجمالي المغادرات</span>
    <div class="h3">{{ total_departures }}</div>
    <i class="fas fa-plane-departure"></i>
</div>

<!-- مغادرات خاصة -->
<div class="dashboard-card card-info">
    <span class="text-xs">مغادرات خاصة</span>
    <div class="h3">{{ personal_departures }}</div>
    <i class="fas fa-user"></i>
</div>

<!-- مغادرات رسمية -->
<div class="dashboard-card card-success">
    <span class="text-xs">مغادرات رسمية</span>
    <div class="h3">{{ official_departures }}</div>
    <i class="fas fa-building"></i>
</div>

<!-- مغادرات هذا الشهر -->
<div class="dashboard-card card-warning">
    <span class="text-xs">هذا الشهر</span>
    <div class="h3">{{ this_month_departures }}</div>
    <i class="fas fa-calendar-alt"></i>
</div>
```

#### ب) إحصائيات الحالات:
```html
<!-- قيد الانتظار -->
<div class="dashboard-card card-warning">
    <span class="text-xs">قيد الانتظار</span>
    <div class="h3">{{ pending_departures }}</div>
    <i class="fas fa-hourglass-half"></i>
</div>

<!-- موافق عليها -->
<div class="dashboard-card card-success">
    <span class="text-xs">موافق عليها</span>
    <div class="h3">{{ approved_departures }}</div>
    <i class="fas fa-check"></i>
</div>

<!-- مرفوضة -->
<div class="dashboard-card card-danger">
    <span class="text-xs">مرفوضة</span>
    <div class="h3">{{ rejected_departures }}</div>
    <i class="fas fa-times"></i>
</div>
```

### ✅ 2. نظام الفلاتر المتقدم

#### أ) فلتر البحث:
- **البحث في**: اسم الموظف، الرقم الوزاري، السبب
- **نوع البحث**: بحث جزئي (contains)
- **واجهة**: حقل نص مع placeholder واضح

#### ب) فلتر نوع المغادرة:
```html
<select name="departure_type">
    <option value="">جميع الأنواع</option>
    <option value="personal">خاصة</option>
    <option value="official">رسمية</option>
</select>
```

#### ج) فلتر السنة:
- **مصدر البيانات**: السنوات المتاحة في قاعدة البيانات
- **ترتيب**: من الأحدث للأقدم
- **خيار افتراضي**: جميع السنوات

#### د) فلتر الحالة:
```html
<select name="status">
    <option value="">جميع الحالات</option>
    <option value="pending">قيد الانتظار</option>
    <option value="approved">موافق عليها</option>
    <option value="rejected">مرفوضة</option>
</select>
```

### ✅ 3. تحديث View (leaves/views.py)

#### الإحصائيات المحسوبة:
```python
# الإحصائيات الأساسية
total_departures = Departure.objects.count()
personal_departures = Departure.objects.filter(departure_type='personal').count()
official_departures = Departure.objects.filter(departure_type='official').count()

# إحصائيات الحالات
pending_departures = Departure.objects.filter(status='pending').count()
approved_departures = Departure.objects.filter(status='approved').count()
rejected_departures = Departure.objects.filter(status='rejected').count()

# إحصائيات زمنية
this_month_departures = Departure.objects.filter(
    date__month=current_month,
    date__year=current_year
).count()
```

#### منطق الفلاتر:
```python
# تطبيق الفلاتر
if departure_type:
    departures = departures.filter(departure_type=departure_type)

if year:
    departures = departures.filter(date__year=year)

if status:
    departures = departures.filter(status=status)

if search:
    departures = departures.filter(
        Q(employee__full_name__icontains=search) |
        Q(employee__ministry_number__icontains=search) |
        Q(reason__icontains=search)
    )
```

### ✅ 4. تصميم متقدم

#### أ) CSS مخصص للبطاقات:
- **تأثيرات hover**: رفع البطاقة وتغيير الظل
- **ألوان متدرجة**: لكل نوع بطاقة لون مميز
- **أيقونات دائرية**: خلفية شفافة ملونة

#### ب) تصميم الفلاتر:
```css
.filters-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.filters-card .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    backdrop-filter: blur(10px);
}
```

#### ج) تأثيرات بصرية:
- **خلفية متدرجة** للفلاتر
- **شفافية** للحقول
- **تأثير blur** للخلفية

### ✅ 5. ميزات إضافية

#### أ) عداد النتائج:
```html
{% if departures.count != total_departures %}
    <span class="badge bg-info">{{ departures.count }} من {{ total_departures }}</span>
{% endif %}
```

#### ب) أزرار التحكم:
- **تطبيق الفلاتر**: تشغيل البحث
- **إعادة تعيين**: مسح جميع الفلاتر

#### ج) حفظ حالة الفلاتر:
- الفلاتر المطبقة تبقى محفوظة بعد التطبيق
- إمكانية العودة للحالة الافتراضية

## الألوان والأيقونات

### بطاقات الإحصائيات:
| البطاقة | اللون | الأيقونة | المعنى |
|---------|-------|---------|--------|
| إجمالي المغادرات | أزرق (`#4e73df`) | `fas fa-plane-departure` | العدد الكلي |
| مغادرات خاصة | أزرق فاتح (`#36b9cc`) | `fas fa-user` | المغادرات الشخصية |
| مغادرات رسمية | أخضر (`#1cc88a`) | `fas fa-building` | المغادرات الرسمية |
| هذا الشهر | أصفر (`#f6c23e`) | `fas fa-calendar-alt` | الشهر الحالي |

### بطاقات الحالات:
| الحالة | اللون | الأيقونة | المعنى |
|--------|-------|---------|--------|
| قيد الانتظار | أصفر (`#f6c23e`) | `fas fa-hourglass-half` | في انتظار الموافقة |
| موافق عليها | أخضر (`#1cc88a`) | `fas fa-check` | تمت الموافقة |
| مرفوضة | أحمر (`#e74a3b`) | `fas fa-times` | تم الرفض |

## الاستخدام

### 1. عرض الإحصائيات:
- البطاقات تظهر تلقائياً عند تحميل الصفحة
- الأرقام محدثة في الوقت الفعلي
- تأثيرات بصرية عند التمرير

### 2. استخدام الفلاتر:
1. **اختر الفلاتر المطلوبة**
2. **اضغط "تطبيق الفلاتر"**
3. **النتائج تظهر فوراً**
4. **لإلغاء الفلاتر**: اضغط "إعادة تعيين"

### 3. البحث المتقدم:
- **بحث بالاسم**: اكتب جزء من اسم الموظف
- **بحث بالرقم الوزاري**: اكتب الرقم أو جزء منه
- **بحث بالسبب**: اكتب كلمة من سبب المغادرة

## الفوائد المحققة

### 1. رؤية شاملة:
- إحصائيات سريعة ومرئية
- فهم سريع لحالة المغادرات
- مقارنة بين الأنواع والحالات

### 2. بحث متقدم:
- فلترة دقيقة للبيانات
- بحث متعدد المعايير
- نتائج فورية

### 3. تجربة مستخدم محسنة:
- واجهة جذابة ومتجاوبة
- تصميم احترافي
- سهولة في الاستخدام

### 4. كفاءة في العمل:
- وصول سريع للمعلومات المطلوبة
- تقليل الوقت المطلوب للبحث
- تحسين الإنتاجية

## الملفات المحدثة

1. **`leaves/views.py`**: إضافة منطق الإحصائيات والفلاتر
2. **`templates/leaves/departure_list.html`**: إضافة البطاقات والفلاتر والتصميم

## الحالة
✅ **مكتمل** - تم إضافة جميع الإحصائيات والفلاتر المطلوبة بنجاح.
