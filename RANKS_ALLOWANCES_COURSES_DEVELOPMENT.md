# تطوير قسم الرتب والعلاوات والدورات
# Development of Ranks, Allowances and Courses Section

## ✅ التطوير المنفذ

### 🎯 الهدف:
تطوير قسم شامل للرتب والعلاوات والدورات يشمل:
1. تغيير اسم القسم إلى "الرتب والعلاوات والدورات"
2. إضافة نظام إدارة الدورات التدريبية
3. ربط الدورات بالموظفين مع إمكانية البحث والتصفية

## 🔧 التغييرات المطبقة

### **1. تحديث اسم القسم:**
```python
# في accounts/views.py
'ranks': 'الرتب والعلاوات والدورات',  # بدلاً من 'الرتب والدرجات'
```

### **2. إضافة نماذج الدورات في `ranks/models.py`:**

#### **أ. نموذج Course (الدورات):**
```python
class Course(models.Model):
    name = models.CharField(_('Course Name'), max_length=200, unique=True)
    description = models.TextField(_('Description'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Course')
        verbose_name_plural = _('Courses')
        ordering = ['name']

    def __str__(self):
        return self.name
```

#### **ب. نموذج EmployeeCourse (دورات الموظفين):**
```python
class EmployeeCourse(models.Model):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='courses')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='employees')
    hours = models.PositiveIntegerField(_('Hours'))
    completion_date = models.DateField(_('Completion Date'))
    certificate_number = models.CharField(_('Certificate Number'), max_length=100, blank=True, null=True)
    notes = models.TextField(_('Notes'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Employee Course')
        verbose_name_plural = _('Employee Courses')
        ordering = ['-completion_date']
        unique_together = ['employee', 'course', 'completion_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.course.name}"
```

### **3. إضافة النماذج (Forms) في `ranks/forms.py`:**

#### **أ. نموذج إدارة الدورات:**
```python
class CourseForm(forms.ModelForm):
    class Meta:
        model = Course
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
```

#### **ب. نموذج إضافة دورة لموظف:**
```python
class EmployeeCourseForm(forms.Form):
    ministry_number = forms.CharField(label='الرقم الوزاري', ...)
    course = forms.ModelChoiceField(queryset=Course.objects.all(), ...)
    hours = forms.IntegerField(label='عدد الساعات', ...)
    completion_date = forms.DateField(label='تاريخ الحصول على الدورة', ...)
    certificate_number = forms.CharField(label='رقم الشهادة', required=False, ...)
    notes = forms.CharField(label='ملاحظات', required=False, ...)
```

### **4. إضافة Views في `ranks/views.py`:**

#### **أ. إدارة الدورات:**
- `course_list`: عرض وإضافة الدورات
- `course_update`: تعديل دورة
- `course_delete`: حذف دورة

#### **ب. دورات الموظفين:**
- `employee_course_list`: عرض دورات الموظفين مع البحث والتصفية
- `employee_course_create`: إضافة دورة لموظف
- `employee_course_update`: تعديل دورة موظف
- `employee_course_delete`: حذف دورة موظف

### **5. تحديث URLs في `ranks/urls.py`:**
```python
# Course URLs
path('courses/', views.course_list, name='course_list'),
path('courses/<int:pk>/update/', views.course_update, name='course_update'),
path('courses/<int:pk>/delete/', views.course_delete, name='course_delete'),

# Employee Course URLs
path('employee-courses/', views.employee_course_list, name='employee_course_list'),
path('employee-courses/add/', views.employee_course_create, name='employee_course_create'),
path('employee-courses/<int:pk>/update/', views.employee_course_update, name='employee_course_update'),
path('employee-courses/<int:pk>/delete/', views.employee_course_delete, name='employee_course_delete'),
```

### **6. إضافة API للبحث عن الموظفين:**
```python
# في employees/views.py
@login_required
def employee_search_api(request):
    ministry_number = request.GET.get('ministry_number', '').strip()
    # ... منطق البحث
    return JsonResponse({
        'success': True,
        'employee': {
            'id': employee.id,
            'full_name': employee.full_name,
            'ministry_number': employee.ministry_number,
            'department': department_name,
            'position': position_name
        }
    })
```

## 📱 الصفحات المطورة

### **1. صفحة أسماء الدورات (`course_list.html`):**
- **المسار**: `/ranks/courses/`
- **الوظائف**:
  - عرض جميع الدورات المتاحة
  - إضافة دورة جديدة
  - تعديل دورة موجودة
  - حذف دورة
  - عرض تفاصيل الدورة في modal

### **2. صفحة الدورات (`employee_course_list.html`):**
- **المسار**: `/ranks/employee-courses/`
- **الوظائف**:
  - عرض دورات جميع الموظفين
  - البحث بالاسم، الرقم الوزاري، أو اسم الدورة
  - التصفية حسب الدورة والسنة
  - عرض تفاصيل الدورة في modal
  - تعديل وحذف دورات الموظفين

### **3. صفحة إضافة دورة لموظف (`employee_course_form.html`):**
- **المسار**: `/ranks/employee-courses/add/`
- **الوظائف**:
  - البحث عن الموظف بالرقم الوزاري
  - عرض معلومات الموظف
  - اختيار الدورة من قائمة منسدلة مع البحث (Select2)
  - إدخال تفاصيل الدورة (ساعات، تاريخ، رقم شهادة، ملاحظات)

## 🎨 التصميم والواجهة

### **الألوان والأيقونات:**
- 🎓 **الدورات**: أيقونة graduation-cap باللون الأزرق
- 📚 **أسماء الدورات**: أيقونة list باللون الأخضر
- ⭐ **الرتب**: أيقونة star باللون الذهبي
- 💰 **العلاوات**: أيقونة money-bill باللون الأخضر

### **التنقل:**
```
الرتب والعلاوات والدورات
├── رتب الموظفين
│   └── زر "الدورات" → صفحة دورات الموظفين
├── الدورات
│   ├── زر "أسماء الدورات" → إدارة أسماء الدورات
│   └── زر "إضافة دورة لموظف" → إضافة دورة جديدة
└── أسماء الدورات
    └── إدارة قائمة الدورات المتاحة
```

## 🔍 المميزات المطورة

### **1. البحث والتصفية:**
- البحث بالاسم، الرقم الوزاري، أو اسم الدورة
- التصفية حسب نوع الدورة
- التصفية حسب سنة الحصول على الدورة

### **2. واجهة سهلة الاستخدام:**
- Select2 للبحث في قائمة الدورات
- عرض معلومات الموظف تلقائياً عند البحث
- Modals لعرض التفاصيل والتأكيدات

### **3. التحقق من البيانات:**
- التحقق من وجود الموظف بالرقم الوزاري
- منع التكرار (موظف + دورة + تاريخ)
- التحقق من صحة التواريخ والأرقام

## 📊 هيكل البيانات

### **جدول الدورات (Course):**
| الحقل | النوع | الوصف |
|-------|--------|--------|
| id | AutoField | المعرف الفريد |
| name | CharField(200) | اسم الدورة (فريد) |
| description | TextField | وصف الدورة (اختياري) |
| created_at | DateTimeField | تاريخ الإنشاء |
| updated_at | DateTimeField | تاريخ التحديث |

### **جدول دورات الموظفين (EmployeeCourse):**
| الحقل | النوع | الوصف |
|-------|--------|--------|
| id | AutoField | المعرف الفريد |
| employee | ForeignKey | الموظف |
| course | ForeignKey | الدورة |
| hours | PositiveIntegerField | عدد الساعات |
| completion_date | DateField | تاريخ الحصول |
| certificate_number | CharField(100) | رقم الشهادة (اختياري) |
| notes | TextField | ملاحظات (اختياري) |
| created_at | DateTimeField | تاريخ الإنشاء |
| updated_at | DateTimeField | تاريخ التحديث |

## 🧪 للاختبار

### **خطوات التحقق:**

#### **1. الوصول للقسم:**
```
http://localhost:8000/ranks/
```

#### **2. اختبار أسماء الدورات:**
1. انقر على "أسماء الدورات"
2. أضف دورة جديدة
3. تأكد من عدم السماح بالتكرار
4. جرب التعديل والحذف

#### **3. اختبار دورات الموظفين:**
1. انقر على "الدورات"
2. انقر على "إضافة دورة لموظف"
3. ابحث عن موظف بالرقم الوزاري
4. اختر دورة من القائمة المنسدلة
5. أدخل التفاصيل واحفظ

#### **4. اختبار البحث والتصفية:**
1. في صفحة الدورات، جرب البحث بالاسم
2. جرب التصفية حسب الدورة
3. جرب التصفية حسب السنة

### **النتائج المتوقعة:**
```
✅ اسم القسم محدث إلى "الرتب والعلاوات والدورات"
✅ صفحة أسماء الدورات تعمل بشكل صحيح
✅ صفحة دورات الموظفين تعرض البيانات
✅ إضافة دورة لموظف تعمل مع البحث
✅ البحث والتصفية يعملان بشكل صحيح
✅ Select2 يعمل في قائمة الدورات
✅ API البحث عن الموظفين يعمل
```

## 📋 الملفات المحدثة والمضافة

### **الملفات المحدثة:**
1. `accounts/views.py` - تحديث اسم القسم
2. `ranks/models.py` - إضافة نماذج الدورات
3. `ranks/forms.py` - إضافة نماذج الدورات
4. `ranks/views.py` - إضافة views الدورات
5. `ranks/urls.py` - إضافة مسارات الدورات
6. `ranks/admin.py` - إضافة admin للدورات
7. `employees/views.py` - إضافة API البحث
8. `employees/urls.py` - إضافة مسار API
9. `templates/ranks/employee_rank_list.html` - إضافة زر الدورات

### **الملفات المضافة:**
1. `ranks/migrations/0002_add_courses.py` - Migration للدورات
2. `templates/ranks/course_list.html` - صفحة أسماء الدورات
3. `templates/ranks/employee_course_list.html` - صفحة دورات الموظفين
4. `templates/ranks/employee_course_form.html` - صفحة إضافة دورة

## 🎉 الخلاصة

✅ **تم تطوير نظام شامل للدورات التدريبية**
✅ **واجهة سهلة الاستخدام مع البحث والتصفية**
✅ **تكامل مع نظام الموظفين الموجود**
✅ **تصميم متطابق مع باقي النظام**
✅ **إمكانيات إدارة كاملة (إضافة، تعديل، حذف)**

**قسم الرتب والعلاوات والدورات الآن جاهز ومتكامل! 🎓✨**
