# تحديث تقارير الإجازات - إضافة المغادرات
# Leave Reports Update - Adding Departures

## ✅ التحديث المنفذ

### 🎯 الهدف:
طرح مجموع المغادرات للموظف خلال السنة من رصيد الإجازات السنوية لإظهار النتيجة النهائية بعد طرح الإجازات والمغادرات.

## 🔧 التحديثات المنفذة

### 1. تحديث View التقارير

#### **في `leaves/views.py` - دالة `leave_reports`:**

```python
# حساب مجموع المغادرات للموظف خلال السنة (بالأيام)
departures_total_days = 0
departures = Departure.objects.filter(
    employee=employee,
    date__year=current_year,
    status='approved'
)

for departure in departures:
    departures_total_days += departure.calculate_duration_days()

# للإجازات السنوية، نطرح المغادرات أيضاً
if leave_type.name == 'إجازة سنوية' or 'سنوية' in leave_type.name:
    remaining = balance.initial_balance - used_days - departures_total_days
    type_balance = {
        'leave_type': leave_type,
        'initial': balance.initial_balance,
        'used': used_days,
        'departures_used': departures_total_days,
        'remaining': remaining if remaining >= 0 else 0,
        'is_annual': True
    }
else:
    # للإجازات الأخرى، لا نطرح المغادرات
    remaining = balance.initial_balance - used_days
    type_balance = {
        'leave_type': leave_type,
        'initial': balance.initial_balance,
        'used': used_days,
        'departures_used': 0,
        'remaining': remaining if remaining >= 0 else 0,
        'is_annual': False
    }
```

### 2. تحديث القالب

#### **أ) تحديث رأس الجدول:**

```html
<thead class="thead-light">
    <tr>
        <th rowspan="2">الموظف</th>
        {% for leave_type in leave_types %}
            {% if 'سنوية' in leave_type.name %}
                <th colspan="4" class="text-center">{{ leave_type.get_name_display }}</th>
            {% else %}
                <th colspan="3" class="text-center">{{ leave_type.get_name_display }}</th>
            {% endif %}
        {% endfor %}
    </tr>
    <tr>
        {% for leave_type in leave_types %}
            <th class="bg-success text-white">الرصيد</th>
            <th class="bg-danger text-white">المستخدم</th>
            {% if 'سنوية' in leave_type.name %}
                <th class="bg-warning text-white">المغادرات</th>
            {% endif %}
            <th class="bg-info text-white">المتبقي</th>
        {% endfor %}
    </tr>
</thead>
```

#### **ب) تحديث محتوى الجدول:**

```html
{% for balance in item.type_balances %}
    <td class="bg-success bg-opacity-25 fw-bold fs-5">{{ balance.initial }}</td>
    <td class="bg-danger bg-opacity-25 fw-bold fs-5">{{ balance.used }}</td>
    {% if balance.is_annual %}
        <td class="bg-warning bg-opacity-25 fw-bold fs-5" title="مجموع المغادرات بالأيام">
            {{ balance.departures_used|floatformat:2 }}
        </td>
    {% endif %}
    <td class="bg-info bg-opacity-25 fw-bold fs-5">{{ balance.remaining|floatformat:2 }}</td>
{% endfor %}
```

### 3. إضافة بطاقات إحصائيات

#### **بطاقات معلوماتية في أعلى الصفحة:**

```html
<div class="row mb-4">
    <!-- إجمالي الموظفين -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                    إجمالي الموظفين
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ employee_balances|length }}</div>
            </div>
        </div>
    </div>

    <!-- موظفين لديهم مغادرات -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                    موظفين لديهم مغادرات
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">[عدد الموظفين]</div>
            </div>
        </div>
    </div>

    <!-- السنة الحالية -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                    السنة الحالية
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ current_year }}</div>
            </div>
        </div>
    </div>

    <!-- أنواع الإجازات -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                    أنواع الإجازات
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ leave_types|length }}</div>
            </div>
        </div>
    </div>
</div>
```

## 📊 كيفية الحساب

### **المعادلة المطبقة:**

#### **للإجازات السنوية:**
```
الرصيد المتبقي = الرصيد الأولي - الإجازات المستخدمة - المغادرات المستخدمة
```

#### **للإجازات الأخرى:**
```
الرصيد المتبقي = الرصيد الأولي - الإجازات المستخدمة
```

### **حساب المغادرات:**
- يتم جمع جميع المغادرات المعتمدة للموظف خلال السنة
- كل مغادرة يتم حساب مدتها بالأيام باستخدام: `calculate_duration_days()`
- المعادلة: كل 420 دقيقة = يوم واحد

### **مثال على الحساب:**

| الموظف | الرصيد الأولي | الإجازات المستخدمة | المغادرات | المتبقي |
|--------|---------------|-------------------|-----------|---------|
| أحمد محمد | 30 يوم | 10 أيام | 2.5 يوم | 17.5 يوم |
| فاطمة علي | 30 يوم | 5 أيام | 1.2 يوم | 23.8 يوم |

## 🎨 التحسينات البصرية

### **الألوان في الجدول:**
- **أخضر**: الرصيد الأولي
- **أحمر**: الإجازات المستخدمة  
- **أصفر**: المغادرات المستخدمة (للإجازات السنوية فقط)
- **أزرق**: الرصيد المتبقي

### **معلومات إضافية:**
- عنوان الصفحة محدث: "تقارير الإجازات والمغادرات"
- ملاحظة توضيحية: "يتم طرح المغادرات من رصيد الإجازات السنوية"
- عرض السنة الحالية في العنوان

## 🔍 الميزات الجديدة

### 1. **تمييز الإجازات السنوية:**
- عمود إضافي للمغادرات في الإجازات السنوية فقط
- حساب منفصل للإجازات السنوية والأخرى

### 2. **دقة في الحساب:**
- استخدام `floatformat:2` لعرض الأرقام العشرية
- حساب دقيق للمغادرات بالأيام

### 3. **معلومات شاملة:**
- بطاقات إحصائيات في أعلى الصفحة
- عرض السنة الحالية
- عدد الموظفين وأنواع الإجازات

### 4. **تحسين تجربة المستخدم:**
- أيقونات مميزة
- ألوان واضحة ومعبرة
- تلميحات (tooltips) للتوضيح

## 📋 البيانات المعروضة

### **لكل موظف وكل نوع إجازة:**

#### **الإجازات السنوية:**
1. **الرصيد**: الرصيد الأولي المخصص
2. **المستخدم**: الإجازات المعتمدة المأخوذة
3. **المغادرات**: مجموع المغادرات بالأيام
4. **المتبقي**: الرصيد بعد طرح الإجازات والمغادرات

#### **الإجازات الأخرى:**
1. **الرصيد**: الرصيد الأولي المخصص
2. **المستخدم**: الإجازات المعتمدة المأخوذة
3. **المتبقي**: الرصيد بعد طرح الإجازات فقط

## 🧪 للاختبار

### **خطوات التحقق:**
1. **افتح صفحة التقارير**: http://localhost:8000/leaves/reports/
2. **تحقق من البطاقات**: في أعلى الصفحة
3. **لاحظ الجدول**: 
   - عمود المغادرات للإجازات السنوية
   - الحساب الصحيح للرصيد المتبقي
4. **تحقق من الحسابات**: 
   - الرصيد المتبقي = الأولي - المستخدم - المغادرات (للسنوية)
   - الرصيد المتبقي = الأولي - المستخدم (للأخرى)

### **النتائج المتوقعة:**
- ✅ عرض المغادرات في عمود منفصل للإجازات السنوية
- ✅ طرح المغادرات من الرصيد المتبقي للإجازات السنوية
- ✅ عدم تأثير المغادرات على الإجازات الأخرى
- ✅ عرض الأرقام بدقة عشرية مناسبة

## الملفات المحدثة

1. **`leaves/views.py`**: تحديث دالة `leave_reports` لحساب المغادرات
2. **`templates/leaves/leave_reports.html`**: تحديث الجدول والواجهة

## الخلاصة

✅ **تم دمج المغادرات في تقارير الإجازات بنجاح**
✅ **حساب دقيق للرصيد المتبقي مع طرح المغادرات**
✅ **تمييز واضح بين الإجازات السنوية والأخرى**
✅ **واجهة محسنة مع معلومات شاملة**
✅ **بطاقات إحصائيات مفيدة**

**التقرير الآن يعطي صورة شاملة ودقيقة لاستخدام الإجازات والمغادرات! 🎉**
