from django.urls import path
from . import views
from . import retired_views
from . import external_transfer_views
from . import qualification_views
from . import maternity_views
from . import internal_transfer_views

app_name = 'employees'

urlpatterns = [
    path('', views.employee_list, name='employee_list'),
    path('ajax/', views.get_employees_ajax, name='get_employees_ajax'),
    path('add/', views.employee_create, name='employee_create'),
    path('<int:pk>/', views.employee_detail, name='employee_detail'),
    path('<int:pk>/edit/', views.employee_update, name='employee_update'),
    path('<int:pk>/delete/', views.employee_delete, name='employee_delete'),
    path('import-export/', views.employee_import_export, name='employee_import_export'),
    path('get-by-ministry-number/', views.get_employee_by_ministry_number, name='get_employee_by_ministry_number'),
    path('annual-report/add/', views.annual_report_create, name='annual_report_create'),
    path('calculate-age/', views.calculate_age, name='calculate_age'),
    path('add-qualification/', qualification_views.add_qualification, name='add_qualification'),
    path('search-employee-for-qualification/', qualification_views.search_employee_for_qualification, name='search_employee_for_qualification'),
    
    # Maternity leaves URLs
    path('maternity-leaves/', maternity_views.maternity_leaves_list, name='maternity_leaves_list'),
    path('maternity-leaves/add/', maternity_views.add_maternity_leave, name='add_maternity_leave'),
    path('maternity-leaves/<int:pk>/', maternity_views.maternity_leave_detail, name='maternity_leave_detail'),
    path('maternity-leaves/<int:pk>/edit/', maternity_views.maternity_leave_update, name='maternity_leave_update'),
    path('maternity-leaves/<int:pk>/delete/', maternity_views.maternity_leave_delete, name='maternity_leave_delete'),
    path('search-employee-for-maternity/', maternity_views.search_employee_for_maternity, name='search_employee_for_maternity'),
    path('export-maternity-leaves-excel/', maternity_views.export_maternity_leaves_excel, name='export_maternity_leaves_excel'),
    
    # Retired employees URLs
    path('retired/', retired_views.retired_employees_list, name='retired_employees_list'),
    path('retired/<int:pk>/', retired_views.retired_employee_detail, name='retired_employee_detail'),
    path('retired/<int:pk>/edit/', retired_views.retired_employee_update, name='retired_employee_update'),
    path('retired/<int:pk>/delete/', retired_views.retired_employee_delete, name='retired_employee_delete'),
    path('search-employees-for-retirement/', retired_views.search_employees_for_retirement, name='search_employees_for_retirement'),
    
    # External transfers URLs
    path('external-transfers/', external_transfer_views.external_transfers_list, name='external_transfers_list'),
    path('external-transfers/<int:pk>/', external_transfer_views.external_transfer_detail, name='external_transfer_detail'),
    path('external-transfers/<int:pk>/edit/', external_transfer_views.external_transfer_update, name='external_transfer_update'),
    path('external-transfers/<int:pk>/delete/', external_transfer_views.external_transfer_delete, name='external_transfer_delete'),
    path('search-employees-for-transfer/', external_transfer_views.search_employees_for_transfer, name='search_employees_for_transfer'),
    
    # Internal transfers URLs
    path('internal-transfers/', internal_transfer_views.internal_transfers_list, name='internal_transfers_list'),
    path('internal-transfers/<int:pk>/', internal_transfer_views.internal_transfer_detail, name='internal_transfer_detail'),
    path('internal-transfers/<int:pk>/edit/', internal_transfer_views.internal_transfer_update, name='internal_transfer_update'),
    path('internal-transfers/<int:pk>/delete/', internal_transfer_views.internal_transfer_delete, name='internal_transfer_delete'),
    path('internal-transfers/statistics/', internal_transfer_views.internal_transfers_statistics, name='internal_transfers_statistics'),
    path('internal-transfers/statistics/api/', internal_transfer_views.internal_transfers_statistics_api, name='internal_transfers_statistics_api'),
    path('export-internal-transfers-excel/', internal_transfer_views.export_internal_transfers_excel, name='export_internal_transfers_excel'),

    # Teacher Specialty Assignment URLs
    path('specialty-assignments/', views.specialty_assignments_list, name='specialty_assignments_list'),
    path('specialty-assignments/add/', views.specialty_assignment_create, name='specialty_assignment_create'),
    path('specialty-assignments/<int:pk>/', views.specialty_assignment_detail, name='specialty_assignment_detail'),
    path('specialty-assignments/<int:pk>/edit/', views.specialty_assignment_update, name='specialty_assignment_update'),
    path('specialty-assignments/<int:pk>/delete/', views.specialty_assignment_delete, name='specialty_assignment_delete'),
    path('specialty-assignments/export/', views.specialty_assignments_export, name='specialty_assignments_export'),
    path('search-employee-for-assignment/', views.search_employee_for_assignment, name='search_employee_for_assignment'),

    # Employee Assignments URLs
    path('assignments/', views.employee_assignments_list, name='employee_assignments_list'),
    path('assignments/create/', views.employee_assignment_create, name='employee_assignment_create'),
    path('assignments/<int:pk>/edit/', views.employee_assignment_update, name='employee_assignment_update'),
    path('assignments/<int:pk>/delete/', views.employee_assignment_delete, name='employee_assignment_delete'),
    path('assignments/export/', views.export_employee_assignments, name='export_employee_assignments'),

    # Shared Employees URLs
    path('shared-employees/', views.shared_employees_list, name='shared_employees_list'),
    path('shared-employees/add/', views.shared_employee_create, name='shared_employee_create'),
    path('shared-employees/<int:pk>/edit/', views.shared_employee_update, name='shared_employee_update'),
    path('shared-employees/<int:pk>/delete/', views.shared_employee_delete, name='shared_employee_delete'),
    path('shared-employees/export/', views.export_shared_employees, name='export_shared_employees'),
    path('search-employee-for-sharing/', views.search_employee_for_sharing, name='search_employee_for_sharing'),

    # Employee Grades URLs
    path('grades/', views.employee_grades_list, name='employee_grades_list'),
    path('grades/add/', views.employee_grade_create, name='employee_grade_create'),
    path('grades/<int:pk>/edit/', views.employee_grade_update, name='employee_grade_update'),
    path('grades/<int:pk>/delete/', views.employee_grade_delete, name='employee_grade_delete'),
    path('grades/export/', views.export_employee_grades_excel, name='export_employee_grades_excel'),
    path('search-employee-for-grade/', views.search_employee_for_grade, name='search_employee_for_grade'),

    # Job Grades URLs
    path('job-grades/', views.job_grades_list, name='job_grades_list'),
    path('job-grades/add/', views.job_grade_create, name='job_grade_create'),
    path('job-grades/<int:pk>/edit/', views.job_grade_update, name='job_grade_update'),
    path('job-grades/<int:pk>/delete/', views.job_grade_delete, name='job_grade_delete'),

    # Promotion Types URLs
    path('promotion-types/', views.promotion_types_list, name='promotion_types_list'),
    path('promotion-types/add/', views.promotion_type_create, name='promotion_type_create'),
    path('promotion-types/<int:pk>/edit/', views.promotion_type_update, name='promotion_type_update'),
    path('promotion-types/<int:pk>/delete/', views.promotion_type_delete, name='promotion_type_delete'),

    # API endpoints
    path('api/search/', views.employee_search_api, name='employee_search_api'),
]
