from django import forms
from .models import LeaveType, LeaveBalance, Leave, Departure
from employees.models import Employee

class LeaveTypeForm(forms.ModelForm):
    class Meta:
        model = LeaveType
        fields = ['name', 'description', 'max_days_per_year']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

class LeaveBalanceForm(forms.ModelForm):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Read-only field for displaying remaining balance
    remaining_balance = forms.IntegerField(
        label='الرصيد المتبقي',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'remaining_balance_display'})
    )

    # Hidden field for used balance (will be calculated automatically)
    used_balance = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = LeaveBalance
        fields = ['employee', 'leave_type', 'year', 'initial_balance', 'used_balance']
        widgets = {
            'employee': forms.HiddenInput(),
        }

    def clean_initial_balance(self):
        """التحقق من أن الرصيد الأولي للإجازات السنوية لا يزيد عن 60 يوم"""
        initial_balance = self.cleaned_data.get('initial_balance')
        leave_type = self.cleaned_data.get('leave_type')

        if initial_balance and leave_type:
            # التحقق إذا كان نوع الإجازة هو إجازة سنوية
            if 'سنوية' in leave_type.name or 'annual' in leave_type.name.lower():
                if initial_balance > 60:
                    raise forms.ValidationError(
                        'الرصيد الأولي للإجازات السنوية لا يمكن أن يزيد عن 60 يوم'
                    )

        return initial_balance

    def clean(self):
        """التحقق الشامل من البيانات"""
        cleaned_data = super().clean()
        initial_balance = cleaned_data.get('initial_balance')
        leave_type = cleaned_data.get('leave_type')

        # التحقق مرة أخرى في التحقق الشامل
        if initial_balance and leave_type:
            if 'سنوية' in leave_type.name or 'annual' in leave_type.name.lower():
                if initial_balance > 60:
                    raise forms.ValidationError(
                        'الرصيد الأولي للإجازات السنوية لا يمكن أن يزيد عن 60 يوم'
                    )

        return cleaned_data

class LeaveForm(forms.ModelForm):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    class Meta:
        model = Leave
        fields = ['employee', 'leave_type', 'start_date', 'end_date', 'days_count', 'reason']
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-control', 'id': 'id_employee'}),
            'leave_type': forms.Select(attrs={'class': 'form-control'}),
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'days_count': forms.NumberInput(attrs={'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        employee = cleaned_data.get('employee')
        leave_type = cleaned_data.get('leave_type')

        # Check if start_date is before end_date
        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError(
                'تاريخ بداية الإجازة يجب أن يكون قبل تاريخ نهاية الإجازة'
            )

        # Check for overlapping leaves if employee and dates are provided
        if employee and start_date and end_date and leave_type:
            # Get the ID of the current leave being edited (if any)
            current_leave_id = self.instance.id if self.instance and self.instance.pk else None

            # Check for overlapping leaves, excluding the current leave being edited
            overlapping_leaves = Leave.objects.filter(
                employee=employee,
                status='approved',
                start_date__lte=end_date,
                end_date__gte=start_date
            )

            # Exclude current leave if editing
            if current_leave_id:
                overlapping_leaves = overlapping_leaves.exclude(id=current_leave_id)

            if overlapping_leaves.exists():
                # Get details of the first overlapping leave for the error message
                overlap = overlapping_leaves.first()
                raise forms.ValidationError(
                    f'الموظف لديه إجازة مسجلة بالفعل خلال هذه الفترة (من {overlap.start_date} إلى {overlap.end_date})'
                )

        # Check annual leave balance for annual leave types
        if employee and leave_type and start_date and end_date:
            # Check if this is an annual leave type
            if 'سنوية' in leave_type.name or 'annual' in leave_type.name.lower():
                from .models import LeaveBalance
                from django.db.models import Sum

                # Calculate requested days
                days_count = cleaned_data.get('days_count')
                if not days_count and start_date and end_date:
                    days_count = (end_date - start_date).days + 1

                # Get current year from start date
                year = start_date.year

                try:
                    # Get employee's annual leave balance for this year
                    leave_balance = LeaveBalance.objects.get(
                        employee=employee,
                        leave_type=leave_type,
                        year=year
                    )

                    # Calculate current remaining balance
                    current_remaining = leave_balance.remaining_balance

                    # If editing, add back the days from the current leave being edited
                    if current_leave_id:
                        current_leave = Leave.objects.get(id=current_leave_id)
                        if current_leave.status == 'approved':
                            current_remaining += current_leave.days_count

                    # Check if requested days exceed remaining balance
                    if days_count > current_remaining:
                        if current_remaining <= 0:
                            raise forms.ValidationError(
                                'لا يمكن إضافة إجازة سنوية. رصيد الموظف من الإجازات السنوية صفر.'
                            )
                        else:
                            raise forms.ValidationError(
                                f'عدد أيام الإجازة المطلوبة ({days_count} يوم) يتجاوز الرصيد المتبقي للموظف ({current_remaining} يوم).'
                            )

                except LeaveBalance.DoesNotExist:
                    raise forms.ValidationError(
                        f'لا يوجد رصيد إجازات سنوية مسجل للموظف للعام {year}. يرجى إضافة رصيد الإجازة أولاً.'
                    )

        return cleaned_data

class DepartureForm(forms.ModelForm):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control employee-search-input',
                                     'data-employee-id-target': 'id_employee_id',
                                     'data-employee-name-target': 'employee_name_display',
                                     'data-search-button-id': 'search_employee_btn',
                                     'data-error-target': 'ministry_number_error'})
    )

    class Meta:
        model = Departure
        fields = ['departure_type', 'date', 'time_from', 'time_to', 'reason', 'status']
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'time_from': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'time_to': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'departure_type': forms.Select(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        time_from = cleaned_data.get('time_from')
        time_to = cleaned_data.get('time_to')
        departure_type = cleaned_data.get('departure_type')

        if time_from and time_to:
            # التحقق من أن وقت البداية قبل وقت النهاية
            if time_from >= time_to:
                raise forms.ValidationError('وقت البداية يجب أن يكون قبل وقت النهاية.')

            # حساب مدة المغادرة بالدقائق
            from_minutes = time_from.hour * 60 + time_from.minute
            to_minutes = time_to.hour * 60 + time_to.minute

            # حساب الفرق بالدقائق
            duration_minutes = to_minutes - from_minutes

            # إذا كان الوقت النهائي أقل من البداية (عبور منتصف الليل)
            if duration_minutes < 0:
                duration_minutes += 24 * 60  # إضافة 24 ساعة

            # التحقق من أن المدة لا تتجاوز 4 ساعات (240 دقيقة) للمغادرات الخاصة فقط
            if departure_type == 'personal' and duration_minutes > 240:
                hours = duration_minutes // 60
                minutes = duration_minutes % 60
                raise forms.ValidationError(
                    f'مدة المغادرة الخاصة لا يجب أن تتجاوز 4 ساعات في اليوم الواحد. '
                    f'المدة المدخلة: {hours} ساعة و {minutes} دقيقة '
                    f'({duration_minutes} دقيقة). الحد الأقصى المسموح للمغادرات الخاصة: 240 دقيقة.'
                )

        return cleaned_data
