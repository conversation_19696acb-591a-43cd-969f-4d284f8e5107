# دليل إضافة الإجازات العرضية
# Guide to Add Casual Leave Type

## 🎯 الهدف
إضافة نوع الإجازة العرضية إلى النظام لتظهر في تقرير الإجازات بعد الإجازات المرضية.

## 🔧 طرق الإضافة

### الطريقة الأولى: عبر D<PERSON>go <PERSON>min (الأسهل)

#### 1. الوصول إلى Django Admin:
```
http://localhost:8000/admin/
```

#### 2. تسجيل الدخول:
- استخدم حساب المدير (superuser)

#### 3. الانتقال إلى أنواع الإجازات:
- اذهب إلى قسم **"LEAVES"**
- اضغط على **"Leave types"**

#### 4. إضافة نوع إجازة جديد:
- اضغط على **"Add Leave type"**
- املأ البيانات:
  ```
  Name: casual
  Description: إجازة عرضية للموظفين
  Max days per year: 7
  ```
- اضغط **"Save"**

### الطريقة الثانية: عبر صفحة أنواع الإجازات في النظام

#### 1. الوصول إلى صفحة أنواع الإجازات:
```
http://localhost:8000/leaves/types/
```

#### 2. إضافة نوع جديد:
- اضغط على **"إضافة نوع إجازة"**
- املأ البيانات:
  ```
  النوع: عرضية (casual)
  الوصف: إجازة عرضية للموظفين
  الحد الأقصى للأيام سنوياً: 7
  ```
- اضغط **"حفظ"**

### الطريقة الثالثة: عبر Migration (تلقائي)

#### 1. تشغيل Migration:
```bash
python manage.py migrate leaves 0005_add_casual_leave_type
```

#### 2. التحقق من النتيجة:
```bash
python manage.py shell -c "from leaves.models import LeaveType; print([lt.name for lt in LeaveType.objects.all()])"
```

## 📊 النتيجة المتوقعة

### في تقرير الإجازات:
| الرقم الوزاري | الموظف | إجازة سنوية | إجازة مرضية | **إجازة عرضية** | إجازة الحج |
|---------------|--------|-------------|-------------|----------------|-----------|
| | | الرصيد \| المستخدم \| المغادرات \| المتبقي | الرصيد \| المستخدم \| المتبقي | **الرصيد \| المستخدم \| المتبقي** | الرصيد \| المستخدم \| المتبقي |

### خصائص الإجازة العرضية:
- ✅ **الاسم**: casual
- ✅ **العرض**: عرضية
- ✅ **الحد الأقصى**: 7 أيام/سنة
- ✅ **الموقع**: بعد الإجازات المرضية مباشرة

## 🧪 التحقق من النجاح

### 1. فحص أنواع الإجازات:
```
http://localhost:8000/leaves/types/
```
يجب أن تظهر "عرضية" في القائمة

### 2. فحص تقرير الإجازات:
```
http://localhost:8000/leaves/reports/
```
يجب أن تظهر "إجازة عرضية" في الجدول

### 3. فحص ترتيب الأعمدة:
```
إجازة سنوية → إجازة مرضية → إجازة عرضية → إجازة الحج
```

## 🔄 إضافة أرصدة للإجازات العرضية

### بعد إضافة نوع الإجازة، قد تحتاج لإضافة أرصدة:

#### 1. عبر Django Admin:
- اذهب إلى **"Leave balances"**
- أضف رصيد لكل موظف:
  ```
  Employee: [اختر الموظف]
  Leave type: عرضية
  Year: 2024
  Initial balance: 7
  Used balance: 0
  ```

#### 2. عبر صفحة إدارة الأرصدة:
```
http://localhost:8000/leaves/balances/
```

## ⚠️ ملاحظات مهمة

### 1. الترتيب في الكود:
```python
# في leaves/views.py
preferred_order = ['annual', 'sick', 'casual', 'hajj', 'paternity', 'bereavement']
```

### 2. استثناء الأمومة:
```python
# في leaves/views.py
leave_types = LeaveType.objects.exclude(name__in=['unpaid', 'maternity'])
```

### 3. إذا لم تظهر الإجازات العرضية:
- تأكد من وجود نوع الإجازة في قاعدة البيانات
- تأكد من عدم استثنائها في الكود
- تأكد من وجود أرصدة للموظفين

## 🔧 استكشاف الأخطاء

### المشكلة: الإجازات العرضية لا تظهر
#### الحلول:
1. **تحقق من وجود النوع**:
   ```
   http://localhost:8000/admin/leaves/leavetype/
   ```

2. **تحقق من الكود**:
   ```python
   # في leaves/views.py - يجب ألا تكون casual مستثناة
   leave_types = LeaveType.objects.exclude(name__in=['unpaid', 'maternity'])
   ```

3. **تحقق من الأرصدة**:
   ```
   http://localhost:8000/admin/leaves/leavebalance/
   ```

### المشكلة: الترتيب خاطئ
#### الحل:
```python
# في leaves/views.py
preferred_order = ['annual', 'sick', 'casual', 'hajj', ...]
```

## الخلاصة

✅ **إضافة نوع الإجازة العرضية**
✅ **ترتيب الأعمدة: سنوية → مرضية → عرضية**
✅ **استثناء إجازات الأمومة**
✅ **إضافة أرصدة للموظفين (اختياري)**

**بعد تطبيق هذه الخطوات، ستظهر الإجازات العرضية في التقرير! 🎉**
