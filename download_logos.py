import os
import requests
from PIL import Image
from io import BytesIO

# Create the directory if it doesn't exist
os.makedirs('static/img/logos', exist_ok=True)

# List of logos to download
logos = [
    {
        'name': 'moe-logo.png',
        'url': 'https://moe.gov.jo/sites/default/files/logo.png',
        'fallback_url': 'https://moe.gov.jo/sites/default/files/logo_0.png'
    },
    {
        'name': 'emp-logo.png',
        'url': 'https://emp.moe.gov.jo/public/assets/images/logo.png',
        'fallback_url': 'https://emp.moe.gov.jo/public/assets/images/logo-ar.png'
    },
    {
        'name': 'teachers-logo.png',
        'url': 'https://teachers.gov.jo/assets/images/logo.png',
        'fallback_url': 'https://teachers.gov.jo/assets/images/logo-ar.png'
    },
    {
        'name': 'openemis-logo.png',
        'url': 'https://emis.moe.gov.jo/openemis-core/img/openemis_logo.png',
        'fallback_url': 'https://www.openemis.org/wp-content/uploads/2016/11/OpenEMIS_logo.png'
    },
    {
        'name': 'btecemis-logo.png',
        'url': 'https://apps.moe.gov.jo/btec/btecemis/public/assets/images/logo.png',
        'fallback_url': 'https://apps.moe.gov.jo/btec/btecemis/public/assets/images/logo-ar.png'
    }
]

# Download each logo
for logo in logos:
    try:
        print(f"Downloading {logo['name']}...")
        
        # Try the primary URL first
        response = requests.get(logo['url'], timeout=10)
        
        # If the primary URL fails, try the fallback URL
        if response.status_code != 200:
            print(f"Primary URL failed, trying fallback URL for {logo['name']}...")
            response = requests.get(logo['fallback_url'], timeout=10)
        
        if response.status_code == 200:
            # Save the image
            img = Image.open(BytesIO(response.content))
            img.save(f"static/img/logos/{logo['name']}")
            print(f"Successfully downloaded {logo['name']}")
        else:
            print(f"Failed to download {logo['name']}: HTTP status code {response.status_code}")
    
    except Exception as e:
        print(f"Error downloading {logo['name']}: {str(e)}")

print("Download complete!")
