{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الدورة - {{ course.name }} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-graduation-cap me-2"></i>تفاصيل الدورة</h2>
    <div>
        <a href="{% url 'ranks:course_update' course.pk %}" class="btn btn-warning me-2">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'ranks:course_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة لقائمة الدورات
        </a>
    </div>
</div>

<!-- Course Details Card -->
<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3 bg-primary text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدورة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-graduation-cap me-1 text-primary"></i>
                                اسم الدورة
                            </label>
                            <div class="p-3 bg-light rounded border">
                                <h5 class="text-primary mb-0">{{ course.name }}</h5>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-clock me-1 text-info"></i>
                                عدد الساعات
                            </label>
                            <div class="p-3 bg-light rounded border">
                                {% if course.hours %}
                                    <span class="badge bg-info fs-6">{{ course.hours }} ساعة</span>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar me-1 text-success"></i>
                                تاريخ الإضافة
                            </label>
                            <div class="p-3 bg-light rounded border">
                                <span class="badge bg-success">{{ course.created_at|date:'d/m/Y' }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-12 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-align-left me-1 text-secondary"></i>
                                الوصف
                            </label>
                            <div class="p-3 bg-light rounded border">
                                {% if course.description %}
                                    <p class="mb-0">{{ course.description|linebreaks }}</p>
                                {% else %}
                                    <span class="text-muted">لا يوجد وصف</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Card -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header py-3 bg-info text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات الدورة
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-users fa-3x text-primary mb-2"></i>
                        <h4 class="text-primary">{{ course.employeecourse_set.count }}</h4>
                        <p class="text-muted mb-0">موظف مسجل</p>
                    </div>
                    
                    {% if course.hours %}
                    <div class="mb-3">
                        <i class="fas fa-clock fa-2x text-info mb-2"></i>
                        <h5 class="text-info">{{ course.hours }}</h5>
                        <p class="text-muted mb-0">ساعة تدريبية</p>
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <i class="fas fa-calendar-plus fa-2x text-success mb-2"></i>
                        <h6 class="text-success">{{ course.created_at|date:'d/m/Y' }}</h6>
                        <p class="text-muted mb-0">تاريخ الإضافة</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions Card -->
        <div class="card shadow mt-4">
            <div class="card-header py-3 bg-warning text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'ranks:course_update' course.pk %}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>تعديل الدورة
                    </a>
                    <a href="{% url 'ranks:employee_course_create' %}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>إضافة موظف للدورة
                    </a>
                    <a href="{% url 'ranks:employee_course_list' %}" class="btn btn-info">
                        <i class="fas fa-list me-2"></i>عرض الموظفين المسجلين
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enrolled Employees Section -->
{% if course.employeecourse_set.exists %}
<div class="card shadow mt-4">
    <div class="card-header py-3 bg-success text-white">
        <h6 class="m-0 font-weight-bold">
            <i class="fas fa-users me-2"></i>
            الموظفون المسجلون في الدورة
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>اسم الموظف</th>
                        <th>الرقم الوزاري</th>
                        <th>تاريخ الإكمال</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee_course in course.employeecourse_set.all|slice:":10" %}
                    <tr>
                        <td>{{ employee_course.employee.full_name }}</td>
                        <td>{{ employee_course.employee.ministry_number }}</td>
                        <td>
                            {% if employee_course.completion_date %}
                                {{ employee_course.completion_date|date:'d/m/Y' }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'ranks:employee_course_update' employee_course.pk %}" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        {% if course.employeecourse_set.count > 10 %}
        <div class="text-center mt-3">
            <a href="{% url 'ranks:employee_course_list' %}?course={{ course.pk }}" class="btn btn-outline-primary">
                <i class="fas fa-eye me-2"></i>عرض جميع الموظفين ({{ course.employeecourse_set.count }})
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        border-radius: 10px;
    }
    
    .card-header {
        border-radius: 10px 10px 0 0 !important;
    }
    
    .bg-light {
        background-color: #f8f9fa !important;
    }
    
    .badge {
        font-size: 0.9em;
    }
    
    .table th {
        background-color: #343a40;
        color: white;
        border: none;
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .btn {
        border-radius: 5px;
    }
</style>
{% endblock %}
