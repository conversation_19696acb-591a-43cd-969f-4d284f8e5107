{% extends 'base.html' %}
{% load static %}

{% block title %}{% if balance %}تعديل رصيد إجازة{% else %}إضافة رصيد إجازة جديد{% endif %} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% if balance %}تعديل رصيد إجازة{% else %}إضافة رصيد إجازة جديد{% endif %}</h2>
    <a href="{% url 'leaves:leave_balance_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة لأرصدة الإجازات
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">بيانات رصيد الإجازة</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <input type="hidden" name="employee" id="id_employee">
            <input type="hidden" name="employee_id" id="id_employee_id">
            <input type="hidden" name="used_balance" id="id_used_balance">

            <div class="mb-3">
                <label for="{{ form.ministry_number.id_for_label }}" class="form-label">الرقم الوزاري</label>
                <div class="input-group">
                    {{ form.ministry_number }}
                    <button class="btn btn-primary" type="button" id="search_employee_btn">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
                {% if form.ministry_number.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.ministry_number.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.employee_name.id_for_label }}" class="form-label">اسم الموظف</label>
                {{ form.employee_name }}
                {% if form.employee_name.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.employee_name.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.leave_type.id_for_label }}" class="form-label">نوع الإجازة</label>
                {{ form.leave_type }}
                {% if form.leave_type.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.leave_type.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.year.id_for_label }}" class="form-label">السنة</label>
                {{ form.year }}
                {% if form.year.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.year.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.initial_balance.id_for_label }}" class="form-label">الرصيد الأولي</label>
                {{ form.initial_balance }}
                {% if form.initial_balance.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.initial_balance.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.remaining_balance.id_for_label }}" class="form-label">الرصيد المتبقي</label>
                {{ form.remaining_balance }}
                <div class="form-text">يتم حساب الرصيد المتبقي تلقائياً بناءً على الرصيد الأولي ومجموع الإجازات المستخدمة</div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form fields
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        // Employee search functionality
        const ministryNumberInput = document.getElementById('ministry_number_input');
        const employeeNameDisplay = document.getElementById('employee_name_display');
        const employeeIdInput = document.getElementById('id_employee_id');
        const searchButton = document.getElementById('search_employee_btn');
        const initialBalanceInput = document.getElementById('id_initial_balance');
        const remainingBalanceDisplay = document.getElementById('remaining_balance_display');
        const leaveTypeSelect = document.getElementById('id_leave_type');
        const yearInput = document.getElementById('id_year');

        // Function to search for employee
        function searchEmployee() {
            const ministryNumber = ministryNumberInput.value.trim();
            if (!ministryNumber) {
                alert('الرجاء إدخال الرقم الوزاري');
                return;
            }

            // Show loading indicator
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            searchButton.disabled = true;

            // Make AJAX request
            fetch(`/leaves/get-employee/?ministry_number=${ministryNumber}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        if (response.status === 403) {
                            // Redirect to login page if not authenticated
                            window.location.href = '/accounts/login/?next=' + encodeURIComponent(window.location.pathname);
                            throw new Error('يجب تسجيل الدخول للمتابعة');
                        }
                        return response.json().then(data => {
                            throw new Error(data.error || 'حدث خطأ أثناء البحث');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);

                    // Check if data is successful and employee data exists
                    if (!data.success) {
                        throw new Error(data.error || 'بيانات الموظف غير متوفرة');
                    }

                    if (!data.employee) {
                        throw new Error('بيانات الموظف غير متوفرة');
                    }

                    // Update form fields
                    employeeNameDisplay.value = data.employee.full_name;
                    employeeIdInput.value = data.employee.id;

                    // Update select field (hidden)
                    const selectElement = document.getElementById('id_employee');
                    if (selectElement && selectElement.options) {
                        // Check if option exists
                        let optionExists = false;
                        for (let i = 0; i < selectElement.options.length; i++) {
                            if (selectElement.options[i].value == data.employee.id) {
                                selectElement.options[i].selected = true;
                                optionExists = true;
                                break;
                            }
                        }

                        // If option doesn't exist, create it
                        if (!optionExists) {
                            const newOption = new Option(data.employee.full_name, data.employee.id, true, true);
                            selectElement.appendChild(newOption);
                        }
                    } else if (selectElement) {
                        // If options is undefined but selectElement exists, create a new option
                        const newOption = new Option(data.employee.full_name, data.employee.id, true, true);
                        selectElement.appendChild(newOption);
                    }

                    // Calculate remaining balance if all required fields are filled
                    calculateRemainingBalance();
                })
                .catch(error => {
                    alert(error.message);
                    employeeNameDisplay.value = '';
                    employeeIdInput.value = '';
                })
                .finally(() => {
                    // Reset button
                    searchButton.innerHTML = '<i class="fas fa-search"></i> بحث';
                    searchButton.disabled = false;
                });
        }

        // Function to calculate remaining balance
        function calculateRemainingBalance() {
            const employeeId = employeeIdInput.value;
            const leaveTypeId = leaveTypeSelect.value;
            const year = yearInput.value;
            const initialBalance = initialBalanceInput.value;
            const usedBalanceInput = document.getElementById('id_used_balance');

            if (employeeId && leaveTypeId && year && initialBalance) {
                // Make AJAX request to get used leave days
                fetch(`/leaves/get-used-leave-days/?employee_id=${employeeId}&leave_type_id=${leaveTypeId}&year=${year}`)
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(data => {
                                throw new Error(data.error || 'حدث خطأ أثناء حساب الرصيد المتبقي');
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            const usedDays = data.used_days || 0;
                            const remaining = initialBalance - usedDays;

                            // Update used balance hidden field
                            if (usedBalanceInput) {
                                usedBalanceInput.value = usedDays;
                                console.log('Updated used balance:', usedDays);
                            }

                            // Update remaining balance display
                            remainingBalanceDisplay.value = remaining >= 0 ? remaining : 0;
                        }
                    })
                    .catch(error => {
                        console.error('Error calculating remaining balance:', error);
                        remainingBalanceDisplay.value = initialBalance; // Default to initial balance if calculation fails

                        // Set used balance to 0 on error
                        if (usedBalanceInput) {
                            usedBalanceInput.value = 0;
                        }
                    });
            } else {
                remainingBalanceDisplay.value = initialBalance || 0; // Default to initial balance

                // Set used balance to 0 if not all required fields are filled
                if (usedBalanceInput) {
                    usedBalanceInput.value = 0;
                }
            }
        }

        // Function to validate annual leave balance
        function validateAnnualLeaveBalance() {
            const leaveTypeName = leaveTypeSelect.options[leaveTypeSelect.selectedIndex]?.text || '';
            const initialBalance = parseInt(initialBalanceInput.value);

            // Remove any existing validation messages
            const existingAlert = document.querySelector('.annual-leave-validation-alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            // Remove validation classes
            initialBalanceInput.classList.remove('is-invalid', 'is-valid');

            if (initialBalance && (leaveTypeName.includes('سنوية') || leaveTypeName.toLowerCase().includes('annual'))) {
                if (initialBalance > 60) {
                    // Add validation error
                    initialBalanceInput.classList.add('is-invalid');

                    // Create and show error message
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger mt-2 annual-leave-validation-alert';
                    alertDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> الرصيد الأولي للإجازات السنوية لا يمكن أن يزيد عن 60 يوم';

                    // Insert after the initial balance input
                    initialBalanceInput.parentNode.appendChild(alertDiv);
                } else {
                    // Add validation success
                    initialBalanceInput.classList.add('is-valid');
                }
            }
        }

        // Add form submission handler
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            console.log('Form submitted');

            // Check if employee is selected
            if (!employeeIdInput.value) {
                e.preventDefault();
                alert('الرجاء اختيار موظف أولاً');
                return false;
            }

            // Check if leave type is selected
            if (!leaveTypeSelect.value) {
                e.preventDefault();
                alert('الرجاء اختيار نوع الإجازة');
                return false;
            }

            // Check if year is entered
            if (!yearInput.value) {
                e.preventDefault();
                alert('الرجاء إدخال السنة');
                return false;
            }

            // Check if initial balance is entered
            if (!initialBalanceInput.value) {
                e.preventDefault();
                alert('الرجاء إدخال الرصيد الأولي');
                return false;
            }

            // Check if initial balance for annual leave exceeds 60 days
            const leaveTypeName = leaveTypeSelect.options[leaveTypeSelect.selectedIndex]?.text || '';
            const initialBalance = parseInt(initialBalanceInput.value);

            if (leaveTypeName.includes('سنوية') || leaveTypeName.toLowerCase().includes('annual')) {
                if (initialBalance > 60) {
                    e.preventDefault();
                    alert('الرصيد الأولي للإجازات السنوية لا يمكن أن يزيد عن 60 يوم');
                    initialBalanceInput.focus();
                    return false;
                }
            }

            // Make sure the employee ID is set in the form
            const selectElement = document.getElementById('id_employee');
            if (selectElement && employeeIdInput.value) {
                selectElement.value = employeeIdInput.value;
            }

            return true;
        });

        // Add event listeners
        if (searchButton) {
            searchButton.addEventListener('click', searchEmployee);
        }

        // Allow searching by pressing Enter in the ministry number field
        if (ministryNumberInput) {
            ministryNumberInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent form submission
                    searchEmployee();
                }
            });
        }

        // Recalculate remaining balance when initial balance, leave type or year changes
        if (initialBalanceInput) {
            initialBalanceInput.addEventListener('change', function() {
                calculateRemainingBalance();
                validateAnnualLeaveBalance();
            });

            // Add real-time validation on input
            initialBalanceInput.addEventListener('input', validateAnnualLeaveBalance);
        }

        if (leaveTypeSelect) {
            leaveTypeSelect.addEventListener('change', function() {
                calculateRemainingBalance();
                validateAnnualLeaveBalance();
            });
        }

        if (yearInput) {
            yearInput.addEventListener('change', calculateRemainingBalance);
        }

        // If we have a pre-filled ministry number (edit mode), trigger search
        if (ministryNumberInput && ministryNumberInput.value && employeeNameDisplay && !employeeNameDisplay.value) {
            searchEmployee();
        }
    });
</script>
{% endblock %}
