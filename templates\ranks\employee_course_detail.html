{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل دورة الموظف - {{ employee_course.employee.full_name }} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-graduate me-2"></i>تفاصيل دورة الموظف</h2>
    <div>
        <a href="{% url 'ranks:employee_course_update' employee_course.pk %}" class="btn btn-warning me-2">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'ranks:employee_course_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>
</div>

<!-- Employee Course Details -->
<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3 bg-primary text-white">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدورة والموظف
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Employee Information -->
                    <div class="col-md-6 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user me-1 text-primary"></i>
                                اسم الموظف
                            </label>
                            <div class="p-3 bg-light rounded border">
                                <h5 class="text-primary mb-0">{{ employee_course.employee.full_name }}</h5>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-id-card me-1 text-info"></i>
                                الرقم الوزاري
                            </label>
                            <div class="p-3 bg-light rounded border">
                                <span class="badge bg-info fs-6">{{ employee_course.employee.ministry_number }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Course Information -->
                    <div class="col-md-12 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-graduation-cap me-1 text-success"></i>
                                اسم الدورة
                            </label>
                            <div class="p-3 bg-light rounded border">
                                <h5 class="text-success mb-0">{{ employee_course.course.name }}</h5>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-clock me-1 text-warning"></i>
                                عدد الساعات
                            </label>
                            <div class="p-3 bg-light rounded border">
                                {% if employee_course.hours %}
                                    <span class="badge bg-warning fs-6">{{ employee_course.hours }} ساعة</span>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar me-1 text-success"></i>
                                تاريخ الإكمال
                            </label>
                            <div class="p-3 bg-light rounded border">
                                {% if employee_course.completion_date %}
                                    <span class="badge bg-success">{{ employee_course.completion_date|date:'d/m/Y' }}</span>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-certificate me-1 text-danger"></i>
                                رقم الشهادة
                            </label>
                            <div class="p-3 bg-light rounded border">
                                {% if employee_course.certificate_number %}
                                    <span class="badge bg-danger">{{ employee_course.certificate_number }}</span>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-plus me-1 text-secondary"></i>
                                تاريخ الإضافة
                            </label>
                            <div class="p-3 bg-light rounded border">
                                <span class="badge bg-secondary">{{ employee_course.created_at|date:'d/m/Y' }}</span>
                            </div>
                        </div>
                    </div>
                    
                    {% if employee_course.notes %}
                    <div class="col-md-12 mb-4">
                        <div class="form-group">
                            <label class="form-label fw-bold">
                                <i class="fas fa-sticky-note me-1 text-dark"></i>
                                ملاحظات
                            </label>
                            <div class="p-3 bg-light rounded border">
                                <p class="mb-0">{{ employee_course.notes|linebreaks }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics and Actions Card -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header py-3 bg-info text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-chart-bar me-2"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    {% if employee_course.hours %}
                    <div class="mb-3">
                        <i class="fas fa-clock fa-3x text-warning mb-2"></i>
                        <h4 class="text-warning">{{ employee_course.hours }}</h4>
                        <p class="text-muted mb-0">ساعة تدريبية</p>
                    </div>
                    {% endif %}
                    
                    {% if employee_course.completion_date %}
                    <div class="mb-3">
                        <i class="fas fa-calendar-check fa-2x text-success mb-2"></i>
                        <h6 class="text-success">{{ employee_course.completion_date|date:'d/m/Y' }}</h6>
                        <p class="text-muted mb-0">تاريخ الإكمال</p>
                    </div>
                    {% endif %}
                    
                    {% if employee_course.certificate_number %}
                    <div class="mb-3">
                        <i class="fas fa-certificate fa-2x text-danger mb-2"></i>
                        <h6 class="text-danger">{{ employee_course.certificate_number }}</h6>
                        <p class="text-muted mb-0">رقم الشهادة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Quick Actions Card -->
        <div class="card shadow mt-4">
            <div class="card-header py-3 bg-warning text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'ranks:employee_course_update' employee_course.pk %}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>تعديل البيانات
                    </a>
                    <a href="{% url 'ranks:course_detail' employee_course.course.pk %}" class="btn btn-info">
                        <i class="fas fa-graduation-cap me-2"></i>تفاصيل الدورة
                    </a>
                    <a href="{% url 'ranks:employee_course_create' %}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>إضافة دورة جديدة
                    </a>
                    <a href="{% url 'ranks:employee_course_list' %}" class="btn btn-secondary">
                        <i class="fas fa-list me-2"></i>عرض جميع الدورات
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Employee Other Courses -->
        {% if employee_course.employee.employeecourse_set.count > 1 %}
        <div class="card shadow mt-4">
            <div class="card-header py-3 bg-success text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-list me-2"></i>
                    دورات أخرى للموظف
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for other_course in employee_course.employee.employeecourse_set.all %}
                        {% if other_course.pk != employee_course.pk %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ other_course.course.name }}</h6>
                                <small class="text-muted">
                                    {% if other_course.completion_date %}
                                        {{ other_course.completion_date|date:'d/m/Y' }}
                                    {% else %}
                                        غير مكتمل
                                    {% endif %}
                                </small>
                            </div>
                            <a href="{% url 'ranks:employee_course_detail' other_course.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        border-radius: 10px;
    }
    
    .card-header {
        border-radius: 10px 10px 0 0 !important;
    }
    
    .bg-light {
        background-color: #f8f9fa !important;
    }
    
    .badge {
        font-size: 0.9em;
    }
    
    .btn {
        border-radius: 5px;
    }
    
    .list-group-item {
        border: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .list-group-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}
