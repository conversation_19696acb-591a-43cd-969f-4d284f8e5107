#!/usr/bin/env python3
"""
تحليل شامل لنظام إدارة شؤون الموظفين
System Analysis for HR Management System
"""

import os
import json
from pathlib import Path
from datetime import datetime

def count_lines_in_file(file_path):
    """Count lines in a file"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return len(f.readlines())
    except:
        return 0

def analyze_system():
    """Analyze the HR system structure and statistics"""
    
    # Base directory
    base_dir = Path('.')
    
    # File extensions to analyze
    extensions = {
        'python': ['.py'],
        'html': ['.html'],
        'css': ['.css'],
        'javascript': ['.js'],
        'markdown': ['.md'],
        'text': ['.txt'],
        'json': ['.json'],
        'sql': ['.sql']
    }
    
    # Directories to exclude
    exclude_dirs = {'venv', '__pycache__', '.git', 'staticfiles', 'media'}
    
    # Initialize statistics
    stats = {
        'total_files': 0,
        'total_lines': 0,
        'by_type': {},
        'by_app': {},
        'apps': [],
        'templates': 0,
        'static_files': 0
    }
    
    # Initialize file type stats
    for file_type in extensions:
        stats['by_type'][file_type] = {'files': 0, 'lines': 0}
    
    # Django apps
    django_apps = []
    
    # Scan all files
    for root, dirs, files in os.walk(base_dir):
        # Remove excluded directories
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        root_path = Path(root)
        
        # Check if this is a Django app
        if (root_path / 'models.py').exists() and (root_path / 'views.py').exists():
            app_name = root_path.name
            if app_name not in django_apps and app_name != '.':
                django_apps.append(app_name)
                stats['by_app'][app_name] = {'files': 0, 'lines': 0}
        
        for file in files:
            file_path = root_path / file
            file_ext = file_path.suffix.lower()
            
            # Count total files
            stats['total_files'] += 1
            
            # Count lines
            lines = count_lines_in_file(file_path)
            stats['total_lines'] += lines
            
            # Categorize by file type
            for file_type, exts in extensions.items():
                if file_ext in exts:
                    stats['by_type'][file_type]['files'] += 1
                    stats['by_type'][file_type]['lines'] += lines
                    break
            
            # Count templates
            if 'templates' in str(file_path) and file_ext == '.html':
                stats['templates'] += 1
            
            # Count static files
            if 'static' in str(file_path) and file_ext in ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif']:
                stats['static_files'] += 1
            
            # Categorize by Django app
            for app in django_apps:
                if str(file_path).startswith(app + os.sep) or str(file_path).startswith(f'.{os.sep}{app}{os.sep}'):
                    if app in stats['by_app']:
                        stats['by_app'][app]['files'] += 1
                        stats['by_app'][app]['lines'] += lines
                    break
    
    stats['apps'] = django_apps
    return stats

def generate_report():
    """Generate comprehensive system report"""
    
    print("=" * 80)
    print("تقرير شامل عن نظام إدارة شؤون الموظفين")
    print("HR Management System Comprehensive Report")
    print("=" * 80)
    print(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Analyze system
    stats = analyze_system()
    
    # 1. إحصائيات عامة
    print("\n📊 الإحصائيات العامة / General Statistics")
    print("-" * 50)
    print(f"إجمالي الملفات: {stats['total_files']:,}")
    print(f"إجمالي الأسطر البرمجية: {stats['total_lines']:,}")
    print(f"عدد التطبيقات (Django Apps): {len(stats['apps'])}")
    print(f"عدد القوالب (Templates): {stats['templates']}")
    print(f"عدد الملفات الثابتة (Static Files): {stats['static_files']}")
    
    # 2. تفصيل حسب نوع الملف
    print("\n📁 تفصيل حسب نوع الملف / Files by Type")
    print("-" * 50)
    for file_type, data in stats['by_type'].items():
        if data['files'] > 0:
            print(f"{file_type.upper():12}: {data['files']:4} ملف، {data['lines']:6,} سطر")
    
    # 3. تطبيقات Django
    print("\n🔧 تطبيقات Django / Django Applications")
    print("-" * 50)
    for i, app in enumerate(stats['apps'], 1):
        app_stats = stats['by_app'].get(app, {'files': 0, 'lines': 0})
        print(f"{i:2}. {app:20}: {app_stats['files']:3} ملف، {app_stats['lines']:5,} سطر")
    
    # 4. معلومات قاعدة البيانات
    print("\n🗄️ قاعدة البيانات / Database Information")
    print("-" * 50)
    print("نوع قاعدة البيانات: SQLite3 (افتراضي) / MySQL (اختياري)")
    print("ملف قاعدة البيانات: db.sqlite3")
    print("دعم MySQL: متوفر مع إعدادات منفصلة")
    
    # 5. التقنيات المستخدمة
    print("\n💻 التقنيات البرمجية المستخدمة / Technologies Used")
    print("-" * 50)
    print("• إطار العمل الخلفي: Django 5.2")
    print("• لغة البرمجة: Python 3.13")
    print("• قاعدة البيانات: SQLite3 / MySQL")
    print("• واجهة المستخدم: Bootstrap 5")
    print("• JavaScript: Vanilla JS + jQuery")
    print("• الأيقونات: Font Awesome")
    print("• التقارير: ReportLab (PDF)")
    print("• التصدير: Pandas + Excel")
    print("• المصادقة: Django Auth")
    print("• الصلاحيات: Django Permissions")
    
    # 6. الميزات الرئيسية
    print("\n🌟 الميزات الرئيسية / Key Features")
    print("-" * 50)
    print("• إدارة بيانات الموظفين")
    print("• نظام الإجازات والمغادرات")
    print("• إدارة الرتب والعلاوات والدورات")
    print("• التقارير والإحصائيات")
    print("• النقل الداخلي والخارجي")
    print("• إدارة الملفات والوثائق")
    print("• نظام الإشعارات")
    print("• النسخ الاحتياطي")
    print("• إدارة المستخدمين والصلاحيات")
    print("• التدقيق وسجلات النظام")
    
    # 7. نوع الواجهة البرمجية
    print("\n🔌 نوع الواجهة البرمجية / API Type")
    print("-" * 50)
    print("• نوع التطبيق: Web Application")
    print("• النمط المعماري: MVC (Model-View-Controller)")
    print("• واجهات API: RESTful endpoints")
    print("• تنسيق البيانات: JSON")
    print("• المصادقة: Session-based")
    print("• الأمان: CSRF Protection + Django Security")
    
    # 8. بنية المشروع
    print("\n📂 بنية المشروع / Project Structure")
    print("-" * 50)
    print("hr_system/          # إعدادات المشروع الرئيسية")
    print("├── accounts/        # إدارة المستخدمين والمصادقة")
    print("├── employees/       # إدارة بيانات الموظفين")
    print("├── employment/      # إدارة التوظيف والمناصب")
    print("├── leaves/          # نظام الإجازات والمغادرات")
    print("├── ranks/           # الرتب والعلاوات والدورات")
    print("├── reports/         # التقارير والإحصائيات")
    print("├── notifications/   # نظام الإشعارات")
    print("├── backup/          # النسخ الاحتياطي")
    print("├── system_logs/     # سجلات النظام")
    print("├── templates/       # قوالب HTML")
    print("└── static/          # الملفات الثابتة")
    
    print("\n" + "=" * 80)
    print("انتهى التقرير / End of Report")
    print("=" * 80)
    
    return stats

if __name__ == "__main__":
    generate_report()
