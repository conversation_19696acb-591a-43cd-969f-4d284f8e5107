#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت النسخ الاحتياطي لقاعدة البيانات
Database Backup Script
"""

import os
import sys
import django
from django.core.management import call_command
from django.conf import settings
import json

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        print("بدء عملية النسخ الاحتياطي...")
        
        # تصدير البيانات
        with open('backup_data.json', 'w', encoding='utf-8') as f:
            call_command(
                'dumpdata',
                '--natural-foreign',
                '--natural-primary',
                '--exclude=contenttypes',
                '--exclude=auth.Permission',
                '--indent=4',
                stdout=f
            )
        
        print("تم إنشاء النسخة الاحتياطية بنجاح في ملف backup_data.json")
        
        # التحقق من حجم الملف
        file_size = os.path.getsize('backup_data.json')
        print(f"حجم ملف النسخة الاحتياطية: {file_size} بايت")
        
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return False

if __name__ == "__main__":
    success = backup_database()
    if success:
        print("تمت عملية النسخ الاحتياطي بنجاح!")
    else:
        print("فشلت عملية النسخ الاحتياطي!")
        sys.exit(1)
