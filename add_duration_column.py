#!/usr/bin/env python
"""
Script to add duration_days column to leaves_departure table
"""

import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

from django.db import connection

def add_duration_column():
    """Add duration_days column to leaves_departure table"""
    with connection.cursor() as cursor:
        try:
            # Check if column already exists
            cursor.execute("PRAGMA table_info(leaves_departure)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'duration_days' not in columns:
                print("Adding duration_days column...")
                cursor.execute("""
                    ALTER TABLE leaves_departure 
                    ADD COLUMN duration_days DECIMAL(5,2) NULL
                """)
                print("Column added successfully!")
                
                # Update existing records with calculated duration
                print("Updating existing records...")
                cursor.execute("""
                    UPDATE leaves_departure 
                    SET duration_days = ROUND(
                        CASE 
                            WHEN time_to >= time_from THEN
                                (strftime('%H', time_to) * 60 + strftime('%M', time_to) - 
                                 strftime('%H', time_from) * 60 - strftime('%M', time_from)) / 420.0
                            ELSE
                                ((strftime('%H', time_to) + 24) * 60 + strftime('%M', time_to) - 
                                 strftime('%H', time_from) * 60 - strftime('%M', time_from)) / 420.0
                        END, 2
                    )
                    WHERE time_from IS NOT NULL AND time_to IS NOT NULL
                """)
                
                updated_rows = cursor.rowcount
                print(f"Updated {updated_rows} existing records with calculated duration.")
                
            else:
                print("Column duration_days already exists!")
                
        except Exception as e:
            print(f"Error: {e}")
            return False
            
    return True

if __name__ == "__main__":
    print("Starting database update...")
    success = add_duration_column()
    if success:
        print("Database update completed successfully!")
    else:
        print("Database update failed!")
