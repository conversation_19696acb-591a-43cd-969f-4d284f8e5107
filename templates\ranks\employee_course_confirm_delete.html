{% extends 'base.html' %}
{% load static %}

{% block title %}حذف دورة الموظف - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>حذف دورة الموظف</h2>
    <a href="{% url 'ranks:employee_course_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للدورات
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 bg-danger text-white">
        <h6 class="m-0 font-weight-bold">
            <i class="fas fa-exclamation-triangle me-2"></i>
            تأكيد حذف دورة الموظف
        </h6>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle"></i> تحذير!</h5>
            <p>هل أنت متأكد من رغبتك في حذف دورة الموظف التالية؟</p>
            
            <div class="row">
                <div class="col-md-6">
                    <ul>
                        <li><strong>اسم الموظف:</strong>
                            <span class="text-primary">{{ employee_course.employee.full_name }}</span>
                        </li>
                        <li><strong>الرقم الوزاري:</strong>
                            <span class="badge bg-info">{{ employee_course.employee.ministry_number }}</span>
                        </li>
                        <li><strong>اسم الدورة:</strong>
                            <span class="text-success">{{ employee_course.course.name }}</span>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul>
                        <li><strong>عدد الساعات:</strong>
                            <span class="badge bg-secondary">{{ employee_course.hours }} ساعة</span>
                        </li>
                        <li><strong>تاريخ الحصول:</strong>
                            <span class="badge bg-dark">{{ employee_course.completion_date|date:"d/m/Y" }}</span>
                        </li>
                        <li><strong>رقم الشهادة:</strong>
                            <span class="text-muted">{{ employee_course.certificate_number|default:"غير محدد" }}</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            {% if employee_course.notes %}
            <div class="mt-3">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i>ملاحظات</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">{{ employee_course.notes }}</p>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Additional Information -->
            <div class="mt-3">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات إضافية</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-calendar-plus me-1"></i>
                                    <strong>تاريخ الإضافة:</strong> {{ employee_course.created_at|date:"d/m/Y - H:i" }}
                                </small>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-calendar-edit me-1"></i>
                                    <strong>آخر تحديث:</strong> {{ employee_course.updated_at|date:"d/m/Y - H:i" }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
            <a href="{% url 'ranks:employee_course_list' %}" class="btn btn-secondary btn-lg me-3">
                <i class="fas fa-arrow-left me-2"></i> العودة للقائمة
            </a>
            <form method="post" style="display: inline;" onsubmit="return confirmDelete()">
                {% csrf_token %}
                <button type="submit" class="btn btn-danger btn-lg">
                    <i class="fas fa-trash-alt me-2"></i> تأكيد الحذف نهائياً
                </button>
            </form>
        </div>

        <script>
        function confirmDelete() {
            return confirm('هل أنت متأكد من رغبتك في حذف هذه الدورة نهائياً؟\n\nلا يمكن التراجع عن هذا الإجراء!');
        }
        </script>
    </div>
</div>
{% endblock %}
