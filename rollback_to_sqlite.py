#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريپت العودة إلى SQLite
Rollback to SQLite Script
"""

import os
import sys
import shutil
from pathlib import Path

def rollback_to_sqlite():
    """العودة إلى إعدادات SQLite"""
    print("بدء عملية العودة إلى SQLite...")
    print("=" * 50)
    
    try:
        # التحقق من وجود النسخة الاحتياطية من الإعدادات
        backup_settings = Path("hr_system/settings_sqlite_backup.py")
        current_settings = Path("hr_system/settings.py")
        
        if backup_settings.exists():
            print("✓ تم العثور على نسخة احتياطية من إعدادات SQLite")
            
            # إنشاء نسخة احتياطية من إعدادات MySQL الحالية
            mysql_backup = Path("hr_system/settings_mysql_backup.py")
            shutil.copy2(current_settings, mysql_backup)
            print("✓ تم حفظ إعدادات MySQL الحالية")
            
            # استعادة إعدادات SQLite
            shutil.copy2(backup_settings, current_settings)
            print("✓ تم استعادة إعدادات SQLite")
            
        else:
            print("! لم يتم العثور على نسخة احتياطية من إعدادات SQLite")
            print("سيتم إنشاء إعدادات SQLite يدوياً...")
            
            # قراءة الملف الحالي
            with open(current_settings, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # استبدال إعدادات MySQL بـ SQLite
            mysql_config = """# إعدادات قاعدة بيانات MySQL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'hr_system_db',
        'USER': 'hr_user',
        'PASSWORD': 'hr_password_2024',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'autocommit': True,
        },
    }
}

# إعدادات SQLite الاحتياطية (للرجوع إليها عند الحاجة)
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR / 'db.sqlite3',
#     }
# }"""
            
            sqlite_config = """# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# إعدادات MySQL المحفوظة (للرجوع إليها عند الحاجة)
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'hr_system_db',
#         'USER': 'hr_user',
#         'PASSWORD': 'hr_password_2024',
#         'HOST': 'localhost',
#         'PORT': '3306',
#         'OPTIONS': {
#             'charset': 'utf8mb4',
#             'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
#             'autocommit': True,
#         },
#     }
# }"""
            
            # استبدال الإعدادات
            new_content = content.replace(mysql_config, sqlite_config)
            
            # كتابة الملف المحدث
            with open(current_settings, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✓ تم تحديث إعدادات قاعدة البيانات إلى SQLite")
        
        # التحقق من وجود ملف SQLite
        sqlite_file = Path("db.sqlite3")
        if sqlite_file.exists():
            print("✓ تم العثور على ملف قاعدة بيانات SQLite الأصلي")
        else:
            print("! لم يتم العثور على ملف SQLite الأصلي")
            print("سيتم إنشاء قاعدة بيانات جديدة عند تشغيل migrate")
        
        print("\n" + "=" * 50)
        print("✓ تمت عملية العودة إلى SQLite بنجاح!")
        print("=" * 50)
        
        print("\nالخطوات التالية:")
        print("1. قم بتشغيل: python manage.py migrate")
        print("2. إذا كنت تريد استعادة البيانات من النسخة الاحتياطية:")
        print("   python manage.py loaddata backup_data.json")
        print("3. قم بإنشاء مستخدم إداري جديد:")
        print("   python manage.py createsuperuser")
        print("4. اختبر النظام: python manage.py runserver")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في عملية العودة: {str(e)}")
        return False

def create_rollback_batch():
    """إنشاء ملف batch للعودة السريعة"""
    batch_content = """@echo off
chcp 65001 > nul
echo ========================================
echo العودة إلى SQLite
echo Rollback to SQLite
echo ========================================
echo.

echo تحذير: هذه العملية ستعيد النظام إلى استخدام SQLite
echo وستفقد الاتصال بقاعدة بيانات MySQL
echo.
set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i not "%confirm%"=="y" (
    echo تم إلغاء العملية
    pause
    exit /b 0
)

echo.
echo تنفيذ عملية العودة...
venv\\Scripts\\python.exe rollback_to_sqlite.py
if errorlevel 1 (
    echo فشلت عملية العودة
    pause
    exit /b 1
)

echo.
echo تطبيق المخططات على SQLite...
venv\\Scripts\\python.exe manage.py migrate
if errorlevel 1 (
    echo فشل في تطبيق المخططات
    pause
    exit /b 1
)

echo.
echo ========================================
echo تمت عملية العودة بنجاح!
echo ========================================
echo.
echo يمكنك الآن تشغيل النظام باستخدام:
echo python manage.py runserver
echo.
pause"""
    
    try:
        with open("rollback_to_sqlite.bat", "w", encoding="utf-8") as f:
            f.write(batch_content)
        print("✓ تم إنشاء ملف rollback_to_sqlite.bat")
        return True
    except Exception as e:
        print(f"✗ خطأ في إنشاء ملف batch: {str(e)}")
        return False

if __name__ == "__main__":
    print("سكريپت العودة إلى SQLite")
    print("=" * 30)
    
    success = rollback_to_sqlite()
    
    if success:
        create_rollback_batch()
        print("\nتمت عملية العودة بنجاح!")
    else:
        print("\nفشلت عملية العودة!")
        sys.exit(1)
