# ميزة حساب مدة المغادرة - النسخة النهائية
# Duration Calculation Feature - Final Version

## ✅ الميزة المكتملة

### 🎯 الهدف المحقق:
إضافة حقل المدة لحساب مدة المغادرة بالأيام باستخدام المعادلة:
**كل 420 دقيقة = يوم واحد**

## 🔧 التنفيذ النهائي

### 1. حساب المدة في النموذج

#### **دالة الحساب:**
```python
# في leaves/models.py
def calculate_duration_days(self):
    """حساب مدة المغادرة بالأيام (كل 420 دقيقة = يوم واحد)"""
    if self.time_from and self.time_to:
        # تحويل الأوقات إلى دقائق
        from_minutes = self.time_from.hour * 60 + self.time_from.minute
        to_minutes = self.time_to.hour * 60 + self.time_to.minute
        
        # حساب الفرق بالدقائق
        duration_minutes = to_minutes - from_minutes
        
        # إذا كان الوقت النهائي أقل من البداية (عبور منتصف الليل)
        if duration_minutes < 0:
            duration_minutes += 24 * 60  # إضافة 24 ساعة
        
        # تحويل إلى أيام (كل 420 دقيقة = يوم واحد)
        duration_days = duration_minutes / 420
        return round(duration_days, 2)
    return 0
```

### 2. عرض المدة في القوالب

#### **أ) قائمة المغادرات:**
```html
<!-- عمود المدة في الجدول -->
<th><i class="fas fa-hourglass-half text-primary"></i> المدة</th>

<!-- قيمة المدة -->
<td>
    <span class="badge bg-info">
        <i class="fas fa-calendar-day"></i> 
        {{ departure.calculate_duration_days }} يوم
    </span>
</td>
```

#### **ب) نموذج الإضافة/التعديل:**
```html
<!-- تخطيط محسن: 3 أعمدة -->
<div class="row">
    <div class="col-md-4 mb-3">من الساعة</div>
    <div class="col-md-4 mb-3">إلى الساعة</div>
    <div class="col-md-4 mb-3">
        <label class="form-label">المدة</label>
        <div class="form-control-plaintext">
            <span id="duration_display" class="badge bg-info fs-6">
                <i class="fas fa-calendar-day"></i> 0.00 يوم
            </span>
        </div>
    </div>
</div>
```

#### **ج) تفاصيل المغادرة:**
```html
<div class="col-md-4">
    <label class="form-label fw-bold">
        <i class="fas fa-hourglass-half text-primary"></i> المدة:
    </label>
    <p class="form-control-plaintext">
        <span class="badge bg-info fs-6">
            <i class="fas fa-calendar-day"></i> 
            {{ departure.calculate_duration_days }} يوم
        </span>
    </p>
</div>
```

### 3. JavaScript للحساب التفاعلي

#### **في نموذج الإضافة/التعديل:**
```javascript
function calculateDuration() {
    const timeFrom = timeFromInput.value;
    const timeTo = timeToInput.value;

    if (timeFrom && timeTo) {
        // تحويل الأوقات إلى دقائق
        const [fromHours, fromMinutes] = timeFrom.split(':').map(Number);
        const [toHours, toMinutes] = timeTo.split(':').map(Number);

        const fromTotalMinutes = fromHours * 60 + fromMinutes;
        let toTotalMinutes = toHours * 60 + toMinutes;

        // معالجة عبور منتصف الليل
        if (toTotalMinutes < fromTotalMinutes) {
            toTotalMinutes += 24 * 60;
        }

        // حساب المدة
        const durationMinutes = toTotalMinutes - fromTotalMinutes;
        const durationDays = (durationMinutes / 420).toFixed(2);

        // تحديث العرض مع تغيير اللون
        durationDisplay.innerHTML = `<i class="fas fa-calendar-day"></i> ${durationDays} يوم`;
        durationDisplay.className = 'badge fs-6 ' + (durationDays > 1 ? 'bg-warning' : 'bg-info');
    }
}

// إضافة مستمعي الأحداث
timeFromInput.addEventListener('change', calculateDuration);
timeToInput.addEventListener('change', calculateDuration);
```

## 📊 أمثلة على الحساب

### **حالات عادية:**
| من الساعة | إلى الساعة | المدة بالدقائق | المدة بالأيام | الحساب |
|-----------|------------|----------------|---------------|---------|
| 08:00 | 15:00 | 420 دقيقة | **1.00 يوم** | 420 ÷ 420 = 1.00 |
| 09:00 | 12:00 | 180 دقيقة | **0.43 يوم** | 180 ÷ 420 = 0.43 |
| 08:00 | 16:00 | 480 دقيقة | **1.14 يوم** | 480 ÷ 420 = 1.14 |
| 14:00 | 17:30 | 210 دقيقة | **0.50 يوم** | 210 ÷ 420 = 0.50 |

### **حالات خاصة (عبور منتصف الليل):**
| من الساعة | إلى الساعة | المدة الفعلية | المدة بالأيام |
|-----------|------------|---------------|---------------|
| 22:00 | 06:00 | 8 ساعات (480 دقيقة) | **1.14 يوم** |
| 23:30 | 07:30 | 8 ساعات (480 دقيقة) | **1.14 يوم** |
| 20:00 | 04:00 | 8 ساعات (480 دقيقة) | **1.14 يوم** |

## 🎨 التصميم البصري

### **الألوان:**
- **أزرق فاتح (`bg-info`)**: للمدد أقل من يوم واحد
- **أصفر (`bg-warning`)**: للمدد يوم واحد أو أكثر

### **الأيقونات:**
- **`fas fa-hourglass-half`**: في رأس عمود المدة
- **`fas fa-calendar-day`**: مع قيمة المدة

### **التخطيط:**
- **قائمة المغادرات**: عمود منفصل للمدة
- **النموذج**: 3 أعمدة متساوية (من، إلى، المدة)
- **التفاصيل**: صف منفصل للمدة مع تصميم مميز

## ✅ الميزات المحققة

### 1. **حساب دقيق:**
- معادلة موحدة: 420 دقيقة = يوم واحد
- معالجة عبور منتصف الليل
- تقريب لمنزلتين عشريتين

### 2. **عرض تفاعلي:**
- حساب فوري في النموذج
- تغيير الألوان حسب المدة
- عرض متسق في جميع الصفحات

### 3. **تجربة مستخدم محسنة:**
- أيقونات مميزة ووضحة
- ألوان معبرة
- تخطيط منظم ومتناسق

### 4. **مرونة في الاستخدام:**
- يعمل مع جميع أنواع الأوقات
- دعم المغادرات القصيرة والطويلة
- لا يحتاج إعدادات إضافية

## 🔄 كيفية العمل

### **عند إضافة مغادرة جديدة:**
1. المستخدم يدخل الأوقات
2. JavaScript يحسب المدة فوراً
3. يتم عرض المدة مع اللون المناسب
4. عند الحفظ، المدة متاحة للعرض

### **عند عرض المغادرات:**
1. النظام يستدعي `calculate_duration_days()`
2. يحسب المدة من الأوقات المحفوظة
3. يعرض النتيجة مع التصميم المناسب

## 🧪 اختبار الميزة

### **الخطوات:**
1. **افتح قائمة المغادرات**: http://localhost:8000/leaves/departures/
2. **لاحظ عمود المدة**: يظهر لجميع المغادرات
3. **اختبر إضافة مغادرة**: http://localhost:8000/leaves/departures/add/
4. **جرب أوقات مختلفة**: ولاحظ التحديث الفوري

### **حالات للاختبار:**
- **مغادرة قصيرة**: 09:00 - 11:00 (0.48 يوم)
- **مغادرة يوم كامل**: 08:00 - 15:00 (1.00 يوم)
- **مغادرة طويلة**: 08:00 - 16:00 (1.14 يوم)
- **عبور منتصف الليل**: 22:00 - 06:00 (1.14 يوم)

## 📁 الملفات المحدثة

1. **`leaves/models.py`**: إضافة دالة `calculate_duration_days()`
2. **`templates/leaves/departure_list.html`**: عمود المدة في الجدول
3. **`templates/leaves/departure_form.html`**: عرض المدة في النموذج + JavaScript
4. **`templates/leaves/departure_detail.html`**: عرض المدة في التفاصيل
5. **`templates/leaves/departure_confirm_delete.html`**: عرض المدة في تأكيد الحذف

## 🎯 النتيجة النهائية

✅ **ميزة حساب المدة تعمل بالكامل**
✅ **عرض تفاعلي في جميع الصفحات**
✅ **حساب دقيق بالمعادلة المطلوبة**
✅ **تصميم احترافي ومتناسق**
✅ **تجربة مستخدم محسنة**

**الميزة جاهزة للاستخدام الفوري! 🎉**
