{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .alert-warning {
        border-left: 4px solid #ffc107;
    }
    
    .btn-outline-primary:hover {
        transform: translateY(-1px);
        transition: all 0.2s ease-in-out;
    }
    
    .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        color: #fff;
        transform: translateY(-1px);
        transition: all 0.2s ease-in-out;
    }
    
    .transfer-details {
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #dee2e6;
    }
    
    .detail-row:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-weight: 600;
        color: #495057;
    }
    
    .detail-value {
        color: #212529;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle"></i> {{ title }}
                    </h6>
                    <div>
                        <a href="{% url 'home:employee_transfer_management' %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left"></i> العودة لإدارة النقل
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <div class="alert alert-warning" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas fa-exclamation-triangle"></i> تحذير!
                        </h5>
                        <p class="mb-0">
                            أنت على وشك حذف عملية نقل الموظف نهائياً. هذا الإجراء لا يمكن التراجع عنه.
                        </p>
                    </div>
                    
                    <div class="transfer-details">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle"></i> تفاصيل عملية النقل
                        </h6>
                        
                        <div class="detail-row">
                            <span class="detail-label">الرقم الوزاري:</span>
                            <span class="detail-value">{{ transfer.ministry_number }}</span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">اسم الموظف:</span>
                            <span class="detail-value">{{ transfer.employee_name }}</span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">التخصص:</span>
                            <span class="detail-value">{{ transfer.specialization|default:"-" }}</span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">الخدمة الفعلية:</span>
                            <span class="detail-value">
                                {{ transfer.calculated_actual_service|default:transfer.actual_service|default:"-" }}
                                <small class="text-muted d-block">
                                    <i class="fas fa-info-circle"></i> محسوبة من صفحة الخدمة الفعلية
                                </small>
                            </span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">القسم الحالي:</span>
                            <span class="detail-value">{{ transfer.current_department }}</span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">القسم الجديد:</span>
                            <span class="detail-value">{{ transfer.new_department.name }}</span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">صفة النقل:</span>
                            <span class="detail-value">
                                {% if transfer.transfer_type == 'internal_transfer' %}
                                    النقل الداخلي
                                {% else %}
                                    التكليف المؤقت
                                {% endif %}
                            </span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">التنسيب:</span>
                            <span class="detail-value">
                                {% if transfer.endorsement == 'admin_financial_manager' %}
                                    تنسيب مدير الشؤون الادارية والمالية
                                {% else %}
                                    تنسيب لجنة الموارد البشرية
                                {% endif %}
                            </span>
                        </div>
                        
                        {% if transfer.notes %}
                        <div class="detail-row">
                            <span class="detail-label">الملاحظات:</span>
                            <span class="detail-value">{{ transfer.notes }}</span>
                        </div>
                        {% endif %}
                        
                        <div class="detail-row">
                            <span class="detail-label">تاريخ الإنشاء:</span>
                            <span class="detail-value">{{ transfer.created_at|date:"Y/m/d H:i" }}</span>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <form method="post" style="display: inline;">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-danger btn-lg me-3" 
                                    onclick="return confirm('هل أنت متأكد من حذف هذا النقل نهائياً؟')">
                                <i class="fas fa-trash"></i> تأكيد الحذف
                            </button>
                        </form>
                        
                        <a href="{% url 'home:employee_transfer_management' %}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation on form submit
    $('form').on('submit', function(e) {
        const confirmed = confirm('تأكيد أخير: هل أنت متأكد من حذف عملية النقل هذه نهائياً؟\n\nلا يمكن التراجع عن هذا الإجراء.');
        if (!confirmed) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
