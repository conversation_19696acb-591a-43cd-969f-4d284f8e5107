{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if form.instance.pk %}
        تعديل معلم محمل على تخصص آخر
    {% else %}
        إضافة معلم محمل على تخصص آخر
    {% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        padding: 30px;
        margin-top: 20px;
    }
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 10px;
    }
    .search-result {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 5px;
        padding: 15px;
        margin-top: 10px;
        display: none;
    }
    .search-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 5px;
        padding: 15px;
        margin-top: 10px;
        display: none;
    }
    .form-group label {
        font-weight: bold;
        color: #333;
    }
    .required-field::after {
        content: " *";
        color: red;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1>
            <i class="fas fa-chalkboard-teacher"></i>
            {% if form.instance.pk %}
                تعديل معلم محمل على تخصص آخر
            {% else %}
                إضافة معلم محمل على تخصص آخر
            {% endif %}
        </h1>
        <p class="lead">إدارة المعلمين الذين يدرسون تخصصات مختلفة عن تخصصهم الأصلي</p>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="form-container">
                <form method="post" id="assignmentForm">
                    {% csrf_token %}
                    
                    <!-- Employee Search Section -->
                    <div class="form-group">
                        <label for="{{ form.ministry_number.id_for_label }}" class="required-field">
                            {{ form.ministry_number.label }}
                        </label>
                        {{ form.ministry_number }}
                        <small class="form-text text-muted">
                            أدخل الرقم الوزاري للمعلم للبحث عن بياناته
                        </small>
                        {% if form.ministry_number.errors %}
                            <div class="text-danger">
                                {% for error in form.ministry_number.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Search Result Display -->
                    <div id="searchResult" class="search-result">
                        <h5><i class="fas fa-check-circle"></i> تم العثور على المعلم</h5>
                        <div id="employeeInfo"></div>
                    </div>

                    <!-- Search Error Display -->
                    <div id="searchError" class="search-error">
                        <h5><i class="fas fa-exclamation-triangle"></i> خطأ في البحث</h5>
                        <div id="errorMessage"></div>
                    </div>

                    <!-- Hidden Employee Field -->
                    {{ form.employee }}

                    <!-- Original Specialty -->
                    <div class="form-group">
                        <label for="{{ form.original_specialty.id_for_label }}" class="required-field">
                            {{ form.original_specialty.label }}
                        </label>
                        {{ form.original_specialty }}
                        {% if form.original_specialty.errors %}
                            <div class="text-danger">
                                {% for error in form.original_specialty.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Assigned Specialty -->
                    <div class="form-group">
                        <label for="{{ form.assigned_specialty.id_for_label }}" class="required-field">
                            {{ form.assigned_specialty.label }}
                        </label>
                        {{ form.assigned_specialty }}
                        {% if form.assigned_specialty.errors %}
                            <div class="text-danger">
                                {% for error in form.assigned_specialty.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Department -->
                    <div class="form-group">
                        <label for="{{ form.department.id_for_label }}" class="required-field">
                            {{ form.department.label }}
                        </label>
                        {{ form.department }}
                        {% if form.department.errors %}
                            <div class="text-danger">
                                {% for error in form.department.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Subjects Taught -->
                    <div class="form-group">
                        <label for="{{ form.subjects_taught.id_for_label }}" class="required-field">
                            {{ form.subjects_taught.label }}
                        </label>
                        {{ form.subjects_taught }}
                        <small class="form-text text-muted">
                            اذكر المواد التي يدرسها المعلم في التخصص الجديد
                        </small>
                        {% if form.subjects_taught.errors %}
                            <div class="text-danger">
                                {% for error in form.subjects_taught.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Assignment Date -->
                    <div class="form-group">
                        <label for="{{ form.assignment_date.id_for_label }}" class="required-field">
                            {{ form.assignment_date.label }}
                        </label>
                        {{ form.assignment_date }}
                        {% if form.assignment_date.errors %}
                            <div class="text-danger">
                                {% for error in form.assignment_date.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Notes -->
                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}">
                            {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        <small class="form-text text-muted">
                            ملاحظات إضافية حول تحميل المعلم على التخصص الآخر
                        </small>
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Is Active -->
                    <div class="form-group form-check">
                        {{ form.is_active }}
                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                            {{ form.is_active.label }}
                        </label>
                        {% if form.is_active.errors %}
                            <div class="text-danger">
                                {% for error in form.is_active.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Submit Buttons -->
                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save"></i>
                            {% if form.instance.pk %}
                                تحديث البيانات
                            {% else %}
                                حفظ البيانات
                            {% endif %}
                        </button>
                        <a href="{% url 'employees:specialty_assignments_list' %}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Employee search functionality
    $('#ministry_number_input').on('blur', function() {
        var ministryNumber = $(this).val().trim();
        if (ministryNumber) {
            searchEmployee(ministryNumber);
        }
    });

    function searchEmployee(ministryNumber) {
        $.ajax({
            url: '{% url "employees:search_employee_for_assignment" %}',
            type: 'GET',
            data: {
                'ministry_number': ministryNumber
            },
            success: function(response) {
                if (response.success) {
                    // Display employee info
                    var employeeInfo = `
                        <p><strong>الاسم:</strong> ${response.employee.full_name}</p>
                        <p><strong>الرقم الوزاري:</strong> ${response.employee.ministry_number}</p>
                        <p><strong>التخصص:</strong> ${response.employee.specialization || 'غير محدد'}</p>
                        <p><strong>القسم الحالي:</strong> ${response.employee.department || 'غير محدد'}</p>
                    `;
                    $('#employeeInfo').html(employeeInfo);
                    $('#searchResult').show();
                    $('#searchError').hide();
                    
                    // Fill form fields
                    $('#id_employee').val(response.employee.id);
                    $('#id_original_specialty').val(response.employee.specialization || '');
                    $('#id_department').val(response.employee.department || '');
                } else {
                    // Display error
                    $('#errorMessage').text(response.error);
                    $('#searchError').show();
                    $('#searchResult').hide();
                    
                    // Clear form fields
                    $('#id_employee').val('');
                    $('#id_original_specialty').val('');
                    $('#id_department').val('');
                }
            },
            error: function() {
                $('#errorMessage').text('حدث خطأ في الاتصال بالخادم');
                $('#searchError').show();
                $('#searchResult').hide();
            }
        });
    }

    // Set default assignment date to today
    if (!$('#id_assignment_date').val()) {
        var today = new Date().toISOString().split('T')[0];
        $('#id_assignment_date').val(today);
    }
});
</script>
{% endblock %}
