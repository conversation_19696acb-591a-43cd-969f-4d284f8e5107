{% extends 'base.html' %}
{% load static %}

{% block title %}حذف الدرجة الوظيفية{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 10px;
    }
    
    .delete-container {
        background: white;
        border-radius: 10px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: 1px solid #dc3545;
    }
    
    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 1rem;
    }
    
    .grade-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        border-left: 4px solid #dc3545;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
    }
    
    .info-value {
        color: #212529;
    }
    
    .btn-action {
        min-width: 120px;
        margin: 0 0.5rem;
    }
    
    .grade-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        background-color: #dc3545;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1 class="mb-3">
            <i class="fas fa-exclamation-triangle"></i>
            تأكيد حذف الدرجة الوظيفية
        </h1>
        <p class="lead mb-0">هل أنت متأكد من حذف هذه الدرجة الوظيفية؟</p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="delete-container">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle warning-icon"></i>
                    <h4 class="text-danger mb-3">تحذير: عملية الحذف لا يمكن التراجع عنها</h4>
                    <p class="text-muted mb-4">
                        سيتم حذف الدرجة الوظيفية التالية نهائياً من النظام:
                    </p>
                </div>

                <!-- Grade Information -->
                <div class="grade-info">
                    <h5 class="text-center mb-3">
                        <i class="fas fa-medal text-danger"></i>
                        معلومات الدرجة الوظيفية
                    </h5>
                    
                    <div class="info-row">
                        <span class="info-label">
                            <i class="fas fa-id-card text-muted me-2"></i>
                            الرقم الوزاري:
                        </span>
                        <span class="info-value">
                            <strong>{{ grade.employee.ministry_number }}</strong>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">
                            <i class="fas fa-user text-muted me-2"></i>
                            اسم الموظف:
                        </span>
                        <span class="info-value">
                            <strong>{{ grade.employee.full_name }}</strong>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">
                            <i class="fas fa-graduation-cap text-muted me-2"></i>
                            التخصص:
                        </span>
                        <span class="info-value">{{ grade.employee.specialization|default:"غير محدد" }}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">
                            <i class="fas fa-building text-muted me-2"></i>
                            القسم:
                        </span>
                        <span class="info-value">{{ grade.employee.school|default:"غير محدد" }}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">
                            <i class="fas fa-medal text-muted me-2"></i>
                            الدرجة الوظيفية:
                        </span>
                        <span class="info-value">
                            <span class="grade-badge">{{ grade.get_grade_display }}</span>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">
                            <i class="fas fa-calendar-alt text-muted me-2"></i>
                            تاريخ الحلول بالدرجة:
                        </span>
                        <span class="info-value">{{ grade.grade_date|date:"Y/m/d" }}</span>
                    </div>
                    
                    {% if grade.notes %}
                    <div class="info-row">
                        <span class="info-label">
                            <i class="fas fa-sticky-note text-muted me-2"></i>
                            الملاحظات:
                        </span>
                        <span class="info-value">{{ grade.notes }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="info-row">
                        <span class="info-label">
                            <i class="fas fa-clock text-muted me-2"></i>
                            تاريخ الإضافة:
                        </span>
                        <span class="info-value">{{ grade.created_at|date:"Y/m/d H:i" }}</span>
                    </div>
                    
                    {% if grade.updated_at != grade.created_at %}
                    <div class="info-row">
                        <span class="info-label">
                            <i class="fas fa-edit text-muted me-2"></i>
                            آخر تحديث:
                        </span>
                        <span class="info-value">{{ grade.updated_at|date:"Y/m/d H:i" }}</span>
                    </div>
                    {% endif %}
                </div>

                <!-- Warning Message -->
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> 
                    بعد الحذف، لن تتمكن من استرداد هذه البيانات. تأكد من أن هذا هو الإجراء المطلوب.
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-center">
                    <a href="{% url 'employees:employee_grades_list' %}" class="btn btn-outline-secondary btn-action">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                    
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger btn-action" 
                                onclick="return confirm('هل أنت متأكد تماماً من حذف هذه الدرجة الوظيفية؟')">
                            <i class="fas fa-trash"></i> تأكيد الحذف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation dialog with more details
    $('form').submit(function(e) {
        const employeeName = '{{ grade.employee.full_name }}';
        const gradeName = '{{ grade.get_grade_display }}';
        
        const confirmed = confirm(
            `هل أنت متأكد من حذف الدرجة الوظيفية "${gradeName}" للموظف "${employeeName}"؟\n\n` +
            'هذا الإجراء لا يمكن التراجع عنه!'
        );
        
        if (!confirmed) {
            e.preventDefault();
            return false;
        }
    });
    
    // Auto-focus on cancel button for safety
    $('.btn-outline-secondary').focus();
});
</script>
{% endblock %}
