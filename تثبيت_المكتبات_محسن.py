#!/usr/bin/env python3
"""
تثبيت محسن لمكتبات نظام إدارة شؤون الموظفين
Enhanced installation for HR Management System libraries
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def print_colored(message, color="white"):
    """طباعة ملونة"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m", 
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "purple": "\033[95m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "reset": "\033[0m"
    }
    
    if platform.system() == "Windows":
        # في Windows، نستخدم طباعة عادية
        print(message)
    else:
        print(f"{colors.get(color, colors['white'])}{message}{colors['reset']}")

def run_command(command):
    """تنفيذ أمر وإرجاع النتيجة"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8',
            errors='ignore'
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr
    except Exception as e:
        return False, str(e)

def find_python_executable():
    """العثور على Python executable المناسب"""
    
    # قائمة بالأوامر المحتملة
    python_commands = [
        sys.executable,  # Python الحالي
        "python",
        "python3", 
        "py",
        "python.exe"
    ]
    
    # إضافة مسارات البيئة الافتراضية
    venv_paths = [
        "venv/Scripts/python.exe",
        "venv/bin/python",
        ".venv/Scripts/python.exe", 
        ".venv/bin/python"
    ]
    
    python_commands.extend(venv_paths)
    
    print_colored("🔍 البحث عن Python executable...", "blue")
    
    for cmd in python_commands:
        try:
            if os.path.exists(cmd):
                result = subprocess.run([cmd, "--version"], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print_colored(f"✅ تم العثور على Python: {cmd}", "green")
                    print_colored(f"   الإصدار: {result.stdout.strip()}", "cyan")
                    return cmd
        except:
            continue
    
    print_colored("❌ لم يتم العثور على Python executable مناسب", "red")
    return None

def find_requirements_file():
    """العثور على ملف requirements المناسب"""
    
    requirements_files = [
        "requirements_essential.txt",
        "requirements.txt"
    ]
    
    for req_file in requirements_files:
        if Path(req_file).exists():
            print_colored(f"✅ تم العثور على ملف المتطلبات: {req_file}", "green")
            return req_file
    
    print_colored("❌ لم يتم العثور على ملف requirements", "red")
    return None

def install_packages(python_cmd, requirements_file):
    """تثبيت المكتبات"""
    
    print_colored("📦 بدء تثبيت المكتبات...", "blue")
    
    # قائمة الأوامر للتجربة
    install_commands = [
        f'"{python_cmd}" -m pip install -r "{requirements_file}"',
        f'"{python_cmd}" -m pip install -r "{requirements_file}" --user',
        f'"{python_cmd}" -m pip install -r "{requirements_file}" --no-cache-dir',
        f'"{python_cmd}" -m pip install -r "{requirements_file}" --force-reinstall'
    ]
    
    for i, cmd in enumerate(install_commands, 1):
        print_colored(f"\n🔄 المحاولة {i}: {cmd}", "yellow")
        success, output = run_command(cmd)
        
        if success:
            print_colored("✅ تم تثبيت المكتبات بنجاح!", "green")
            return True
        else:
            print_colored(f"❌ فشل: {output[:200]}...", "red")
    
    # محاولة تثبيت المكتبات الأساسية فقط
    print_colored("\n🎯 محاولة تثبيت المكتبات الأساسية فقط...", "yellow")
    
    essential_packages = [
        "Django==5.2",
        "Pillow",
        "openpyxl", 
        "pandas",
        "reportlab",
        "django-import-export",
        "python-dateutil",
        "requests"
    ]
    
    success_count = 0
    
    for package in essential_packages:
        cmd = f'"{python_cmd}" -m pip install "{package}"'
        print_colored(f"📦 تثبيت {package}...", "cyan")
        
        success, output = run_command(cmd)
        if success:
            print_colored(f"✅ تم تثبيت {package}", "green")
            success_count += 1
        else:
            print_colored(f"❌ فشل تثبيت {package}", "red")
    
    print_colored(f"\n📊 تم تثبيت {success_count}/{len(essential_packages)} مكتبة أساسية", "blue")
    return success_count >= len(essential_packages) * 0.7  # 70% نجاح

def verify_installation(python_cmd):
    """التحقق من نجاح التثبيت"""
    
    print_colored("\n🔍 التحقق من التثبيت...", "blue")
    
    test_script = '''
import sys
packages = [
    ("django", "Django"),
    ("PIL", "Pillow"),
    ("openpyxl", "OpenPyXL"),
    ("pandas", "Pandas"),
    ("reportlab", "ReportLab"),
    ("import_export", "Django Import Export"),
    ("dateutil", "Python DateUtil"),
    ("requests", "Requests")
]

success = 0
total = len(packages)

for module, name in packages:
    try:
        __import__(module)
        print(f"✅ {name}")
        success += 1
    except ImportError:
        print(f"❌ {name}")

percentage = (success / total) * 100
print(f"")
print(f"📊 معدل النجاح: {percentage:.1f}% ({success}/{total})")

if percentage >= 70:
    print("🎉 التثبيت مكتمل والنظام جاهز للتشغيل!")
    sys.exit(0)
else:
    print("⚠️ بعض المكتبات لم يتم تثبيتها بشكل صحيح")
    sys.exit(1)
'''
    
    success, output = run_command(f'"{python_cmd}" -c "{test_script}"')
    
    if success:
        print_colored(output, "green")
        return True
    else:
        print_colored(f"❌ فشل في التحقق: {output}", "red")
        return False

def main():
    """الدالة الرئيسية"""
    
    print_colored("=" * 70, "purple")
    print_colored("🚀 مثبت مكتبات نظام إدارة شؤون الموظفين المحسن", "purple")
    print_colored("=" * 70, "purple")
    
    print_colored(f"📍 مجلد العمل: {os.getcwd()}", "cyan")
    print_colored(f"🖥️ نظام التشغيل: {platform.system()}", "cyan")
    
    # العثور على Python
    python_cmd = find_python_executable()
    if not python_cmd:
        print_colored("\n❌ لم يتم العثور على Python!", "red")
        print_colored("💡 يرجى تثبيت Python أو التأكد من إعداد PATH", "yellow")
        return False
    
    # العثور على ملف requirements
    requirements_file = find_requirements_file()
    if not requirements_file:
        print_colored("\n❌ لم يتم العثور على ملف requirements!", "red")
        print_colored("💡 يرجى التأكد من وجود requirements.txt أو requirements_essential.txt", "yellow")
        return False
    
    # تثبيت المكتبات
    if not install_packages(python_cmd, requirements_file):
        print_colored("\n❌ فشل في تثبيت المكتبات!", "red")
        return False
    
    # التحقق من التثبيت
    if verify_installation(python_cmd):
        print_colored("\n🎉 تم التثبيت بنجاح!", "green")
        print_colored("\n🚀 لتشغيل النظام:", "blue")
        print_colored("   python تشغيل_النظام.py", "cyan")
        print_colored("   أو", "cyan")
        print_colored("   python manage.py runserver", "cyan")
        return True
    else:
        print_colored("\n⚠️ بعض المكتبات لم يتم تثبيتها بشكل صحيح", "yellow")
        return False

if __name__ == "__main__":
    try:
        success = main()
        
        print_colored("\n" + "=" * 70, "purple")
        if success:
            print_colored("✅ اكتمل التثبيت بنجاح!", "green")
        else:
            print_colored("❌ فشل في التثبيت!", "red")
        print_colored("=" * 70, "purple")
        
        # إبقاء النافذة مفتوحة
        input("\n📱 اضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print_colored("\n\n⚠️ تم إلغاء التثبيت بواسطة المستخدم", "yellow")
    except Exception as e:
        print_colored(f"\n❌ خطأ غير متوقع: {e}", "red")
        input("\n📱 اضغط Enter للخروج...")
