{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة المغادرات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* تنسيق البطاقات */
    .dashboard-card {
        border-right: 4px solid;
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
        background-color: white;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }

    .dashboard-card .card-body {
        padding: 1.5rem;
    }

    .dashboard-card .text-xs {
        font-size: 0.85rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        display: block;
    }

    .dashboard-card .h3 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0;
    }

    /* تنسيق الأيقونات */
    .dashboard-card .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    /* ألوان البطاقات */
    .card-primary {
        border-color: #4e73df;
    }

    .card-primary .text-xs {
        color: #4e73df;
    }

    .card-primary .icon-circle {
        background-color: rgba(78, 115, 223, 0.1);
        color: #4e73df;
    }

    .card-success {
        border-color: #1cc88a;
    }

    .card-success .text-xs {
        color: #1cc88a;
    }

    .card-success .icon-circle {
        background-color: rgba(28, 200, 138, 0.1);
        color: #1cc88a;
    }

    .card-info {
        border-color: #36b9cc;
    }

    .card-info .text-xs {
        color: #36b9cc;
    }

    .card-info .icon-circle {
        background-color: rgba(54, 185, 204, 0.1);
        color: #36b9cc;
    }

    .card-warning {
        border-color: #f6c23e;
    }

    .card-warning .text-xs {
        color: #f6c23e;
    }

    .card-warning .icon-circle {
        background-color: rgba(246, 194, 62, 0.1);
        color: #f6c23e;
    }

    .card-danger {
        border-color: #e74a3b;
    }

    .card-danger .text-xs {
        color: #e74a3b;
    }

    .card-danger .icon-circle {
        background-color: rgba(231, 74, 59, 0.1);
        color: #e74a3b;
    }

    /* تنسيق الفلاتر مثل صفحة الموظفين */
    .filters-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin-bottom: 0;
    }

    .filter-control {
        font-size: 0.95rem;
        padding: 8px 12px;
        height: auto;
        border: 1px solid #ced4da;
    }

    .filter-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn-filter-action {
        min-width: 80px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .form-label.small {
        font-weight: 600;
        margin-bottom: 5px;
    }

    #filterInfo {
        background-color: #f8f9fc;
        padding: 8px 12px;
        border-radius: 0.35rem;
        border-left: 4px solid #4e73df;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-plane-departure text-primary"></i> قائمة المغادرات</h2>
    <div>
        <a href="{% url 'leaves:departure_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة مغادرة جديدة
        </a>
        <a href="{% url 'leaves:leave_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للإجازات
        </a>
    </div>
</div>

<!-- بطاقات الإحصائيات في سطر واحد -->
<div class="row mb-4">
    <!-- إجمالي المغادرات -->
    <div class="col-lg mb-3">
        <div class="dashboard-card card-primary">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">إجمالي المغادرات</span>
                        <div class="h3 text-dark">{{ total_departures }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-plane-departure"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مغادرات خاصة -->
    <div class="col-lg mb-3">
        <div class="dashboard-card card-info">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">مغادرات خاصة</span>
                        <div class="h3 text-dark">{{ personal_departures }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مغادرات رسمية -->
    <div class="col-lg mb-3">
        <div class="dashboard-card card-success">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">مغادرات رسمية</span>
                        <div class="h3 text-dark">{{ official_departures }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- هذا الشهر -->
    <div class="col-lg mb-3">
        <div class="dashboard-card card-warning">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">هذا الشهر</span>
                        <div class="h3 text-dark">{{ this_month_departures }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قيد الانتظار -->
    <div class="col-lg mb-3">
        <div class="dashboard-card card-warning">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">قيد الانتظار</span>
                        <div class="h3 text-dark">{{ pending_departures }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-hourglass-half"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- موافق عليها -->
    <div class="col-lg mb-3">
        <div class="dashboard-card card-success">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">موافق عليها</span>
                        <div class="h3 text-dark">{{ approved_departures }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مرفوضة -->
    <div class="col-lg mb-3">
        <div class="dashboard-card card-danger">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">مرفوضة</span>
                        <div class="h3 text-dark">{{ rejected_departures }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-times"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قسم الفلاتر -->
<div class="card shadow mb-3">
    <div class="card-header py-2">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-filter"></i> فلاتر البحث
        </h6>
    </div>
    <div class="card-body py-3">
        <form method="GET" id="departureFilterForm">
            <div class="row g-3">
                <!-- البحث -->
                <div class="col-md-3">
                    <label class="form-label small">
                        <i class="fas fa-search text-primary"></i> البحث
                    </label>
                    <input type="text" class="form-control form-control-sm filter-control"
                           name="search" placeholder="اسم الموظف، الرقم الوزاري، السبب..."
                           value="{{ current_filters.search }}" autocomplete="off">
                </div>

                <!-- نوع المغادرة -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-tag text-info"></i> نوع المغادرة
                    </label>
                    <select class="form-select form-select-sm filter-control" name="departure_type">
                        <option value="">جميع الأنواع</option>
                        <option value="personal" {% if current_filters.departure_type == 'personal' %}selected{% endif %}>
                            خاصة
                        </option>
                        <option value="official" {% if current_filters.departure_type == 'official' %}selected{% endif %}>
                            رسمية
                        </option>
                    </select>
                </div>

                <!-- السنة -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-calendar text-warning"></i> السنة
                    </label>
                    <select class="form-select form-select-sm filter-control" name="year">
                        <option value="">جميع السنوات</option>
                        {% for year_date in available_years %}
                            <option value="{{ year_date.year }}"
                                    {% if current_filters.year == year_date.year|stringformat:"s" %}selected{% endif %}>
                                {{ year_date.year }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- الحالة -->
                <div class="col-md-3">
                    <label class="form-label small">
                        <i class="fas fa-check-circle text-success"></i> الحالة
                    </label>
                    <select class="form-select form-select-sm filter-control" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending" {% if current_filters.status == 'pending' %}selected{% endif %}>
                            قيد الانتظار
                        </option>
                        <option value="approved" {% if current_filters.status == 'approved' %}selected{% endif %}>
                            موافق عليها
                        </option>
                        <option value="rejected" {% if current_filters.status == 'rejected' %}selected{% endif %}>
                            مرفوضة
                        </option>
                    </select>
                </div>

                <!-- أزرار التحكم -->
                <div class="col-md-2">
                    <label class="form-label small d-block">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-sm btn-filter-action">
                            <i class="fas fa-filter"></i> تطبيق
                        </button>
                        <a href="{% url 'leaves:departure_list' %}" class="btn btn-outline-secondary btn-sm btn-filter-action">
                            <i class="fas fa-undo"></i> إعادة ضبط
                        </a>
                    </div>
                </div>
            </div>
        </form>

        <!-- معلومات نتائج الفلتر -->
        <div class="row mt-2">
            <div class="col-12">
                <small class="text-muted" id="filterInfo">
                    <i class="fas fa-info-circle"></i>
                    {% if departures.count != total_departures %}
                        <span class="text-primary">الفلاتر النشطة: عرض {{ departures.count }} من {{ total_departures }} مغادرة</span>
                    {% else %}
                        عرض جميع المغادرات ({{ total_departures }})
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-table"></i> جميع المغادرات
            {% if departures.count != total_departures %}
                <span class="badge bg-info">{{ departures.count }} من {{ total_departures }}</span>
            {% endif %}
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th><i class="fas fa-id-card text-primary"></i> الرقم الوزاري</th>
                        <th><i class="fas fa-user text-primary"></i> الموظف</th>
                        <th><i class="fas fa-tag text-primary"></i> نوع المغادرة</th>
                        <th><i class="fas fa-calendar text-primary"></i> التاريخ</th>
                        <th><i class="fas fa-clock text-primary"></i> من الساعة</th>
                        <th><i class="fas fa-clock text-primary"></i> إلى الساعة</th>
                        <th><i class="fas fa-hourglass-half text-primary"></i> المدة</th>
                        <th><i class="fas fa-check-circle text-primary"></i> الحالة</th>
                        <th><i class="fas fa-cogs text-primary"></i> الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for departure in departures %}
                    <tr>
                        <td>
                            <span class="badge bg-secondary">{{ departure.employee.ministry_number }}</span>
                        </td>
                        <td>
                            <a href="{% url 'employees:employee_detail' departure.employee.pk %}" class="text-decoration-none">
                                <i class="fas fa-user text-muted"></i> {{ departure.employee.full_name }}
                            </a>
                        </td>
                        <td>
                            {% if departure.departure_type == 'personal' %}
                                <span class="badge bg-info"><i class="fas fa-user"></i> خاصة</span>
                            {% else %}
                                <span class="badge bg-primary"><i class="fas fa-building"></i> رسمية</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="text-nowrap">{{ departure.date|date:"Y/m/d" }}</span>
                        </td>
                        <td>
                            <span class="text-nowrap"><i class="fas fa-play text-success"></i> {{ departure.time_from }}</span>
                        </td>
                        <td>
                            <span class="text-nowrap"><i class="fas fa-stop text-danger"></i> {{ departure.time_to }}</span>
                        </td>
                        <td>
                            {% with hours=departure.get_duration_hours_minutes.0 minutes=departure.get_duration_hours_minutes.1 total_minutes=departure.calculate_duration_minutes %}
                                {% if total_minutes > 240 %}
                                    <span class="badge bg-danger" title="تجاوز الحد الأقصى (4 ساعات)">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        {{ hours }}س {{ minutes }}د
                                    </span>
                                {% elif total_minutes > 180 %}
                                    <span class="badge bg-warning" title="قريب من الحد الأقصى">
                                        <i class="fas fa-clock"></i>
                                        {{ hours }}س {{ minutes }}د
                                    </span>
                                {% else %}
                                    <span class="badge bg-success" title="ضمن الحد المسموح">
                                        <i class="fas fa-check"></i>
                                        {{ hours }}س {{ minutes }}د
                                    </span>
                                {% endif %}
                                <br>
                                <small class="text-muted">({{ departure.calculate_duration_days }} يوم)</small>
                            {% endwith %}
                        </td>
                        <td>
                            {% if departure.status == 'pending' %}
                                <span class="badge bg-warning"><i class="fas fa-hourglass-half"></i> قيد الانتظار</span>
                            {% elif departure.status == 'approved' %}
                                <span class="badge bg-success"><i class="fas fa-check"></i> موافق عليها</span>
                            {% elif departure.status == 'rejected' %}
                                <span class="badge bg-danger"><i class="fas fa-times"></i> مرفوضة</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group" aria-label="إجراءات المغادرة">
                                <a href="{% url 'leaves:departure_detail' departure.pk %}" class="btn btn-info btn-sm" title="معاينة">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'leaves:departure_update' departure.pk %}" class="btn btn-warning btn-sm" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'leaves:departure_delete' departure.pk %}" class="btn btn-danger btn-sm" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i><br>
                            لا يوجد مغادرات
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات hover للجدول
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#f8f9fa';
            });
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });

        // تأكيد الحذف
        const deleteButtons = document.querySelectorAll('a[href*="delete"]');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('هل أنت متأكد من حذف هذه المغادرة؟ لا يمكن التراجع عن هذا الإجراء.')) {
                    e.preventDefault();
                }
            });
        });

        // تحسين البحث - إرسال عند الضغط على Enter
        const searchInput = document.querySelector('input[name="search"]');
        if (searchInput) {
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    document.getElementById('departureFilterForm').submit();
                }
            });
        }

        // تحديث معلومات الفلتر عند تغيير القيم
        const filterInputs = document.querySelectorAll('.filter-control');
        filterInputs.forEach(input => {
            input.addEventListener('change', function() {
                updateFilterInfo();
            });
        });

        // دالة تحديث معلومات الفلتر
        function updateFilterInfo() {
            const activeFilters = [];

            // فحص البحث
            const searchValue = document.querySelector('input[name="search"]').value;
            if (searchValue) {
                activeFilters.push('البحث: "' + searchValue + '"');
            }

            // فحص نوع المغادرة
            const departureType = document.querySelector('select[name="departure_type"]').value;
            if (departureType) {
                const typeText = departureType === 'personal' ? 'خاصة' : 'رسمية';
                activeFilters.push('النوع: ' + typeText);
            }

            // فحص السنة
            const year = document.querySelector('select[name="year"]').value;
            if (year) {
                activeFilters.push('السنة: ' + year);
            }

            // فحص الحالة
            const status = document.querySelector('select[name="status"]').value;
            if (status) {
                let statusText = '';
                switch(status) {
                    case 'pending': statusText = 'قيد الانتظار'; break;
                    case 'approved': statusText = 'موافق عليها'; break;
                    case 'rejected': statusText = 'مرفوضة'; break;
                }
                activeFilters.push('الحالة: ' + statusText);
            }

            // تحديث عرض معلومات الفلتر
            const filterInfo = document.getElementById('filterInfo');
            if (activeFilters.length > 0) {
                filterInfo.innerHTML = '<i class="fas fa-filter text-primary"></i> الفلاتر النشطة: ' + activeFilters.join(' | ');
                filterInfo.className = 'text-primary';
            } else {
                filterInfo.innerHTML = '<i class="fas fa-info-circle"></i> عرض جميع المغادرات';
                filterInfo.className = 'text-muted';
            }
        }

        // تشغيل التحديث عند تحميل الصفحة
        updateFilterInfo();

        // تحسين البطاقات - إضافة تأثيرات
        const dashboardCards = document.querySelectorAll('.dashboard-card');
        dashboardCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 8px 15px rgba(0, 0, 0, 0.15)';
            });
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.1)';
            });
        });
    });
</script>
{% endblock %}
