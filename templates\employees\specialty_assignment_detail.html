{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل المعلم المحمل على تخصص آخر{% endblock %}

{% block extra_css %}
<style>
    .detail-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        padding: 30px;
        margin-top: 20px;
    }
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 10px;
    }
    .info-card {
        background: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 5px;
    }
    .info-label {
        font-weight: bold;
        color: #495057;
        margin-bottom: 5px;
    }
    .info-value {
        color: #212529;
        font-size: 16px;
    }
    .status-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: bold;
    }
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1><i class="fas fa-chalkboard-teacher"></i> تفاصيل المعلم المحمل على تخصص آخر</h1>
        <p class="lead">{{ assignment.employee.full_name }}</p>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="detail-container">
                <!-- Employee Information -->
                <div class="info-card">
                    <h4><i class="fas fa-user"></i> بيانات المعلم</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-label">الاسم الكامل</div>
                            <div class="info-value">{{ assignment.employee.full_name }}</div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-label">الرقم الوزاري</div>
                            <div class="info-value">{{ assignment.employee.ministry_number }}</div>
                        </div>
                    </div>
                </div>

                <!-- Specialty Information -->
                <div class="info-card">
                    <h4><i class="fas fa-graduation-cap"></i> معلومات التخصص</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-label">التخصص الأصلي</div>
                            <div class="info-value">{{ assignment.original_specialty }}</div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-label">التخصص المحمل عليه</div>
                            <div class="info-value">{{ assignment.assigned_specialty }}</div>
                        </div>
                    </div>
                </div>

                <!-- Department and Subjects -->
                <div class="info-card">
                    <h4><i class="fas fa-building"></i> القسم والمواد</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-label">القسم</div>
                            <div class="info-value">{{ assignment.department }}</div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-label">تاريخ التحميل</div>
                            <div class="info-value">{{ assignment.assignment_date|date:"Y-m-d" }}</div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="info-label">المواد التي يدرسها</div>
                            <div class="info-value">{{ assignment.subjects_taught|linebreaks }}</div>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                {% if assignment.notes %}
                <div class="info-card">
                    <h4><i class="fas fa-sticky-note"></i> ملاحظات</h4>
                    <div class="info-value">{{ assignment.notes|linebreaks }}</div>
                </div>
                {% endif %}

                <!-- Status and Dates -->
                <div class="info-card">
                    <h4><i class="fas fa-info-circle"></i> معلومات إضافية</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="info-label">الحالة</div>
                            <div class="info-value">
                                {% if assignment.is_active %}
                                    <span class="status-badge status-active">نشط</span>
                                {% else %}
                                    <span class="status-badge status-inactive">غير نشط</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-label">تاريخ الإنشاء</div>
                            <div class="info-value">{{ assignment.created_at|date:"Y-m-d H:i" }}</div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-label">آخر تحديث</div>
                            <div class="info-value">{{ assignment.updated_at|date:"Y-m-d H:i" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="col-md-4">
            <div class="detail-container">
                <h4><i class="fas fa-cogs"></i> الإجراءات</h4>
                
                <div class="d-grid gap-2">
                    <a href="{% url 'employees:specialty_assignment_update' assignment.pk %}" 
                       class="btn btn-warning btn-lg">
                        <i class="fas fa-edit"></i> تعديل البيانات
                    </a>
                    
                    <a href="{% url 'employees:specialty_assignment_delete' assignment.pk %}" 
                       class="btn btn-danger btn-lg"
                       onclick="return confirm('هل أنت متأكد من حذف هذا التحميل؟')">
                        <i class="fas fa-trash"></i> حذف التحميل
                    </a>
                    
                    <a href="{% url 'employees:employee_detail' assignment.employee.pk %}" 
                       class="btn btn-info btn-lg">
                        <i class="fas fa-user"></i> عرض بيانات المعلم
                    </a>
                    
                    <a href="{% url 'employees:specialty_assignments_list' %}" 
                       class="btn btn-secondary btn-lg">
                        <i class="fas fa-arrow-right"></i> العودة للقائمة
                    </a>
                </div>

                <!-- Quick Stats -->
                <div class="mt-4">
                    <h5><i class="fas fa-chart-bar"></i> إحصائيات سريعة</h5>
                    <div class="info-card">
                        <div class="row text-center">
                            <div class="col-12">
                                <div class="info-label">مدة التحميل</div>
                                <div class="info-value">
                                    {% now "Y-m-d" as today %}
                                    {% with days_diff=assignment.assignment_date|timesince %}
                                        {{ days_diff }}
                                    {% endwith %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add any JavaScript functionality here if needed
});
</script>
{% endblock %}
