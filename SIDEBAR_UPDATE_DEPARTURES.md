# تحديث القائمة الجانبية - إضافة رابط المغادرات
# Sidebar Update - Adding Departures Link

## التغيير المطلوب
إضافة رابط صفحة المغادرات إلى القائمة الجانبية في قسم "إجازات الموظفين" أسفل صفحة "إضافة إجازة".

## التغيير المنفذ

### الملف المحدث:
`templates/base.html`

### الموقع:
قسم إجازات الموظفين في القائمة الجانبية (السطور 339-346)

### الكود المضاف:
```html
{% if request.user.is_superuser or request.user.is_admin or 'leaves:departure_list' in user_visible_pages %}
<div class="sidebar-item">
    <a href="{% url 'leaves:departure_list' %}" class="sidebar-link {% if '/leaves/departures/' in request.path %}active{% endif %}">
        <i class="fas fa-plane-departure"></i>
        <span>المغادرات</span>
    </a>
</div>
{% endif %}
```

## ترتيب العناصر في قسم إجازات الموظفين

الآن أصبح ترتيب العناصر كالتالي:

1. **قائمة الإجازات** - `<i class="fas fa-list"></i>`
2. **إضافة إجازة** - `<i class="fas fa-plus"></i>`
3. **رصيد الإجازات** - `<i class="fas fa-calculator"></i>`
4. **المغادرات** - `<i class="fas fa-plane-departure"></i>` ← **جديد**
5. **تقارير الإجازات** - `<i class="fas fa-chart-bar"></i>`

## الميزات المضافة

### 1. الرابط:
- **URL**: `/leaves/departures/`
- **اسم URL**: `leaves:departure_list`

### 2. الأيقونة:
- **FontAwesome**: `fas fa-plane-departure`
- **مناسبة لموضوع المغادرات**

### 3. التفعيل التلقائي:
- الرابط يصبح نشط (active) عندما يكون المستخدم في صفحة المغادرات
- شرط التفعيل: `{% if '/leaves/departures/' in request.path %}`

### 4. الصلاحيات:
- متاح للمدير العام (`is_superuser`)
- متاح للمدير (`is_admin`)
- متاح للمستخدمين الذين لديهم صلاحية `leaves:departure_list`

## التحقق من التحديث

### 1. الوصول للصفحة:
```
http://localhost:8000/leaves/departures/
```

### 2. موقع الرابط:
- القائمة الجانبية → إجازات الموظفين → المغادرات

### 3. الاختبار:
- تسجيل الدخول كمدير
- فتح قسم إجازات الموظفين
- التحقق من وجود رابط "المغادرات"
- النقر على الرابط والتأكد من الانتقال للصفحة الصحيحة

## ملاحظات تقنية

### 1. التوافق:
- التحديث متوافق مع النظام الحالي
- لا يؤثر على الروابط الموجودة
- يحترم نظام الصلاحيات

### 2. التصميم:
- يتبع نفس نمط الروابط الأخرى
- أيقونة مناسبة ومميزة
- نص واضح ومفهوم

### 3. الوظائف:
- التفعيل التلقائي عند زيارة الصفحة
- إخفاء الرابط للمستخدمين غير المخولين
- انتقال سلس للصفحة

## الحالة
✅ **مكتمل** - تم إضافة رابط المغادرات بنجاح إلى القائمة الجانبية في قسم إجازات الموظفين.

## اختبار سريع
1. افتح النظام: http://localhost:8000
2. سجل الدخول بحساب admin
3. انتقل لقسم "إجازات الموظفين" في القائمة الجانبية
4. تحقق من وجود رابط "المغادرات" مع أيقونة الطائرة
5. اضغط على الرابط للانتقال لصفحة المغادرات
