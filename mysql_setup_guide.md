# دليل تثبيت وإعداد MySQL لنظام الموارد البشرية

## 1. تثبيت MySQL Server

### الطريقة الأولى: تحميل من الموقع الرسمي
1. اذه<PERSON> إلى: https://dev.mysql.com/downloads/mysql/
2. اختر "MySQL Installer for Windows"
3. حمل النسخة الكاملة (mysql-installer-web-community)
4. شغل الملف واتبع التعليمات

### الطريقة الثانية: استخدام Chocolatey (إذا كان مثبت)
```powershell
choco install mysql
```

### الطريقة الثالثة: استخدام XAMPP (الأسهل للتطوير)
1. حمل XAMPP من: https://www.apachefriends.org/download.html
2. ثبت XAMPP
3. شغ<PERSON> MySQL من لوحة تحكم XAMPP

## 2. إعد<PERSON> قاعدة البيانات

بعد تثبيت MySQL، افتح MySQL Command Line أو phpMyAdmin وقم بتنفيذ الأوامر التالية:

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE hr_system_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم جديد
CREATE USER 'hr_user'@'localhost' IDENTIFIED BY 'hr_password_2024';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON hr_system_db.* TO 'hr_user'@'localhost';

-- تطبيق التغييرات
FLUSH PRIVILEGES;

-- التحقق من إنشاء قاعدة البيانات
SHOW DATABASES;

-- التحقق من المستخدم
SELECT User, Host FROM mysql.user WHERE User = 'hr_user';
```

## 3. إعدادات الاتصال

بعد إعداد MySQL، ستحتاج إلى تحديث ملف `settings.py` بالمعلومات التالية:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'hr_system_db',
        'USER': 'hr_user',
        'PASSWORD': 'hr_password_2024',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

## 4. التحقق من الاتصال

يمكنك استخدام الأمر التالي للتحقق من الاتصال:

```bash
mysql -u hr_user -p hr_system_db
```

## ملاحظات مهمة

1. **كلمة المرور**: تأكد من استخدام كلمة مرور قوية في البيئة الإنتاجية
2. **التشفير**: استخدم utf8mb4 لدعم جميع أحرف Unicode بما في ذلك العربية
3. **الصلاحيات**: في البيئة الإنتاجية، امنح صلاحيات محددة فقط
4. **النسخ الاحتياطي**: تأكد من إعداد نظام نسخ احتياطي منتظم

## استكمال العملية

بعد إعداد MySQL، قم بتشغيل الأوامر التالية:

```bash
# تطبيق المخططات
python manage.py migrate

# استيراد البيانات
python manage.py loaddata backup_data.json

# إنشاء مستخدم إداري جديد (اختياري)
python manage.py createsuperuser
```
