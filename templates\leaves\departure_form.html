{% extends 'base.html' %}
{% load static %}

{% block title %}{% if departure %}تعديل مغادرة{% else %}إضافة مغادرة جديدة{% endif %} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% if departure %}تعديل مغادرة{% else %}إضافة مغادرة جديدة{% endif %}</h2>
    <a href="{% url 'leaves:departure_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للمغادرات
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">بيانات المغادرة</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            {{ form.employee_id }}

            <div class="mb-3">
                <label for="{{ form.ministry_number.id_for_label }}" class="form-label">الرقم الوزاري</label>
                <div class="input-group">
                    {{ form.ministry_number }}
                    <button class="btn btn-primary" type="button" id="search_employee_btn">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
                <div id="ministry_number_error" class="invalid-feedback d-none"></div>
                <div id="employee_name_display" class="alert alert-success mt-2 fw-bold d-none"></div>
                {% if form.ministry_number.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.ministry_number.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.departure_type.id_for_label }}" class="form-label">نوع المغادرة</label>
                {{ form.departure_type }}
                {% if form.departure_type.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.departure_type.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.date.id_for_label }}" class="form-label">التاريخ</label>
                {{ form.date }}
                {% if form.date.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.date.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="{{ form.time_from.id_for_label }}" class="form-label">من الساعة</label>
                    {{ form.time_from }}
                    {% if form.time_from.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.time_from.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="col-md-4 mb-3">
                    <label for="{{ form.time_to.id_for_label }}" class="form-label">إلى الساعة</label>
                    {{ form.time_to }}
                    {% if form.time_to.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.time_to.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="col-md-4 mb-3">
                    <label class="form-label">المدة</label>
                    <div class="form-control-plaintext">
                        <span id="duration_display" class="badge bg-info fs-6">
                            <i class="fas fa-calendar-day"></i> 0.00 يوم
                        </span>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="{{ form.reason.id_for_label }}" class="form-label">السبب</label>
                {{ form.reason }}
                {% if form.reason.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.reason.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.status.id_for_label }}" class="form-label">الحالة</label>
                {{ form.status }}
                {% if form.status.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.status.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary" id="submit_btn">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        // Employee search functionality
        const searchButton = document.getElementById('search_employee_btn');
        const ministryNumberInput = document.querySelector('input[name="ministry_number"]');
        const employeeIdInput = document.getElementById('id_employee_id');
        const employeeNameDisplay = document.getElementById('employee_name_display');
        const errorDisplay = document.getElementById('ministry_number_error');

        // إذا كان في وضع التعديل، عرض اسم الموظف
        {% if departure and employee_name %}
        if (employeeNameDisplay) {
            employeeNameDisplay.textContent = '{{ employee_name }}';
            employeeNameDisplay.style.display = 'block';
        }
        {% endif %}

        if (searchButton && ministryNumberInput) {
            console.log('Search button and input found');

            // Function to search for employee
            const searchEmployee = () => {
                const ministryNumber = ministryNumberInput.value.trim();
                console.log('Searching for ministry number:', ministryNumber);

                if (!ministryNumber) {
                    if (errorDisplay) {
                        errorDisplay.textContent = 'الرجاء إدخال الرقم الوزاري';
                        errorDisplay.classList.remove('d-none');
                    }
                    return;
                }

                // Clear previous results
                if (errorDisplay) {
                    errorDisplay.classList.add('d-none');
                }

                // Make AJAX request
                fetch(`/employees/get-by-ministry-number/?ministry_number=${ministryNumber}`)
                    .then(response => {
                        console.log('Response received:', response);
                        return response.json();
                    })
                    .then(data => {
                        console.log('Data received:', data);
                        if (data.success) {
                            // Employee found
                            if (employeeIdInput) {
                                employeeIdInput.value = data.employee.id;
                            }
                            if (employeeNameDisplay) {
                                employeeNameDisplay.innerHTML = '<i class="fas fa-user"></i> <strong>الموظف:</strong> ' + data.employee.full_name;
                                employeeNameDisplay.classList.remove('d-none');
                            }
                        } else {
                            // Error occurred
                            if (errorDisplay) {
                                errorDisplay.textContent = data.error || 'حدث خطأ أثناء البحث';
                                errorDisplay.classList.remove('d-none');
                            }
                            if (employeeNameDisplay) {
                                employeeNameDisplay.classList.add('d-none');
                            }
                            if (employeeIdInput) {
                                employeeIdInput.value = '';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        if (errorDisplay) {
                            errorDisplay.textContent = 'لم يتم العثور على موظف بهذا الرقم الوزاري';
                            errorDisplay.classList.remove('d-none');
                        }
                        if (employeeNameDisplay) {
                            employeeNameDisplay.classList.add('d-none');
                        }
                        if (employeeIdInput) {
                            employeeIdInput.value = '';
                        }
                    });
            };

            // Add event listeners
            searchButton.addEventListener('click', function(e) {
                e.preventDefault();
                searchEmployee();
            });

            // Search on Enter key
            ministryNumberInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchEmployee();
                }
            });
        } else {
            console.error('Search button or input not found');
        }

        // Form submission validation
        const form = document.querySelector('form');
        const submitBtn = document.getElementById('submit_btn');

        if (form && submitBtn) {
            form.addEventListener('submit', function(e) {
                const employeeIdInput = document.getElementById('id_employee_id');

                if (!employeeIdInput || !employeeIdInput.value) {
                    e.preventDefault();
                    alert('يجب اختيار موظف أولاً. الرجاء البحث عن الموظف باستخدام الرقم الوزاري.');
                    return false;
                }
            });
        }

        // Duration calculation
        const timeFromInput = document.querySelector('input[name="time_from"]');
        const timeToInput = document.querySelector('input[name="time_to"]');
        const departureTypeSelect = document.querySelector('select[name="departure_type"]');
        const durationDisplay = document.getElementById('duration_display');

        function calculateDuration() {
            const timeFrom = timeFromInput.value;
            const timeTo = timeToInput.value;
            const departureType = document.querySelector('select[name="departure_type"]').value;

            if (timeFrom && timeTo) {
                // تحويل الأوقات إلى دقائق
                const [fromHours, fromMinutes] = timeFrom.split(':').map(Number);
                const [toHours, toMinutes] = timeTo.split(':').map(Number);

                const fromTotalMinutes = fromHours * 60 + fromMinutes;
                let toTotalMinutes = toHours * 60 + toMinutes;

                // إذا كان الوقت النهائي أقل من البداية (عبور منتصف الليل)
                if (toTotalMinutes < fromTotalMinutes) {
                    toTotalMinutes += 24 * 60; // إضافة 24 ساعة
                }

                // حساب الفرق بالدقائق
                const durationMinutes = toTotalMinutes - fromTotalMinutes;

                // تحويل إلى ساعات ودقائق للعرض
                const hours = Math.floor(durationMinutes / 60);
                const minutes = durationMinutes % 60;

                // تحويل إلى أيام (كل 420 دقيقة = يوم واحد)
                const durationDays = (durationMinutes / 420).toFixed(2);

                // التحقق من الحد الأقصى (240 دقيقة = 4 ساعات) للمغادرات الخاصة فقط
                let displayClass = 'badge fs-6 ';
                let displayText = '';

                if (departureType === 'personal' && durationMinutes > 240) {
                    // تجاوز الحد الأقصى للمغادرات الخاصة
                    displayClass += 'bg-danger';
                    displayText = `<i class="fas fa-exclamation-triangle"></i> ${hours}س ${minutes}د (${durationDays} يوم) - تجاوز الحد الأقصى للمغادرات الخاصة!`;
                } else if (departureType === 'personal' && durationMinutes > 180) {
                    // قريب من الحد الأقصى للمغادرات الخاصة
                    displayClass += 'bg-warning';
                    displayText = `<i class="fas fa-clock"></i> ${hours}س ${minutes}د (${durationDays} يوم) - قريب من الحد الأقصى`;
                } else if (departureType === 'official') {
                    // مغادرة رسمية - لا يوجد حد أقصى
                    displayClass += 'bg-info';
                    displayText = `<i class="fas fa-building"></i> ${hours}س ${minutes}د (${durationDays} يوم) - مغادرة رسمية`;
                } else {
                    // ضمن الحد المسموح للمغادرات الخاصة
                    displayClass += 'bg-success';
                    displayText = `<i class="fas fa-check"></i> ${hours}س ${minutes}د (${durationDays} يوم)`;
                }

                // تحديث العرض
                durationDisplay.innerHTML = displayText;
                durationDisplay.className = displayClass;

                // إضافة تحذير إضافي إذا تجاوز الحد
                const warningDiv = document.getElementById('duration_warning');
                if (durationMinutes > 240) {
                    if (!warningDiv) {
                        const warning = document.createElement('div');
                        warning.id = 'duration_warning';
                        warning.className = 'alert alert-danger mt-2';
                        warning.innerHTML = `
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>تحذير:</strong> مدة المغادرة تتجاوز الحد الأقصى المسموح (4 ساعات = 240 دقيقة).
                            <br>المدة الحالية: ${hours} ساعة و ${minutes} دقيقة (${durationMinutes} دقيقة).
                        `;
                        durationDisplay.parentNode.appendChild(warning);
                    }
                } else {
                    if (warningDiv) {
                        warningDiv.remove();
                    }
                }
            } else {
                durationDisplay.innerHTML = '<i class="fas fa-calendar-day"></i> 0س 0د (0.00 يوم)';
                durationDisplay.className = 'badge bg-info fs-6';

                // إزالة التحذير إذا كان موجوداً
                const warningDiv = document.getElementById('duration_warning');
                if (warningDiv) {
                    warningDiv.remove();
                }
            }
        }

        // إضافة مستمعي الأحداث
        if (timeFromInput && timeToInput && departureTypeSelect && durationDisplay) {
            timeFromInput.addEventListener('change', calculateDuration);
            timeToInput.addEventListener('change', calculateDuration);
            timeFromInput.addEventListener('input', calculateDuration);
            timeToInput.addEventListener('input', calculateDuration);
            departureTypeSelect.addEventListener('change', calculateDuration);

            // حساب المدة عند تحميل الصفحة (للتعديل)
            calculateDuration();
        }
    });
</script>
{% endblock %}
