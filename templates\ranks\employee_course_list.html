{% extends 'base.html' %}
{% load static %}

{% block title %}الدورات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .search-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .search-card .card-body {
        padding: 2rem;
    }
    .filter-card {
        background: #f8f9fc;
        border: 1px solid #e3e6f0;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>الدورات</h2>
    <div>
        <a href="{% url 'ranks:employee_course_create' %}" class="btn btn-primary me-1">
            <i class="fas fa-plus"></i> إضافة دورة لموظف
        </a>
        <a href="{% url 'ranks:course_create' %}" class="btn btn-success me-1">
            <i class="fas fa-graduation-cap"></i> إضافة دورة جديدة
        </a>
        <a href="{% url 'ranks:course_list' %}" class="btn btn-info me-1">
            <i class="fas fa-list"></i> أسماء الدورات
        </a>
        <a href="{% url 'ranks:employee_rank_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للرتب
        </a>
    </div>
</div>

<!-- Search and Filter Card -->
<div class="card shadow mb-4 filter-card">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="اسم الموظف، الرقم الوزاري، أو اسم الدورة">
            </div>
            <div class="col-md-3">
                <label for="course" class="form-label">الدورة</label>
                <select class="form-control" id="course" name="course">
                    <option value="">جميع الدورات</option>
                    {% for course in courses %}
                        <option value="{{ course.id }}" {% if selected_course == course.id|stringformat:"s" %}selected{% endif %}>{{ course.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="year" class="form-label">السنة</label>
                <select class="form-control" id="year" name="year">
                    <option value="">جميع السنوات</option>
                    {% for year in years %}
                        <option value="{{ year.year }}" {% if selected_year == year.year|stringformat:"s" %}selected{% endif %}>{{ year.year }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Employee Courses List -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">دورات الموظفين</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr class="text-center">
                        <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                        <th><i class="fas fa-user me-1"></i> اسم الموظف</th>
                        <th><i class="fas fa-graduation-cap me-1"></i> اسم الدورة</th>
                        <th><i class="fas fa-clock me-1"></i> عدد الساعات</th>
                        <th><i class="fas fa-calendar me-1"></i> تاريخ الحصول عليها</th>
                        <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee_course in employee_courses %}
                    <tr class="text-center">
                        <td>{{ employee_course.employee.ministry_number }}</td>
                        <td>{{ employee_course.employee.full_name }}</td>
                        <td>{{ employee_course.course.name }}</td>
                        <td>
                            <span class="badge bg-info">{{ employee_course.hours }} ساعة</span>
                        </td>
                        <td>
                            <span class="badge bg-secondary">
                                {{ employee_course.completion_date|date:"d/m/Y" }}
                            </span>
                        </td>
                        <td>
                            <a href="{% url 'ranks:employee_course_detail' employee_course.pk %}" class="btn btn-info btn-sm">
                                <i class="fas fa-eye"></i> معاينة
                            </a>
                            <a href="{% url 'ranks:employee_course_update' employee_course.pk %}" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'ranks:employee_course_delete' employee_course.pk %}" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center">لا توجد دورات مضافة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>



<!-- Delete Course Modal -->
<div class="modal fade" id="deleteCourseModal" tabindex="-1" aria-labelledby="deleteCourseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCourseModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف دورة <strong id="deleteCourseNameModal"></strong> للموظف <strong id="deleteEmployeeNameModal"></strong>؟</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteCourseForm" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete course modal
    document.querySelectorAll('.delete-course').forEach(button => {
        button.addEventListener('click', function() {
            const courseId = this.getAttribute('data-id');
            const employeeName = this.getAttribute('data-employee');
            const courseName = this.getAttribute('data-course');
            
            document.getElementById('deleteEmployeeNameModal').textContent = employeeName;
            document.getElementById('deleteCourseNameModal').textContent = courseName;
            document.getElementById('deleteCourseForm').action = `/ranks/employee-courses/${courseId}/delete/`;
            
            new bootstrap.Modal(document.getElementById('deleteCourseModal')).show();
        });
    });
});
</script>
{% endblock %}
