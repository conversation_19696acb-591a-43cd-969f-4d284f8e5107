#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت التحويل الشامل من SQLite إلى MySQL
Complete SQLite to MySQL Migration Script
"""

import os
import sys
import django
import subprocess
import time
from django.core.management import call_command, execute_from_command_line
from django.db import connection

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')

def run_command(command, description):
    """تشغيل أمر وعرض النتيجة"""
    print(f"\n{description}...")
    print("-" * 50)
    
    try:
        if isinstance(command, list):
            result = subprocess.run(command, capture_output=True, text=True, encoding='utf-8')
        else:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print(f"✓ {description} - مكتمل بنجاح")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print(f"✗ {description} - فشل")
            if result.stderr:
                print(f"خطأ: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ {description} - خطأ: {str(e)}")
        return False

def test_mysql_connection():
    """اختبار الاتصال بـ MySQL"""
    try:
        django.setup()
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        return result is not None
    except Exception as e:
        print(f"خطأ في الاتصال: {str(e)}")
        return False

def migrate_database():
    """تنفيذ عملية التحويل الكاملة"""
    print("بدء عملية التحويل من SQLite إلى MySQL")
    print("=" * 60)
    
    # الخطوة 1: التحقق من وجود النسخة الاحتياطية
    if not os.path.exists('backup_data.json'):
        print("✗ لم يتم العثور على ملف النسخة الاحتياطية backup_data.json")
        print("قم بتشغيل: python backup_script.py أولاً")
        return False
    
    print("✓ تم العثور على ملف النسخة الاحتياطية")
    
    # الخطوة 2: اختبار الاتصال بـ MySQL
    print("\nاختبار الاتصال بـ MySQL...")
    if not test_mysql_connection():
        print("✗ فشل الاتصال بـ MySQL")
        print("تأكد من:")
        print("1. تثبيت وتشغيل MySQL")
        print("2. إنشاء قاعدة البيانات والمستخدم")
        print("3. صحة إعدادات الاتصال في settings.py")
        return False
    
    print("✓ تم الاتصال بـ MySQL بنجاح")
    
    # الخطوة 3: تطبيق المخططات
    print("\nتطبيق المخططات على MySQL...")
    try:
        django.setup()
        call_command('migrate', verbosity=2)
        print("✓ تم تطبيق المخططات بنجاح")
    except Exception as e:
        print(f"✗ فشل في تطبيق المخططات: {str(e)}")
        return False
    
    # الخطوة 4: استيراد البيانات
    print("\nاستيراد البيانات من النسخة الاحتياطية...")
    try:
        call_command('loaddata', 'backup_data.json', verbosity=2)
        print("✓ تم استيراد البيانات بنجاح")
    except Exception as e:
        print(f"✗ فشل في استيراد البيانات: {str(e)}")
        print("قد تحتاج إلى:")
        print("1. التحقق من تطابق إصدارات Django")
        print("2. حل تضارب المفاتيح الأساسية")
        print("3. التحقق من صحة ملف النسخة الاحتياطية")
        return False
    
    # الخطوة 5: التحقق من البيانات
    print("\nالتحقق من البيانات المستوردة...")
    try:
        with connection.cursor() as cursor:
            # فحص بعض الجداول الرئيسية
            tables_to_check = [
                'employees_employee',
                'accounts_user',
                'employment_department',
                'leaves_leave'
            ]
            
            for table in tables_to_check:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"✓ {table}: {count} سجل")
                except:
                    print(f"- {table}: غير موجود أو فارغ")
        
        print("✓ تم التحقق من البيانات")
        
    except Exception as e:
        print(f"✗ خطأ في التحقق من البيانات: {str(e)}")
        return False
    
    print("\n" + "=" * 60)
    print("✓ تمت عملية التحويل بنجاح!")
    print("=" * 60)
    
    print("\nالخطوات التالية:")
    print("1. اختبر النظام للتأكد من عمل جميع الوظائف")
    print("2. قم بإنشاء نسخة احتياطية من قاعدة بيانات MySQL")
    print("3. احتفظ بملف backup_data.json كنسخة احتياطية")
    print("4. يمكنك حذف ملف db.sqlite3 بعد التأكد من عمل النظام")
    
    return True

if __name__ == "__main__":
    success = migrate_database()
    if not success:
        print("\n✗ فشلت عملية التحويل!")
        sys.exit(1)
    else:
        print("\n✓ تمت عملية التحويل بنجاح!")
