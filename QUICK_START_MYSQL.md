# دليل البدء السريع - التحويل إلى MySQL
# Quick Start Guide - MySQL Migration

## المشكلة الحالية
❌ **خطأ**: `ModuleNotFoundError: No module named 'MySQLdb'`

✅ **تم الحل**: تم تثبيت مكتبة `mysqlclient` بنجاح

## الوضع الحالي
- ✅ النظام يعمل حالياً مع SQLite
- ✅ تم إنشاء نسخة احتياطية من البيانات (backup_data.json)
- ✅ تم تثبيت مكتبة mysqlclient
- ⏳ يحتاج إعداد MySQL Server

## خطوات التحويل السريع

### الطريقة الأسهل (موصى بها):
```bash
setup_mysql_complete.bat
```
هذا السكريپت سيرشدك خطوة بخطوة لإعداد MySQL كاملاً.

### الطريقة اليدوية:

#### 1. تثبيت MySQL
**الخيار أ: XAMPP (الأسهل)**
- حمل من: https://www.apachefriends.org/download.html
- ثبت XAMPP
- شغل MySQL من XAMPP Control Panel

**الخيار ب: MySQL Server**
- حمل من: https://dev.mysql.com/downloads/mysql/
- ثبت MySQL Server

#### 2. إعداد قاعدة البيانات
```sql
CREATE DATABASE hr_system_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'hr_user'@'localhost' IDENTIFIED BY 'hr_password_2024';
GRANT ALL PRIVILEGES ON hr_system_db.* TO 'hr_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 3. تحديث إعدادات النظام
```bash
python switch_to_mysql.py
```

#### 4. تطبيق المخططات
```bash
python manage.py migrate
```

#### 5. استيراد البيانات
```bash
python manage.py loaddata backup_data.json
```

#### 6. اختبار النظام
```bash
python manage.py runserver
```

## أدوات المساعدة المتوفرة

### فحص حالة MySQL:
```bash
python check_mysql_status.py
```

### اختبار الاتصال:
```bash
python test_mysql_connection.py
```

### التحقق من صحة التحويل:
```bash
python verify_migration.py
```

### العودة إلى SQLite (إذا لزم الأمر):
```bash
python rollback_to_sqlite.py
```

## استكشاف الأخطاء

### خطأ: "Can't connect to MySQL server"
**الحل:**
1. تأكد من تشغيل MySQL
2. في XAMPP: تأكد من أن MySQL يظهر "Running"
3. فحص المنفذ 3306

### خطأ: "Access denied"
**الحل:**
1. تأكد من إنشاء المستخدم والقاعدة
2. تحقق من كلمة المرور
3. أعد إنشاء المستخدم

### خطأ: "Unknown database"
**الحل:**
1. تأكد من إنشاء قاعدة البيانات
2. تحقق من اسم القاعدة في settings.py

## الملفات المهمة

- `backup_data.json` - نسخة احتياطية من البيانات ✅
- `setup_mysql_complete.bat` - إعداد شامل تلقائي
- `install_mysql_guide.md` - دليل تثبيت MySQL مفصل
- `check_mysql_status.py` - فحص حالة MySQL
- `switch_to_mysql.py` - تبديل الإعدادات
- `rollback_to_sqlite.py` - العودة إلى SQLite

## الأمان

- ✅ تم حفظ جميع البيانات الأصلية
- ✅ يمكن العودة إلى SQLite في أي وقت
- ✅ إعدادات SQLite محفوظة كنسخة احتياطية

## الدعم

إذا واجهت مشاكل:
1. شغل `python check_mysql_status.py` لفحص المشكلة
2. راجع `install_mysql_guide.md` للتفاصيل
3. استخدم `rollback_to_sqlite.py` للعودة الآمنة

## التوصية

**للمبتدئين**: استخدم `setup_mysql_complete.bat` - سيرشدك خطوة بخطوة

**للمتقدمين**: اتبع الخطوات اليدوية أعلاه

---

**ملاحظة**: النظام يعمل حالياً مع SQLite بشكل طبيعي. التحويل إلى MySQL اختياري ولكنه موصى به للأداء الأفضل.
