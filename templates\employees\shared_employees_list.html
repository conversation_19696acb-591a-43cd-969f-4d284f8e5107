{% extends 'base.html' %}
{% load static %}

{% block title %}المعلمين المشركين - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin: -1.5rem -1.5rem 3rem -1.5rem;
        border-radius: 0 0 30px 30px;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-header .container-fluid {
        position: relative;
        z-index: 1;
    }

    .stats-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: none;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    }

    .stats-card .icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        margin-bottom: 1rem;
    }

    .stats-card .number {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stats-card .label {
        color: #6c757d;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.9rem;
    }

    .search-container {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .table-container {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    }

    .table-enhanced {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .table-enhanced thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
        padding: 1rem;
    }

    .table-enhanced tbody tr {
        transition: all 0.3s ease;
    }

    .table-enhanced tbody tr:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        transform: scale(1.01);
    }

    .btn-enhanced {
        border-radius: 15px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-enhanced:hover::before {
        left: 100%;
    }

    .btn-enhanced:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .fade-in {
        opacity: 0;
        transform: translateY(30px);
        animation: fadeInUp 0.8s ease-out forwards;
    }

    @keyframes fadeInUp {
        from { 
            opacity: 0; 
            transform: translateY(30px) scale(0.95); 
        }
        to { 
            opacity: 1; 
            transform: translateY(0) scale(1); 
        }
    }

    .badge-status {
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 0.75rem;
    }

    .badge-active {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
    }

    .badge-inactive {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1><i class="fas fa-share-alt me-3"></i>المعلمين المشركين بأكثر من مدرسة</h1>
        <p class="lead mb-4">إدارة وتتبع المعلمين المشركين بين المدارس المختلفة</p>

        <!-- Action Buttons -->
        <div class="mt-4">
            <a href="{% url 'employees:shared_employee_create' %}" class="btn btn-enhanced btn-success btn-lg me-3">
                <i class="fas fa-plus me-2"></i> إضافة معلم مشترك جديد
            </a>
            <a href="{% url 'employees:export_shared_employees' %}" class="btn btn-enhanced btn-info btn-lg me-3">
                <i class="fas fa-download me-2"></i> تصدير البيانات
            </a>
            <a href="{% url 'employment:employment_list' %}" class="btn btn-enhanced btn-outline-light btn-lg">
                <i class="fas fa-arrow-left me-2"></i> العودة لإدارة الكادر
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card fade-in" style="animation-delay: 0.1s;">
                <div class="d-flex align-items-center">
                    <div class="icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <div class="ms-3">
                        <div class="number">{{ total_shared }}</div>
                        <div class="label">إجمالي المشركين</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card fade-in" style="animation-delay: 0.2s;">
                <div class="d-flex align-items-center">
                    <div class="icon" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ms-3">
                        <div class="number">{{ active_shared }}</div>
                        <div class="label">نشط حالياً</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card fade-in" style="animation-delay: 0.3s;">
                <div class="d-flex align-items-center">
                    <div class="icon" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="ms-3">
                        <div class="number">{{ inactive_shared }}</div>
                        <div class="label">منتهي الإشراك</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card fade-in" style="animation-delay: 0.4s;">
                <div class="d-flex align-items-center">
                    <div class="icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="ms-3">
                        <div class="number">{{ unique_departments }}</div>
                        <div class="label">المدارس المشاركة</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-container fade-in" style="animation-delay: 0.5s;">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label fw-bold">البحث العام</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" 
                           class="form-control" 
                           name="search" 
                           value="{{ search_query }}" 
                           placeholder="الرقم الوزاري، الاسم، التخصص...">
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">القسم الأصلي</label>
                <select name="original_department" class="form-select">
                    <option value="">جميع الأقسام الأصلية</option>
                    {% for dept in original_departments %}
                    <option value="{{ dept }}" {% if dept == original_department_filter %}selected{% endif %}>
                        {{ dept }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">القسم المشترك</label>
                <select name="shared_department" class="form-select">
                    <option value="">جميع الأقسام المشتركة</option>
                    {% for dept in shared_departments %}
                    <option value="{{ dept }}" {% if dept == shared_department_filter %}selected{% endif %}>
                        {{ dept }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>منتهي</option>
                </select>
            </div>
            <div class="col-12">
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-enhanced btn-primary">
                        <i class="fas fa-search me-2"></i> بحث
                    </button>
                    <a href="{% url 'employees:shared_employees_list' %}" class="btn btn-enhanced btn-outline-secondary">
                        <i class="fas fa-times me-2"></i> مسح الفلاتر
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Shared Employees Table -->
    <div class="table-container fade-in" style="animation-delay: 0.6s;">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة المعلمين المشركين
                {% if search_query %}
                <small class="text-muted">(نتائج البحث عن: "{{ search_query }}")</small>
                {% endif %}
            </h5>
            <div class="text-muted small">
                إجمالي النتائج: {{ shared_employees.count }}
            </div>
        </div>

        {% if shared_employees %}
        <div class="table-responsive">
            <table class="table table-enhanced" id="sharedEmployeesTable">
                <thead>
                    <tr>
                        <th width="8%">الرقم الوزاري</th>
                        <th width="20%">الاسم الكامل</th>
                        <th width="15%">التخصص</th>
                        <th width="15%">القسم الأصلي</th>
                        <th width="15%">القسم المشترك</th>
                        <th width="10%">تاريخ الإشراك</th>
                        <th width="7%">الحالة</th>
                        <th width="10%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for shared_employee in shared_employees %}
                    <tr>
                        <td>
                            <span class="badge bg-primary">{{ shared_employee.employee.ministry_number }}</span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-2" style="width: 40px; height: 40px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                                    {{ shared_employee.employee.full_name|first }}
                                </div>
                                <div>
                                    <div class="fw-bold">{{ shared_employee.employee.full_name }}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="text-muted">{{ shared_employee.employee.specialization|default:"غير محدد" }}</span>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ shared_employee.original_department }}</span>
                        </td>
                        <td>
                            <span class="badge bg-warning">{{ shared_employee.shared_department }}</span>
                        </td>
                        <td>
                            <i class="fas fa-calendar-alt text-muted me-1"></i>
                            {{ shared_employee.sharing_date|date:"Y/m/d" }}
                        </td>
                        <td>
                            {% if shared_employee.is_active %}
                                <span class="badge badge-status badge-active">
                                    <i class="fas fa-check me-1"></i>نشط
                                </span>
                            {% else %}
                                <span class="badge badge-status badge-inactive">
                                    <i class="fas fa-times me-1"></i>منتهي
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'employees:shared_employee_update' shared_employee.pk %}"
                                   class="btn btn-outline-primary btn-sm"
                                   title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'employees:shared_employee_delete' shared_employee.pk %}"
                                   class="btn btn-outline-danger btn-sm"
                                   title="حذف"
                                   onclick="return confirm('هل أنت متأكد من حذف هذا الإشراك؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-share-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا يوجد معلمين مشركين</h5>
            <p class="text-muted">
                {% if search_query %}
                    لم يتم العثور على نتائج للبحث "{{ search_query }}"
                {% else %}
                    لم يتم إضافة أي معلمين مشركين بعد
                {% endif %}
            </p>
            <a href="{% url 'employees:shared_employee_create' %}" class="btn btn-enhanced btn-primary">
                <i class="fas fa-plus me-2"></i> إضافة معلم مشترك جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable if there are records
    {% if shared_employees %}
    $('#sharedEmployeesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
        },
        "pageLength": 10,
        "ordering": true,
        "searching": false,
        "paging": true,
        "info": true,
        "responsive": true,
        "order": [[ 5, "desc" ]] // Sort by sharing date descending
    });
    {% endif %}

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Animate statistics cards
    function animateCounters() {
        const counters = document.querySelectorAll('.number');

        counters.forEach(counter => {
            const target = parseInt(counter.textContent);
            if (isNaN(target)) return;

            const duration = 2000;
            const increment = target / (duration / 16);
            let current = 0;

            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };

            // Start animation when element is visible
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        setTimeout(updateCounter, 500);
                        observer.unobserve(entry.target);
                    }
                });
            });

            observer.observe(counter);
        });
    }

    // Initialize animations
    animateCounters();
});
</script>
{% endblock %}
