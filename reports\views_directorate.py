from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum
from django.http import HttpResponse, JsonResponse
import pandas as pd
from io import BytesIO
import os
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
# Try to import Arabic text processing libraries
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False
    # Create dummy functions for when libraries are not available
    def arabic_reshaper_reshape(text):
        return text

    def get_display(text):
        return text
from django.utils import timezone
from employment.models import Department, Employment
from leaves.models import Leave, LeaveType, LeaveBalance
from employees.models import Employee

@login_required
def directorate_employees_report(request):
    """View for generating reports of employees working in the directorate"""
    # Get current year
    current_year = timezone.now().year

    # Get all departments where workplace is 'directorate'
    departments = Department.objects.filter(workplace='directorate').order_by('name')

    # Get selected department (if any)
    department_id = request.GET.get('department')
    selected_department = None

    if department_id:
        try:
            selected_department = Department.objects.get(id=department_id, workplace='directorate')
        except Department.DoesNotExist:
            pass

    # Get employees based on selection
    if selected_department:
        # Get employees in the selected department
        employments = Employment.objects.filter(
            department=selected_department,
            is_current=True
        ).select_related('employee')
        employees = [emp.employee for emp in employments]
        report_title = f"تقرير موظفي {selected_department.name}"
    else:
        # Get all employees in all directorate departments
        directorate_departments = Department.objects.filter(workplace='directorate')
        employments = Employment.objects.filter(
            department__in=directorate_departments,
            is_current=True
        ).select_related('employee')
        employees = [emp.employee for emp in employments]
        report_title = "تقرير موظفي المديرية"

    # Define leave types
    leave_types = [
        {'name': 'annual', 'display_name': 'الإجازة السنوية'},
        {'name': 'sick', 'display_name': 'الإجازة المرضية'},
        {'name': 'casual', 'display_name': 'الإجازة العرضية'}
    ]

    # Get leave balances for all employees
    employee_data = []
    for employee in employees:
        # Get current position and department
        position = employee.get_latest_position()
        current_employment = Employment.objects.filter(
            employee=employee,
            is_current=True
        ).first()
        department = current_employment.department.name if current_employment else "-"

        # Get leave balances
        annual_balance = {'initial': 0, 'used': 0, 'departures': 0, 'remaining': 0}
        sick_balance = {'initial': 0, 'used': 0, 'remaining': 0}
        casual_balance = {'initial': 0, 'used': 0, 'remaining': 0}

        # Get annual leave balance
        try:
            annual_leave_type = LeaveType.objects.get(name='annual')
            annual_balance_obj = LeaveBalance.objects.get(
                employee=employee,
                leave_type=annual_leave_type,
                year=current_year
            )

            # Get used leaves count
            annual_used_leaves = Leave.objects.filter(
                employee=employee,
                leave_type=annual_leave_type,
                start_date__year=current_year,
                status='approved'
            ).aggregate(total_days=Sum('days_count'))

            annual_used_days = annual_used_leaves['total_days'] or 0

            # Get personal departures only for annual leave
            from leaves.models import Departure
            departures = Departure.objects.filter(
                employee=employee,
                date__year=current_year,
                status='approved',
                departure_type='personal'  # المغادرات الخاصة فقط
            )

            # Calculate total departure days (personal only)
            departure_days = 0
            for departure in departures:
                departure_days += departure.calculate_duration_days()

            annual_remaining = annual_balance_obj.initial_balance - annual_used_days - departure_days

            annual_balance = {
                'initial': annual_balance_obj.initial_balance,
                'used': annual_used_days,
                'departures': departure_days,
                'remaining': annual_remaining
            }
        except (LeaveType.DoesNotExist, LeaveBalance.DoesNotExist):
            pass

        # Get sick leave balance
        try:
            sick_leave_type = LeaveType.objects.get(name='sick')
            sick_balance_obj = LeaveBalance.objects.get(
                employee=employee,
                leave_type=sick_leave_type,
                year=current_year
            )

            # Get used leaves count
            sick_used_leaves = Leave.objects.filter(
                employee=employee,
                leave_type=sick_leave_type,
                start_date__year=current_year,
                status='approved'
            ).aggregate(total_days=Sum('days_count'))

            sick_used_days = sick_used_leaves['total_days'] or 0
            sick_remaining = sick_balance_obj.initial_balance - sick_used_days

            sick_balance = {
                'initial': sick_balance_obj.initial_balance,
                'used': sick_used_days,
                'remaining': sick_remaining
            }
        except (LeaveType.DoesNotExist, LeaveBalance.DoesNotExist):
            pass

        # Get casual leave balance
        try:
            casual_leave_type = LeaveType.objects.get(name='casual')
            casual_balance_obj = LeaveBalance.objects.get(
                employee=employee,
                leave_type=casual_leave_type,
                year=current_year
            )

            # Get used leaves count
            casual_used_leaves = Leave.objects.filter(
                employee=employee,
                leave_type=casual_leave_type,
                start_date__year=current_year,
                status='approved'
            ).aggregate(total_days=Sum('days_count'))

            casual_used_days = casual_used_leaves['total_days'] or 0
            casual_remaining = casual_balance_obj.initial_balance - casual_used_days

            casual_balance = {
                'initial': casual_balance_obj.initial_balance,
                'used': casual_used_days,
                'remaining': casual_remaining
            }
        except (LeaveType.DoesNotExist, LeaveBalance.DoesNotExist):
            pass



        # Add employee data with leave balances
        employee_data.append({
            'employee': employee,
            'position': position,
            'department': department,
            'annual_balance': annual_balance,
            'sick_balance': sick_balance,
            'casual_balance': casual_balance
        })

    # Handle export
    if 'export' in request.GET:
        export_format = request.GET.get('export', 'excel')

        if export_format == 'excel':
            return export_to_excel(employee_data, report_title, leave_types)
        elif export_format == 'pdf':
            # Always show preview page first
            return render(request, 'reports/directorate_employees_pdf_preview.html', {
                'departments': departments,
                'selected_department': selected_department,
                'employee_data': employee_data,
                'leave_types': leave_types,
                'report_title': report_title
            })

    # Render the page
    return render(request, 'reports/directorate_employees_report.html', {
        'departments': departments,
        'selected_department': selected_department,
        'employee_data': employee_data,
        'leave_types': leave_types,
        'report_title': report_title,
        'current_year': current_year
    })

def export_to_excel(employee_data, report_title, leave_types):
    """Export employees data to Excel"""
    # Create DataFrame
    data = []

    for item in employee_data:
        employee = item['employee']

        # Create base employee data
        employee_row = {
            'الرقم الوزاري': employee.ministry_number,
            'الاسم': employee.full_name,
            'القسم': item['department'],
        }

        # Add annual leave balance data
        annual_name = leave_types[0]['display_name']
        employee_row[f'{annual_name} - الرصيد الأولي'] = item['annual_balance']['initial']
        employee_row[f'{annual_name} - الإجازات المستخدمة'] = item['annual_balance']['used']
        employee_row[f'{annual_name} - الرصيد المتبقي'] = item['annual_balance']['remaining']

        # Add sick leave balance data
        sick_name = leave_types[1]['display_name']
        employee_row[f'{sick_name} - الرصيد الأولي'] = item['sick_balance']['initial']
        employee_row[f'{sick_name} - الإجازات المستخدمة'] = item['sick_balance']['used']
        employee_row[f'{sick_name} - الرصيد المتبقي'] = item['sick_balance']['remaining']

        # Add casual leave balance data
        casual_name = leave_types[2]['display_name']
        employee_row[f'{casual_name} - الرصيد الأولي'] = item['casual_balance']['initial']
        employee_row[f'{casual_name} - الإجازات المستخدمة'] = item['casual_balance']['used']
        employee_row[f'{casual_name} - الرصيد المتبقي'] = item['casual_balance']['remaining']

        data.append(employee_row)

    # Create Excel file
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df = pd.DataFrame(data)
        df.to_excel(writer, index=False, sheet_name='تقرير الموظفين')

        # Auto-adjust columns' width
        worksheet = writer.sheets['تقرير الموظفين']
        for idx, col in enumerate(df.columns):
            max_len = max(df[col].astype(str).map(len).max(), len(col)) + 2
            worksheet.column_dimensions[chr(65 + idx)].width = max_len

    # Create response
    output.seek(0)
    response = HttpResponse(output.read(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename="{report_title}.xlsx"'

    return response

@login_required
def get_employee_whatsapp_data(request, employee_id):
    """AJAX endpoint to get employee data for WhatsApp message"""
    try:
        # Check if this is a request for all employees in a department
        if employee_id == '0' and 'department' in request.GET:
            department_id = request.GET.get('department')

            # Get all employees in the department or all directorate departments
            try:
                if department_id:
                    # Get employees in the selected department
                    department = Department.objects.get(id=department_id)
                    employments = Employment.objects.filter(
                        department=department,
                        is_current=True
                    ).select_related('employee')
                    department_name = department.name
                else:
                    # Get all employees in all directorate departments
                    directorate_departments = Department.objects.filter(workplace='directorate')
                    employments = Employment.objects.filter(
                        department__in=directorate_departments,
                        is_current=True
                    ).select_related('employee')
                    department_name = "جميع أقسام المديرية"

                employees_data = []
                current_year = timezone.now().year

                for employment in employments:
                    employee = employment.employee

                    # Skip employees without phone numbers
                    if not employee.phone_number:
                        continue

                    # Format phone number
                    phone = ''.join(filter(str.isdigit, employee.phone_number))
                    if phone.startswith('0'):
                        phone = '962' + phone[1:]
                    elif not phone.startswith('962'):
                        phone = '962' + phone

                    # Get leave balances
                    balances = LeaveBalance.objects.filter(
                        employee_id=employee.id,
                        year=current_year
                    )

                    # Construct message
                    message = f"مرحباً {employee.full_name}،\n\nأرصدة الإجازات الخاصة بك لعام {current_year}:\n"

                    for balance in balances:
                        message += f"- {balance.leave_type.get_name_display()}: {balance.remaining_balance} يوم\n"

                    message += "\nمع تحيات قسم شؤون الموظفين"

                    employees_data.append({
                        'id': employee.id,
                        'name': employee.full_name,
                        'phone': phone,
                        'message': message
                    })

                return JsonResponse({
                    'success': True,
                    'employees': employees_data,
                    'department': department_name,
                    'count': len(employees_data)
                })

            except Department.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'لم يتم العثور على القسم'})

        # Get individual employee data
        employee = Employee.objects.get(pk=employee_id)

        # Get current year
        current_year = timezone.now().year

        # Get all leave balances for this employee for the current year
        balances = LeaveBalance.objects.filter(
            employee_id=employee_id,
            year=current_year
        )

        # Get the latest leave for this employee
        latest_leave = Leave.objects.filter(
            employee_id=employee_id,
            status='approved'
        ).order_by('-end_date').first()

        # Format phone number (remove any non-digit characters and ensure it starts with 962)
        phone = employee.phone_number
        if phone:
            # Remove any non-digit characters
            phone = ''.join(filter(str.isdigit, phone))
            # Ensure it starts with 962 (Jordan country code)
            if phone.startswith('0'):
                phone = '962' + phone[1:]
            elif not phone.startswith('962'):
                phone = '962' + phone
        else:
            return JsonResponse({'success': False, 'error': 'لا يوجد رقم هاتف لهذا الموظف'})

        # Construct message
        message = f"مرحباً {employee.full_name}،\n\nأرصدة الإجازات الخاصة بك لعام {current_year}:\n"

        for balance in balances:
            message += f"- {balance.leave_type.get_name_display()}: {balance.remaining_balance} يوم\n"

        if latest_leave:
            message += f"\nتاريخ آخر إجازة: من {latest_leave.start_date} إلى {latest_leave.end_date}\n"
        else:
            message += "\nلا يوجد إجازات سابقة\n"

        message += "\nمع تحيات قسم شؤون الموظفين"

        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'name': employee.full_name,
                'phone': phone
            },
            'message': message
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على الموظف'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

def export_to_pdf(employee_data, report_title, leave_types):
    """Export employees data to PDF with proper Arabic support"""
    # Create a response object with PDF content type
    response = HttpResponse(content_type='application/pdf; charset=utf-8')
    response['Content-Disposition'] = f'attachment; filename="{report_title}.pdf"'

    # Register Arabic fonts - we'll register multiple fonts for better compatibility
    fonts_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'fonts')

    # Register Noto Sans Arabic font
    noto_sans_path = os.path.join(fonts_dir, 'NotoSansArabic-Regular.ttf')
    pdfmetrics.registerFont(TTFont('NotoSansArabic', noto_sans_path))

    # Try to register Noto Sans Arabic Bold font if available
    noto_sans_bold_path = os.path.join(fonts_dir, 'NotoSansArabic-Bold.ttf')
    if os.path.exists(noto_sans_bold_path):
        pdfmetrics.registerFont(TTFont('NotoSansArabic-Bold', noto_sans_bold_path))

    # Register Arial font as fallback
    arial_path = os.path.join(fonts_dir, 'arial.ttf')
    if os.path.exists(arial_path):
        pdfmetrics.registerFont(TTFont('Arial', arial_path))

    # Create the PDF document with RTL support and landscape orientation for wider table
    # Convert inches to points (1 inch = 72 points)
    quarter_inch_margin = 0.25 * 72  # 0.25 inch in points (reduced from 0.75 inch)

    # Explicitly use landscape orientation
    page_width, page_height = landscape(A4)

    doc = SimpleDocTemplate(
        response,
        pagesize=(page_width, page_height),  # Explicitly set landscape dimensions
        rightMargin=quarter_inch_margin,  # 0.25 inch right margin
        leftMargin=quarter_inch_margin,   # 0.25 inch left margin
        topMargin=3,                      # Keep small top margin
        bottomMargin=10,                  # Keep small bottom margin for the date
        encoding='UTF-8'                  # Explicitly set UTF-8 encoding
    )

    # Create styles for Arabic text
    styles = getSampleStyleSheet()
    arabic_style = ParagraphStyle(
        'Arabic',
        parent=styles['Normal'],
        fontName='NotoSansArabic',
        fontSize=8,  # Further reduced font size to 8 as requested
        alignment=1,  # Center alignment
        leading=9,   # Further reduced line spacing
        wordWrap=None,  # Disable word wrapping completely
        splitLongWords=0,  # Prevent splitting long words
        spaceAfter=0,  # Remove space after paragraphs
        spaceBefore=0,  # Remove space before paragraphs
    )

    # Create title style
    title_style = ParagraphStyle(
        'Title',
        parent=styles['Title'],
        fontName='NotoSansArabic',
        fontSize=14,  # Reduced font size
        alignment=1,  # Center alignment
        leading=16,  # Reduced line spacing
        spaceAfter=6,  # Reduced space after title
    )

    # Create elements list for the document
    elements = []

    # Add title with proper Arabic reshaping
    if ARABIC_SUPPORT:
        try:
            reshaped_title = arabic_reshaper.reshape(report_title)
            bidi_title = get_display(reshaped_title)
            elements.append(Paragraph(bidi_title, title_style))
        except:
            elements.append(Paragraph(report_title, title_style))
    else:
        elements.append(Paragraph(report_title, title_style))

    # Add a smaller spacer
    elements.append(Spacer(1, 10))

    # We'll create a multi-row header similar to the HTML table
    # First row: Basic columns + leave type headers that span 3 columns each
    # Second row: Detailed balance headers

    # First, create the first row headers (with column spans)
    first_row_headers = ['الرقم الوزاري', 'الاسم', 'القسم']

    # Get leave type names
    annual_name = leave_types[0]['display_name']
    sick_name = leave_types[1]['display_name']
    casual_name = leave_types[2]['display_name']

    # Process first row headers with Arabic reshaping
    first_row = []
    for header in first_row_headers:
        if ARABIC_SUPPORT:
            try:
                reshaped_header = arabic_reshaper.reshape(header)
                bidi_header = get_display(reshaped_header)
                first_row.append(Paragraph(bidi_header, arabic_style))
            except:
                first_row.append(Paragraph(header, arabic_style))
        else:
            first_row.append(Paragraph(header, arabic_style))

    # Create second row headers for balance details
    # For RTL display, we need: initial, used, remaining (from right to left)
    balance_headers = ['الرصيد الأولي', 'الإجازات المستخدمة', 'الرصيد المتبقي']

    # Process second row headers with Arabic reshaping
    second_row_headers = []
    for header in balance_headers:
        if ARABIC_SUPPORT:
            try:
                reshaped_header = arabic_reshaper.reshape(header)
                bidi_header = get_display(reshaped_header)
                second_row_headers.append(Paragraph(bidi_header, arabic_style))
            except:
                second_row_headers.append(Paragraph(header, arabic_style))
        else:
            second_row_headers.append(Paragraph(header, arabic_style))

    # Create a special style for leave type headers with larger font and bold
    leave_type_style = ParagraphStyle(
        'LeaveTypeHeader',
        parent=arabic_style,
        fontSize=10,  # Larger font for leave type headers to make them more visible
        fontName='NotoSansArabic-Bold' if 'NotoSansArabic-Bold' in pdfmetrics._fonts else 'NotoSansArabic',
        alignment=1,  # Center alignment
        leading=12,  # Increased line spacing for better readability
        wordWrap=None,  # Disable word wrapping
        spaceAfter=0,
        spaceBefore=0,
    )

    # Process leave type headers with Arabic reshaping
    # We'll create the headers in the correct order for RTL display
    leave_type_headers = []

    # Create a list of leave type names with their display names
    # For RTL display, we need: annual, sick, casual (from right to left)
    leave_names = [
        {'name': 'annual', 'display': annual_name},
        {'name': 'sick', 'display': sick_name},
        {'name': 'casual', 'display': casual_name}
    ]

    # Process each leave type name
    for leave in leave_names:
        display_text = f"نوع الإجازة: {leave['display']}"
        if ARABIC_SUPPORT:
            try:
                reshaped_name = arabic_reshaper.reshape(display_text)
                bidi_name = get_display(reshaped_name)
                leave_type_headers.append(Paragraph(bidi_name, leave_type_style))
            except:
                leave_type_headers.append(Paragraph(display_text, leave_type_style))
        else:
            leave_type_headers.append(Paragraph(display_text, leave_type_style))

    # We'll create the table data in RTL order directly
    # This means the first column from the right will be ministry number
    data = []

    # First row: Basic columns + leave type headers
    first_row_data = []

    # Add leave type headers in RTL order (annual, sick, casual from right to left)
    for i, leave_header in enumerate(leave_type_headers):
        first_row_data.append(leave_header)  # This will be marked with a span of 3 columns

    # Add basic columns in RTL order (department, name, ministry_number from right to left)
    first_row_data.extend(first_row)

    # Second row: Balance headers
    second_row = []

    # Add balance headers for each leave type in RTL order
    for _ in range(3):  # For annual, sick, casual
        second_row.extend(second_row_headers)

    # Add empty cells for the basic columns that are spanned from first row
    for _ in range(3):  # department, name, ministry_number
        second_row.append('')

    # Add the header rows to the data
    data.append(first_row_data)
    data.append(second_row)

    # Add data rows with processed Arabic text
    for item in employee_data:
        employee = item['employee']

        # Process Arabic text
        if ARABIC_SUPPORT:
            try:
                name = get_display(arabic_reshaper.reshape(employee.full_name))
                department = get_display(arabic_reshaper.reshape(item['department']))
            except:
                name = employee.full_name
                department = item['department']
        else:
            name = employee.full_name
            department = item['department']

        # Create row with data in RTL order
        row = []

        # Add annual leave balance data first (in RTL order: initial, used, remaining)
        row.extend([
            Paragraph(str(item['annual_balance']['initial']), arabic_style),
            Paragraph(str(item['annual_balance']['used']), arabic_style),
            Paragraph(str(item['annual_balance']['remaining']), arabic_style)
        ])

        # Add sick leave balance data second
        row.extend([
            Paragraph(str(item['sick_balance']['initial']), arabic_style),
            Paragraph(str(item['sick_balance']['used']), arabic_style),
            Paragraph(str(item['sick_balance']['remaining']), arabic_style)
        ])

        # Add casual leave balance data third
        row.extend([
            Paragraph(str(item['casual_balance']['initial']), arabic_style),
            Paragraph(str(item['casual_balance']['used']), arabic_style),
            Paragraph(str(item['casual_balance']['remaining']), arabic_style)
        ])

        # Add basic employee data last (in RTL order)
        row.extend([
            Paragraph(department, arabic_style),
            Paragraph(name, arabic_style),
            Paragraph(str(employee.ministry_number), arabic_style)
        ])

        data.append(row)

    # Create table with adjusted column widths for RTL
    # Increase width for leave balance columns and adjust other columns
    # For leave balance columns (9 columns) - redistribute space with more emphasis on leave balances

    # Allocate more space for leave balance columns
    # First, allocate different widths based on column type
    initial_balance_width = 55    # More space for initial balance columns
    used_balance_width = 55       # More space for used balance columns
    remaining_balance_width = 55  # More space for remaining balance columns

    # Create column widths in RTL order

    # For ministry number, name, and department columns
    min_num_width = 50  # Ministry number column width
    name_width = 120    # Name column width as requested
    dept_width = 80     # Department column width as requested

    # Create the widths array for all 9 leave balance columns (3 types × 3 columns)
    leave_balance_widths = []

    # Order: annual, sick, casual (from right to left)
    for _ in range(3):  # For each leave type
        leave_balance_widths.extend([
            initial_balance_width,     # Initial
            used_balance_width,        # Used
            remaining_balance_width    # Remaining
        ])

    # Combine all widths in RTL order
    col_widths = [min_num_width, name_width, dept_width] + leave_balance_widths
    table = Table(data, colWidths=col_widths, repeatRows=1)

    # Style the table with spans for the header rows
    table_style = [
        # Basic styling
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 1), 'NotoSansArabic'),
        ('FONTSIZE', (0, 0), (-1, -1), 8),       # Reduced font size to 8 for all cells
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # Vertical alignment
        ('LEFTPADDING', (0, 0), (-1, -1), 1),    # Minimal padding
        ('RIGHTPADDING', (0, 0), (-1, -1), 1),   # Minimal padding
        ('TOPPADDING', (0, 0), (-1, -1), 1),     # Minimal padding
        ('BOTTOMPADDING', (0, 2), (-1, -1), 1),  # Minimal padding for data rows

        # Header styling
        ('BACKGROUND', (0, 0), (-1, 1), colors.beige),  # Background for both header rows
        ('TEXTCOLOR', (0, 0), (-1, 1), colors.black),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 4),   # Padding for first header row
        ('BOTTOMPADDING', (0, 1), (-1, 1), 4),   # Padding for second header row

        # Data rows styling
        ('BACKGROUND', (0, 2), (-1, -1), colors.white),

        # Column spans for the first row in RTL order
        # The last 3 columns are the basic columns (ministry_number, name, department)
        ('SPAN', (-1, 0), (-1, 1)),  # Ministry number spans 2 rows
        ('SPAN', (-2, 0), (-2, 1)),  # Name spans 2 rows
        ('SPAN', (-3, 0), (-3, 1)),  # Department spans 2 rows

        # Column spans for leave type headers (each spans 3 columns)
        # In RTL order: annual (index 0-2), sick (index 3-5), casual (index 6-8)
        ('SPAN', (0, 0), (2, 0)),  # annual spans 3 columns
        ('SPAN', (3, 0), (5, 0)),  # sick spans 3 columns
        ('SPAN', (6, 0), (8, 0)),  # casual spans 3 columns

        # Add special background for leave type headers to make them stand out
        # Use a more distinctive color for better visibility
        ('BACKGROUND', (0, 0), (2, 0), colors.Color(0.9, 0.9, 0.7)),  # annual - light yellow
        ('BACKGROUND', (3, 0), (5, 0), colors.Color(0.9, 0.9, 0.7)),  # sick - light yellow
        ('BACKGROUND', (6, 0), (8, 0), colors.Color(0.9, 0.9, 0.7)),  # casual - light yellow
    ]

    # Add colors for leave balance columns in the second header row
    # In RTL order: annual (columns 0-2), sick (columns 3-5), casual (columns 6-8)

    # Colors for balance headers in second row - match the HTML template exactly
    # Define exact Bootstrap colors
    # Convert hex to RGB: #28a745 = (40, 167, 69) / 255 = (0.16, 0.65, 0.27)
    success_color = colors.Color(40/255, 167/255, 69/255)  # Bootstrap success color (#28a745)
    # Convert hex to RGB: #dc3545 = (220, 53, 69) / 255 = (0.86, 0.21, 0.27)
    danger_color = colors.Color(220/255, 53/255, 69/255)   # Bootstrap danger color (#dc3545)
    # Convert hex to RGB: #17a2b8 = (23, 162, 184) / 255 = (0.09, 0.64, 0.72)
    info_color = colors.Color(23/255, 162/255, 184/255)    # Bootstrap info color (#17a2b8)

    # Annual leave columns (index 0-2)
    table_style.append(('BACKGROUND', (0, 1), (0, 1), success_color))   # Initial - green
    table_style.append(('BACKGROUND', (1, 1), (1, 1), danger_color))    # Used - red
    table_style.append(('BACKGROUND', (2, 1), (2, 1), info_color))      # Remaining - blue

    # Sick leave columns (index 3-5)
    table_style.append(('BACKGROUND', (3, 1), (3, 1), success_color))   # Initial - green
    table_style.append(('BACKGROUND', (4, 1), (4, 1), danger_color))    # Used - red
    table_style.append(('BACKGROUND', (5, 1), (5, 1), info_color))      # Remaining - blue

    # Casual leave columns (index 6-8)
    table_style.append(('BACKGROUND', (6, 1), (6, 1), success_color))   # Initial - green
    table_style.append(('BACKGROUND', (7, 1), (7, 1), danger_color))    # Used - red
    table_style.append(('BACKGROUND', (8, 1), (8, 1), info_color))      # Remaining - blue

    # Add light background colors for data cells
    # Define exact Bootstrap colors with opacity
    # Convert hex to RGB: #d4edda = (212, 237, 218) / 255 = (0.83, 0.93, 0.85)
    success_light = colors.Color(212/255, 237/255, 218/255)  # Bootstrap bg-success bg-opacity-25 (#d4edda)
    # Convert hex to RGB: #f8d7da = (248, 215, 218) / 255 = (0.97, 0.84, 0.85)
    danger_light = colors.Color(248/255, 215/255, 218/255)   # Bootstrap bg-danger bg-opacity-25 (#f8d7da)
    # Convert hex to RGB: #d1ecf1 = (209, 236, 241) / 255 = (0.82, 0.93, 0.95)
    info_light = colors.Color(209/255, 236/255, 241/255)     # Bootstrap bg-info bg-opacity-25 (#d1ecf1)

    for row in range(2, len(data)):  # Start from the first data row
        # Annual leave columns (index 0-2)
        table_style.append(('BACKGROUND', (0, row), (0, row), success_light))  # Initial
        table_style.append(('BACKGROUND', (1, row), (1, row), danger_light))   # Used
        table_style.append(('BACKGROUND', (2, row), (2, row), info_light))     # Remaining

        # Sick leave columns (index 3-5)
        table_style.append(('BACKGROUND', (3, row), (3, row), success_light))  # Initial
        table_style.append(('BACKGROUND', (4, row), (4, row), danger_light))   # Used
        table_style.append(('BACKGROUND', (5, row), (5, row), info_light))     # Remaining

        # Casual leave columns (index 6-8)
        table_style.append(('BACKGROUND', (6, row), (6, row), success_light))  # Initial
        table_style.append(('BACKGROUND', (7, row), (7, row), danger_light))   # Used
        table_style.append(('BACKGROUND', (8, row), (8, row), info_light))     # Remaining

    table.setStyle(TableStyle(table_style))

    elements.append(table)

    # Add page number and date function
    def add_page_number_and_date(canvas, _):  # Use _ to indicate unused parameter
        # Save the state of our canvas so we can draw on it
        canvas.saveState()

        # Get current date and format it
        current_date = timezone.now().strftime("%d/%m/%Y")

        # Add date at the bottom left with smaller font and closer to edge
        date_text = f"تاريخ التقرير: {current_date}"
        if ARABIC_SUPPORT:
            try:
                reshaped_date = arabic_reshaper.reshape(date_text)
                bidi_date = get_display(reshaped_date)
                canvas.setFont('NotoSansArabic', 6)  # Smaller font
                canvas.drawString(5, 5, bidi_date)   # Closer to edge
            except:
                canvas.setFont('Helvetica', 6)       # Smaller font
                canvas.drawString(5, 5, date_text)  # Closer to edge
        else:
            canvas.setFont('Helvetica', 6)       # Smaller font
            canvas.drawString(5, 5, date_text)  # Closer to edge

        # Add page number at the bottom right with smaller font and closer to edge
        page_num = canvas.getPageNumber()
        canvas.drawRightString(landscape(A4)[0] - 5, 5, str(page_num))  # Closer to edge

        # Release the canvas
        canvas.restoreState()

    # Build the PDF document with page numbers and date
    doc.build(elements, onFirstPage=add_page_number_and_date, onLaterPages=add_page_number_and_date)

    return response
