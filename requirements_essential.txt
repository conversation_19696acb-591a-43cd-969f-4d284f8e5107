# ===== ESSENTIAL REQUIREMENTS FOR HR SYSTEM =====
# متطلبات أساسية لنظام إدارة شؤون الموظفين

# ===== CORE DJANGO FRAMEWORK =====
Django==5.2

# ===== ESSENTIAL PACKAGES =====
# المكتبات الأساسية المطلوبة للنظام

# File Processing & Excel
Pillow==10.1.0              # Image processing
openpyxl==3.1.2             # Excel files (.xlsx)
pandas==2.1.4               # Data analysis
numpy==1.26.2               # Numerical computing

# PDF Generation
reportlab==4.0.9            # PDF creation

# Django Extensions
django-import-export==3.3.1 # Import/Export functionality

# Date & Time
python-dateutil==2.8.2     # Date processing
pytz==2023.3                # Timezone support

# HTTP & Requests
requests==2.31.0            # HTTP library

# Utilities
six==1.16.0                 # Python 2/3 compatibility
humanize==4.8.0             # Human readable numbers

# ===== OPTIONAL BUT RECOMMENDED =====
# مكتبات اختيارية لكن مُوصى بها

# Forms & UI (Optional)
django-crispy-forms==2.1    # Enhanced forms
crispy-bootstrap5==2023.10  # Bootstrap styling
django-widget-tweaks==1.5.0 # Form field customization

# PDF Advanced (Optional)
WeasyPrint==60.2            # HTML to PDF
arabic-reshaper==3.0.0      # Arabic text support
python-bidi==0.4.2          # Bidirectional text

# Excel Advanced (Optional)
xlsxwriter==3.1.9           # Excel writing
xlrd==2.0.1                 # Excel reading (.xls)
xlwt==1.3.0                 # Excel writing (.xls)

# Static Files (Optional)
whitenoise==6.6.0           # Static files serving

# ===== DEVELOPMENT TOOLS (OPTIONAL) =====
# أدوات التطوير (اختيارية)

# Server
gunicorn==21.2.0            # WSGI HTTP Server (for production)

# Monitoring
psutil==5.9.6               # System monitoring
