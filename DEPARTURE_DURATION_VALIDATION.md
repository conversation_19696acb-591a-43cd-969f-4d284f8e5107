# التحقق من مدة المغادرة - الحد الأقصى 4 ساعات
# Departure Duration Validation - Maximum 4 Hours

## ✅ الميزة الجديدة

### 🎯 الهدف:
التأكد من أن مدة المغادرة لا تتجاوز **4 ساعات (240 دقيقة)** في اليوم الواحد.

## 🔧 التحديثات المنفذة

### 1. تحديث النموذج (Model)

#### **دوال جديدة في `leaves/models.py`:**

```python
def calculate_duration_minutes(self):
    """حساب مدة المغادرة بالدقائق"""
    if self.time_from and self.time_to:
        from_minutes = self.time_from.hour * 60 + self.time_from.minute
        to_minutes = self.time_to.hour * 60 + self.time_to.minute
        
        duration_minutes = to_minutes - from_minutes
        
        # معالجة عبور منتصف الليل
        if duration_minutes < 0:
            duration_minutes += 24 * 60
        
        return duration_minutes
    return 0

def is_duration_valid(self):
    """التحقق من أن مدة المغادرة لا تتجاوز 4 ساعات (240 دقيقة)"""
    duration_minutes = self.calculate_duration_minutes()
    return duration_minutes <= 240

def get_duration_hours_minutes(self):
    """الحصول على المدة بصيغة ساعات ودقائق"""
    duration_minutes = self.calculate_duration_minutes()
    hours = duration_minutes // 60
    minutes = duration_minutes % 60
    return hours, minutes
```

### 2. تحديث النموذج (Forms)

#### **التحقق في `leaves/forms.py`:**

```python
def clean(self):
    cleaned_data = super().clean()
    time_from = cleaned_data.get('time_from')
    time_to = cleaned_data.get('time_to')
    
    if time_from and time_to:
        # حساب مدة المغادرة
        from_minutes = time_from.hour * 60 + time_from.minute
        to_minutes = time_to.hour * 60 + time_to.minute
        duration_minutes = to_minutes - from_minutes
        
        if duration_minutes < 0:
            duration_minutes += 24 * 60
        
        # التحقق من الحد الأقصى (240 دقيقة)
        if duration_minutes > 240:
            hours = duration_minutes // 60
            minutes = duration_minutes % 60
            raise forms.ValidationError(
                f'مدة المغادرة لا يجب أن تتجاوز 4 ساعات في اليوم الواحد. '
                f'المدة المدخلة: {hours} ساعة و {minutes} دقيقة '
                f'({duration_minutes} دقيقة). الحد الأقصى المسموح: 240 دقيقة.'
            )
    
    return cleaned_data
```

### 3. تحديث JavaScript

#### **التحقق الفوري في النموذج:**

```javascript
function calculateDuration() {
    // ... حساب المدة
    
    // التحقق من الحد الأقصى (240 دقيقة = 4 ساعات)
    let displayClass = 'badge fs-6 ';
    let displayText = '';
    
    if (durationMinutes > 240) {
        // تجاوز الحد الأقصى - أحمر
        displayClass += 'bg-danger';
        displayText = `<i class="fas fa-exclamation-triangle"></i> ${hours}س ${minutes}د - تجاوز الحد الأقصى!`;
        
        // إضافة تحذير
        showDurationWarning(hours, minutes, durationMinutes);
    } else if (durationMinutes > 180) {
        // قريب من الحد الأقصى - أصفر
        displayClass += 'bg-warning';
        displayText = `<i class="fas fa-clock"></i> ${hours}س ${minutes}د`;
    } else {
        // ضمن الحد المسموح - أخضر
        displayClass += 'bg-success';
        displayText = `<i class="fas fa-check"></i> ${hours}س ${minutes}د`;
    }
}
```

### 4. تحديث العرض في القوالب

#### **أ) قائمة المغادرات:**

```html
{% with hours=departure.get_duration_hours_minutes.0 minutes=departure.get_duration_hours_minutes.1 total_minutes=departure.calculate_duration_minutes %}
    {% if total_minutes > 240 %}
        <span class="badge bg-danger" title="تجاوز الحد الأقصى (4 ساعات)">
            <i class="fas fa-exclamation-triangle"></i> {{ hours }}س {{ minutes }}د
        </span>
    {% elif total_minutes > 180 %}
        <span class="badge bg-warning" title="قريب من الحد الأقصى">
            <i class="fas fa-clock"></i> {{ hours }}س {{ minutes }}د
        </span>
    {% else %}
        <span class="badge bg-success" title="ضمن الحد المسموح">
            <i class="fas fa-check"></i> {{ hours }}س {{ minutes }}د
        </span>
    {% endif %}
    <br><small class="text-muted">({{ departure.calculate_duration_days }} يوم)</small>
{% endwith %}
```

## 🎨 نظام الألوان والتحذيرات

### **الألوان حسب المدة:**

| المدة | اللون | الأيقونة | المعنى |
|-------|-------|---------|--------|
| **0-180 دقيقة** | أخضر (`bg-success`) | `fas fa-check` | ضمن الحد المسموح |
| **181-240 دقيقة** | أصفر (`bg-warning`) | `fas fa-clock` | قريب من الحد الأقصى |
| **241+ دقيقة** | أحمر (`bg-danger`) | `fas fa-exclamation-triangle` | تجاوز الحد الأقصى |

### **رسائل التحذير:**

#### **في النموذج:**
```html
<div class="alert alert-danger">
    <i class="fas fa-exclamation-triangle"></i>
    <strong>تحذير:</strong> مدة المغادرة تتجاوز الحد الأقصى المسموح (4 ساعات = 240 دقيقة).
    <br>المدة الحالية: X ساعة و Y دقيقة (Z دقيقة).
</div>
```

#### **في التفاصيل:**
```html
<small class="text-danger">
    <i class="fas fa-warning"></i> تجاوز الحد الأقصى المسموح (240 دقيقة)
</small>
```

## 📊 أمثلة على التطبيق

### **حالات مقبولة (أخضر):**
| من الساعة | إلى الساعة | المدة | الحالة |
|-----------|------------|-------|--------|
| 08:00 | 11:00 | 3س 0د (180د) | ✅ مقبول |
| 09:00 | 12:00 | 3س 0د (180د) | ✅ مقبول |
| 14:00 | 16:30 | 2س 30د (150د) | ✅ مقبول |

### **حالات تحذيرية (أصفر):**
| من الساعة | إلى الساعة | المدة | الحالة |
|-----------|------------|-------|--------|
| 08:00 | 11:30 | 3س 30د (210د) | ⚠️ قريب من الحد |
| 09:00 | 12:45 | 3س 45د (225د) | ⚠️ قريب من الحد |
| 14:00 | 18:00 | 4س 0د (240د) | ⚠️ الحد الأقصى |

### **حالات مرفوضة (أحمر):**
| من الساعة | إلى الساعة | المدة | الحالة |
|-----------|------------|-------|--------|
| 08:00 | 12:30 | 4س 30د (270د) | ❌ مرفوض |
| 09:00 | 14:00 | 5س 0د (300د) | ❌ مرفوض |
| 08:00 | 16:00 | 8س 0د (480د) | ❌ مرفوض |

## 🔒 مستويات الحماية

### 1. **JavaScript (فوري):**
- تحذير فوري عند تجاوز الحد
- تغيير الألوان حسب المدة
- رسائل تحذيرية واضحة

### 2. **Forms Validation (عند الإرسال):**
- منع حفظ المغادرات التي تتجاوز 4 ساعات
- رسائل خطأ مفصلة
- عرض المدة المدخلة والحد المسموح

### 3. **Model Methods (للتحقق):**
- دوال للتحقق من صحة المدة
- حساب دقيق للمدة بالدقائق
- إرجاع المدة بصيغة ساعات ودقائق

## 🧪 اختبار الميزة

### **خطوات الاختبار:**

1. **افتح نموذج إضافة مغادرة**: http://localhost:8000/leaves/departures/add/

2. **اختبر حالات مختلفة:**
   - **مدة قصيرة**: 09:00 - 11:00 (أخضر)
   - **مدة متوسطة**: 08:00 - 11:30 (أصفر)
   - **مدة طويلة**: 08:00 - 13:00 (أحمر + تحذير)

3. **تحقق من:**
   - تغيير الألوان فوراً
   - ظهور رسائل التحذير
   - منع الحفظ للمدد الطويلة

### **النتائج المتوقعة:**
- ✅ عرض فوري للمدة بالساعات والدقائق
- ✅ ألوان تحذيرية حسب المدة
- ✅ منع حفظ المغادرات التي تتجاوز 4 ساعات
- ✅ رسائل خطأ واضحة ومفيدة

## الملفات المحدثة

1. **`leaves/models.py`**: إضافة دوال التحقق والحساب
2. **`leaves/forms.py`**: إضافة التحقق في النموذج
3. **`templates/leaves/departure_form.html`**: JavaScript للتحقق الفوري
4. **`templates/leaves/departure_list.html`**: عرض محسن للمدة
5. **`templates/leaves/departure_detail.html`**: تفاصيل المدة مع التحذيرات
6. **`templates/leaves/departure_confirm_delete.html`**: عرض المدة في الحذف

## الخلاصة

✅ **تم تطبيق الحد الأقصى 4 ساعات بنجاح**
✅ **حماية متعددة المستويات**
✅ **عرض بصري واضح ومفيد**
✅ **رسائل تحذيرية مفصلة**
✅ **تجربة مستخدم محسنة**

**النظام الآن يمنع تماماً إدخال مغادرات تتجاوز 4 ساعات! 🎉**
