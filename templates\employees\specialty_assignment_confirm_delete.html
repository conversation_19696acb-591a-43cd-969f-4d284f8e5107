{% extends 'base.html' %}
{% load static %}

{% block title %}تأكيد حذف المعلم المحمل على تخصص آخر{% endblock %}

{% block extra_css %}
<style>
    .delete-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        padding: 30px;
        margin-top: 20px;
    }
    .page-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 10px;
    }
    .warning-box {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 5px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .info-item {
        background: #f8f9fa;
        border-left: 4px solid #dc3545;
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 5px;
    }
    .info-label {
        font-weight: bold;
        color: #495057;
        margin-bottom: 5px;
    }
    .info-value {
        color: #212529;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1><i class="fas fa-exclamation-triangle"></i> تأكيد حذف المعلم المحمل على تخصص آخر</h1>
        <p class="lead">هل أنت متأكد من حذف هذا التحميل؟</p>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="delete-container">
                <!-- Warning Message -->
                <div class="warning-box">
                    <h4><i class="fas fa-exclamation-triangle text-warning"></i> تحذير</h4>
                    <p class="mb-0">
                        سيتم حذف جميع بيانات تحميل هذا المعلم على التخصص الآخر نهائياً. 
                        هذا الإجراء لا يمكن التراجع عنه.
                    </p>
                </div>

                <!-- Assignment Details -->
                <h4><i class="fas fa-info-circle"></i> تفاصيل التحميل المراد حذفه</h4>
                
                <div class="info-item">
                    <div class="info-label">المعلم</div>
                    <div class="info-value">{{ assignment.employee.full_name }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">الرقم الوزاري</div>
                    <div class="info-value">{{ assignment.employee.ministry_number }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">التخصص الأصلي</div>
                    <div class="info-value">{{ assignment.original_specialty }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">التخصص المحمل عليه</div>
                    <div class="info-value">{{ assignment.assigned_specialty }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">القسم</div>
                    <div class="info-value">{{ assignment.department }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">المواد التي يدرسها</div>
                    <div class="info-value">{{ assignment.subjects_taught|truncatechars:100 }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">تاريخ التحميل</div>
                    <div class="info-value">{{ assignment.assignment_date|date:"Y-m-d" }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">الحالة</div>
                    <div class="info-value">
                        {% if assignment.is_active %}
                            <span class="badge badge-success">نشط</span>
                        {% else %}
                            <span class="badge badge-secondary">غير نشط</span>
                        {% endif %}
                    </div>
                </div>

                <!-- Confirmation Form -->
                <form method="post" class="mt-4">
                    {% csrf_token %}
                    <div class="text-center">
                        <button type="submit" class="btn btn-danger btn-lg me-3">
                            <i class="fas fa-trash"></i> نعم، احذف التحميل
                        </button>
                        <a href="{% url 'employees:specialty_assignment_detail' assignment.pk %}" 
                           class="btn btn-secondary btn-lg">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>

                <!-- Additional Information -->
                <div class="mt-4">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> ملاحظة مهمة</h5>
                        <ul class="mb-0">
                            <li>حذف التحميل لن يؤثر على بيانات المعلم الأساسية</li>
                            <li>سيتم حذف سجل التحميل على التخصص الآخر فقط</li>
                            <li>يمكن إعادة إضافة المعلم لتخصص آخر في أي وقت</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation dialog
    $('form').on('submit', function(e) {
        if (!confirm('هل أنت متأكد من حذف هذا التحميل؟ لا يمكن التراجع عن هذا الإجراء.')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
