{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل المغادرة - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-plane-departure text-primary"></i> تفاصيل المغادرة</h2>
    <div>
        <a href="{% url 'leaves:departure_update' departure.pk %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'leaves:departure_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle"></i> معلومات المغادرة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user text-primary"></i> الموظف:
                            </label>
                            <p class="form-control-plaintext">
                                <a href="{% url 'employees:employee_detail' departure.employee.pk %}" class="text-decoration-none">
                                    {{ departure.employee.full_name }}
                                </a>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-id-card text-primary"></i> الرقم الوزاري:
                            </label>
                            <p class="form-control-plaintext">{{ departure.employee.ministry_number }}</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-tag text-primary"></i> نوع المغادرة:
                            </label>
                            <p class="form-control-plaintext">
                                {% if departure.departure_type == 'personal' %}
                                    <span class="badge bg-info fs-6"><i class="fas fa-user"></i> خاصة</span>
                                {% else %}
                                    <span class="badge bg-primary fs-6"><i class="fas fa-building"></i> رسمية</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar text-primary"></i> التاريخ:
                            </label>
                            <p class="form-control-plaintext">{{ departure.date|date:"Y/m/d" }}</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-clock text-primary"></i> من الساعة:
                            </label>
                            <p class="form-control-plaintext">{{ departure.time_from }}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-clock text-primary"></i> إلى الساعة:
                            </label>
                            <p class="form-control-plaintext">{{ departure.time_to }}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-hourglass-half text-primary"></i> المدة:
                            </label>
                            <p class="form-control-plaintext">
                                {% with hours=departure.get_duration_hours_minutes.0 minutes=departure.get_duration_hours_minutes.1 total_minutes=departure.calculate_duration_minutes %}
                                    {% if total_minutes > 240 %}
                                        <span class="badge bg-danger fs-6" title="تجاوز الحد الأقصى (4 ساعات)">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            {{ hours }} ساعة و {{ minutes }} دقيقة
                                        </span>
                                        <br>
                                        <small class="text-danger">
                                            <i class="fas fa-warning"></i> تجاوز الحد الأقصى المسموح (240 دقيقة)
                                        </small>
                                    {% elif total_minutes > 180 %}
                                        <span class="badge bg-warning fs-6" title="قريب من الحد الأقصى">
                                            <i class="fas fa-clock"></i>
                                            {{ hours }} ساعة و {{ minutes }} دقيقة
                                        </span>
                                        <br>
                                        <small class="text-warning">
                                            <i class="fas fa-info-circle"></i> قريب من الحد الأقصى (240 دقيقة)
                                        </small>
                                    {% else %}
                                        <span class="badge bg-success fs-6" title="ضمن الحد المسموح">
                                            <i class="fas fa-check"></i>
                                            {{ hours }} ساعة و {{ minutes }} دقيقة
                                        </span>
                                        <br>
                                        <small class="text-success">
                                            <i class="fas fa-check-circle"></i> ضمن الحد المسموح
                                        </small>
                                    {% endif %}
                                    <br>
                                    <small class="text-muted">({{ departure.calculate_duration_days }} يوم)</small>
                                {% endwith %}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-comment text-primary"></i> السبب:
                            </label>
                            <p class="form-control-plaintext">{{ departure.reason|default:"غير محدد" }}</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-check-circle text-primary"></i> الحالة:
                            </label>
                            <p class="form-control-plaintext">
                                {% if departure.status == 'pending' %}
                                    <span class="badge bg-warning fs-6">قيد الانتظار</span>
                                {% elif departure.status == 'approved' %}
                                    <span class="badge bg-success fs-6">موافق عليها</span>
                                {% elif departure.status == 'rejected' %}
                                    <span class="badge bg-danger fs-6">مرفوضة</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-cogs"></i> الإجراءات
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'leaves:departure_update' departure.pk %}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> تعديل المغادرة
                    </a>
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="fas fa-trash"></i> حذف المغادرة
                    </button>
                    <a href="{% url 'leaves:departure_list' %}" class="btn btn-secondary">
                        <i class="fas fa-list"></i> قائمة المغادرات
                    </a>
                </div>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-building"></i> معلومات القسم
                </h6>
            </div>
            <div class="card-body">
                <p><strong>القسم:</strong> {{ departure.employee.department.name|default:"غير محدد" }}</p>
                <p><strong>المنصب:</strong> {{ departure.employee.position|default:"غير محدد" }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal للحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-danger"></i> تأكيد الحذف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذه المغادرة؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="post" action="{% url 'leaves:departure_delete' departure.pk %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
