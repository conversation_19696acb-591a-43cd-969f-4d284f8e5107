# تحديثات تقارير الإجازات - البحث والتحسينات
# Leave Reports Updates - Search and Improvements

## ✅ التحديثات المنفذة

### 🎯 الأهداف المحققة:
1. **إضافة شريط البحث** للبحث عن الموظفين
2. **تعديل حقل المتبقي** ليكون رقماً صحيحاً (عدا الإجازات السنوية)
3. **طرح المغادرات مباشرة** بعد إضافة أي مغادرة للموظف

## 🔍 1. إضافة شريط البحث

### **في القالب (`leave_reports.html`):**

#### **شريط البحث في رأس البطاقة:**
```html
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-chart-bar"></i> أرصدة الإجازات للموظفين - {{ current_year }}
        </h6>
        <small class="text-muted">
            <i class="fas fa-info-circle"></i> 
            يتم طرح المغادرات من رصيد الإجازات السنوية (كل 420 دقيقة = يوم واحد)
        </small>
    </div>
    <div class="d-flex align-items-center">
        <div class="input-group" style="width: 300px;">
            <span class="input-group-text">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" class="form-control" id="employeeSearch" 
                   placeholder="البحث عن موظف..." autocomplete="off">
        </div>
    </div>
</div>
```

#### **JavaScript للبحث:**
```javascript
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#dataTable').DataTable({
        "language": {
            "url": "{% static 'vendor/datatables/ar.json' %}"
        },
        "order": [[0, "asc"]],
        "pageLength": 25,
        "searching": false, // Disable default search
        "dom": 'lrtip' // Remove default search box
    });

    // Custom search functionality
    $('#employeeSearch').on('keyup', function() {
        var searchValue = this.value;
        
        // Search in the first column (employee name)
        table.column(0).search(searchValue).draw();
        
        // Update search info
        updateSearchInfo(searchValue, table);
    });

    // Function to update search information
    function updateSearchInfo(searchValue, table) {
        var info = table.page.info();
        var searchInfo = document.getElementById('searchInfo');
        
        if (!searchInfo) {
            // Create search info element if it doesn't exist
            searchInfo = document.createElement('div');
            searchInfo.id = 'searchInfo';
            searchInfo.className = 'alert alert-info mt-2';
            document.querySelector('.card-body').insertBefore(searchInfo, document.querySelector('.table-responsive'));
        }
        
        if (searchValue) {
            searchInfo.innerHTML = `
                <i class="fas fa-search"></i> 
                نتائج البحث عن: "<strong>${searchValue}</strong>" - 
                عرض ${info.recordsDisplay} من ${info.recordsTotal} موظف
            `;
            searchInfo.style.display = 'block';
        } else {
            searchInfo.style.display = 'none';
        }
    }

    // Clear search on escape key
    $('#employeeSearch').on('keydown', function(e) {
        if (e.key === 'Escape') {
            this.value = '';
            table.search('').draw();
            updateSearchInfo('', table);
        }
    });
});
```

#### **CSS للتصميم:**
```css
/* Search box styling */
#employeeSearch {
    border-radius: 0.375rem;
    border: 1px solid #d1d3e2;
    transition: all 0.3s ease;
}

#employeeSearch:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.input-group-text {
    background-color: #f8f9fc;
    border-color: #d1d3e2;
    color: #5a5c69;
}

/* Search info styling */
#searchInfo {
    margin-bottom: 1rem;
    border-left: 4px solid #36b9cc;
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}
```

## 🔢 2. تعديل حقل المتبقي

### **في القالب:**

#### **قبل التحديث:**
```html
<td class="bg-info bg-opacity-25 fw-bold fs-5">{{ balance.remaining|floatformat:2 }}</td>
```

#### **بعد التحديث:**
```html
{% if balance.is_annual %}
    <!-- للإجازات السنوية: عرض الرقم العشري -->
    <td class="bg-info bg-opacity-25 fw-bold fs-5">{{ balance.remaining|floatformat:2 }}</td>
{% else %}
    <!-- للإجازات الأخرى: عرض رقم صحيح -->
    <td class="bg-info bg-opacity-25 fw-bold fs-5">{{ balance.remaining|floatformat:0 }}</td>
{% endif %}
```

### **النتيجة:**
| نوع الإجازة | الرصيد المتبقي | التنسيق |
|-------------|----------------|---------|
| **إجازة سنوية** | 17.5 | عشري (floatformat:2) |
| **إجازة مرضية** | 10 | صحيح (floatformat:0) |
| **إجازة طارئة** | 5 | صحيح (floatformat:0) |

## ⚡ 3. طرح المغادرات مباشرة

### **في النموذج (`models.py`):**

#### **دالة الحفظ المحدثة:**
```python
def save(self, *args, **kwargs):
    """حفظ النموذج مع تحديث رصيد الإجازات السنوية"""
    is_new = self.pk is None
    super().save(*args, **kwargs)
    
    # تحديث رصيد الإجازات السنوية بعد حفظ المغادرة
    if is_new and self.status == 'approved':
        self.update_annual_leave_balance()

def update_annual_leave_balance(self):
    """تحديث رصيد الإجازات السنوية للموظف"""
    from django.utils import timezone
    
    try:
        # البحث عن نوع الإجازة السنوية
        annual_leave_type = LeaveType.objects.filter(
            name__icontains='سنوية'
        ).first()
        
        if annual_leave_type:
            # البحث عن رصيد الإجازة السنوية للموظف
            leave_balance, created = LeaveBalance.objects.get_or_create(
                employee=self.employee,
                leave_type=annual_leave_type,
                year=self.date.year,
                defaults={'initial_balance': 30}  # رصيد افتراضي
            )
            
            # حساب مجموع المغادرات المعتمدة للموظف في نفس السنة
            total_departures = Departure.objects.filter(
                employee=self.employee,
                date__year=self.date.year,
                status='approved'
            ).exclude(pk=self.pk)  # استثناء المغادرة الحالية
            
            departures_days = sum(d.calculate_duration_days() for d in total_departures)
            departures_days += self.calculate_duration_days()  # إضافة المغادرة الحالية
            
    except Exception as e:
        # في حالة حدوث خطأ، نسجله ولا نوقف العملية
        import logging
        logging.error(f"Error updating annual leave balance: {e}")
```

#### **دالة الحذف المحدثة:**
```python
def delete(self, *args, **kwargs):
    """حذف المغادرة مع تحديث رصيد الإجازات"""
    if self.status == 'approved':
        # حفظ معلومات المغادرة قبل الحذف
        employee = self.employee
        year = self.date.year
        duration = self.calculate_duration_days()
        
        # حذف المغادرة
        super().delete(*args, **kwargs)
        
        # تحديث رصيد الإجازات بعد الحذف
        self._update_balance_after_deletion(employee, year, duration)
    else:
        super().delete(*args, **kwargs)
```

### **في Views (`views.py`):**

#### **تحديث دالة التعديل:**
```python
@login_required
def departure_update(request, pk):
    """تعديل المغادرة"""
    departure = get_object_or_404(Departure, pk=pk)
    old_status = departure.status  # حفظ الحالة القديمة
    
    if request.method == 'POST':
        # ... منطق التحديث
        
        # تحديث رصيد الإجازات إذا تغيرت الحالة إلى معتمدة
        if old_status != 'approved' and updated_departure.status == 'approved':
            updated_departure.save()
            updated_departure.update_annual_leave_balance()
        else:
            updated_departure.save()
```

## 🎯 الميزات الجديدة

### 1. **البحث المتقدم:**
- بحث فوري في أسماء الموظفين
- عرض نتائج البحث مع العدد
- إمكانية مسح البحث بالضغط على Escape
- تصميم أنيق ومتجاوب

### 2. **عرض محسن للأرقام:**
- **الإجازات السنوية**: أرقام عشرية (17.50)
- **الإجازات الأخرى**: أرقام صحيحة (10)
- وضوح أكبر في القراءة

### 3. **تحديث تلقائي للرصيد:**
- طرح المغادرات فور اعتمادها
- تحديث الرصيد عند تغيير حالة المغادرة
- إعادة حساب الرصيد عند حذف المغادرة

### 4. **معالجة الأخطاء:**
- حماية من الأخطاء أثناء تحديث الرصيد
- تسجيل الأخطاء للمراجعة
- عدم توقف العملية في حالة الخطأ

## 🧪 للاختبار

### **1. اختبار البحث:**
1. افتح صفحة التقارير: http://localhost:8000/leaves/reports/
2. اكتب في شريط البحث اسم موظف
3. لاحظ تحديث النتائج فوراً
4. اضغط Escape لمسح البحث

### **2. اختبار عرض الأرقام:**
1. تحقق من عمود "المتبقي"
2. الإجازات السنوية: أرقام عشرية
3. الإجازات الأخرى: أرقام صحيحة

### **3. اختبار التحديث التلقائي:**
1. أضف مغادرة جديدة لموظف
2. اعتمد المغادرة (status = approved)
3. تحقق من تقرير الإجازات
4. يجب أن يظهر طرح المغادرة من الرصيد

### **4. اختبار الحذف:**
1. احذف مغادرة معتمدة
2. تحقق من تقرير الإجازات
3. يجب أن يعود الرصيد كما كان

## الملفات المحدثة

1. **`templates/leaves/leave_reports.html`**:
   - إضافة شريط البحث
   - تحديث عرض الأرقام
   - إضافة JavaScript للبحث
   - إضافة CSS للتصميم

2. **`leaves/models.py`**:
   - تحديث دالة save للمغادرات
   - إضافة دالة update_annual_leave_balance
   - تحديث دالة delete
   - معالجة الأخطاء

3. **`leaves/views.py`**:
   - تحديث دالة departure_update
   - إضافة منطق تحديث الرصيد

## الخلاصة

✅ **شريط البحث فعال ومتجاوب**
✅ **عرض الأرقام محسن ومناسب**
✅ **تحديث تلقائي للرصيد عند إضافة/حذف المغادرات**
✅ **معالجة شاملة للأخطاء**
✅ **تجربة مستخدم محسنة**

**النظام الآن يوفر تقارير دقيقة ومحدثة تلقائياً مع إمكانية البحث السريع! 🎉**
