"""
إعدادات خاصة لتوافق Django 5.2 مع MariaDB 10.4
"""

from django.db.backends.mysql.base import DatabaseWrapper as MySQLDatabaseWrapper
from django.db.backends.mysql.features import DatabaseFeatures as MySQLDatabaseFeatures


class MariaDB104Features(MySQLDatabaseFeatures):
    """
    ميزات قاعدة البيانات المخصصة لـ MariaDB 10.4
    """
    # تعطيل ميزة RETURNING غير المدعومة في MariaDB 10.4
    can_return_columns_from_insert = False
    can_return_rows_from_bulk_insert = False
    supports_over_clause = False
    supports_frame_range_clause = False
    supports_aggregate_filter_clause = False
    supports_json_field = False
    supports_json_field_contains = False
    supports_primitives_in_json_field = False
    supports_json_field_exact_lookup = False
    
    # إعدادات أخرى للتوافق
    supports_transactions = True
    supports_foreign_keys = True
    supports_check_constraints = True
    autocommits_when_autocommit_is_off = True
    can_introspect_foreign_keys = True
    can_introspect_check_constraints = False


class MariaDB104DatabaseWrapper(MySQLDatabaseWrapper):
    """
    Database wrapper مخصص لـ MariaDB 10.4
    """
    features_class = MariaDB104Features
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تعطيل autocommit للتوافق
        self.features.autocommits_when_autocommit_is_off = False


# تطبيق الإعدادات
def patch_mysql_backend():
    """
    تطبيق التعديلات على MySQL backend
    """
    import django.db.backends.mysql.base as mysql_base
    mysql_base.DatabaseWrapper = MariaDB104DatabaseWrapper
