from django.db import models
from django.utils.translation import gettext_lazy as _
from employees.models import Employee
from employment.models import Department

class LeaveType(models.Model):
    """Model for storing leave types"""
    ANNUAL = 'annual'
    SICK = 'sick'
    MEDICAL_COMMITTEE = 'medical_committee'
    CASUAL = 'casual'
    PERSONAL_DEPARTURE = 'personal_departure'
    OFFICIAL_DEPARTURE = 'official_departure'
    UNPAID = 'unpaid'
    HAJJ = 'hajj'
    MATERNITY = 'maternity'
    PATERNITY = 'paternity'
    HUSBAND_DEATH = 'husband_death'
    WIFE_DEATH = 'wife_death'

    TYPE_CHOICES = [
        (ANNUAL, _('سنوية')),
        (SICK, _('مرضية')),
        (MEDICAL_COMMITTEE, _('لجان طبية')),
        (CASUAL, _('عرضية')),
        (PERSONAL_DEPARTURE, _('مغادرة خاصة')),
        (OFFICIAL_DEPARTURE, _('مغادرة رسمية')),
        (UNPAID, _('بدون راتب')),
        (HAJJ, _('اجازة الحج')),
        (MATERNITY, _('امومة')),
        (PATERNITY, _('الابوة')),
        (HUSBAND_DEATH, _('وفاة الزوج')),
        (WIFE_DEATH, _('وفاة الزوجة')),
    ]

    name = models.CharField(_('Name'), max_length=50, choices=TYPE_CHOICES, unique=True)
    description = models.TextField(_('Description'), blank=True, null=True)
    max_days_per_year = models.PositiveIntegerField(_('Maximum Days Per Year'), default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Leave Type')
        verbose_name_plural = _('Leave Types')

    def __str__(self):
        return self.get_name_display()

class LeaveBalance(models.Model):
    """Model for storing employee leave balances"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='leave_balances')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE, related_name='employee_balances')
    year = models.PositiveIntegerField(_('Year'))
    initial_balance = models.PositiveIntegerField(_('Initial Balance'))
    used_balance = models.PositiveIntegerField(_('Used Balance'), default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def remaining_balance(self):
        """Calculate remaining balance based on actual approved leaves"""
        from django.db.models import Sum

        # حساب الإجازات المستخدمة من جدول الإجازات مباشرة
        used_leaves = Leave.objects.filter(
            employee=self.employee,
            leave_type=self.leave_type,
            start_date__year=self.year,
            status='approved'
        ).aggregate(total_days=Sum('days_count'))

        used_days = used_leaves['total_days'] or 0
        remaining = self.initial_balance - used_days
        return remaining if remaining >= 0 else 0

    @property
    def actual_used_balance(self):
        """Calculate actual used balance from approved leaves"""
        from django.db.models import Sum

        used_leaves = Leave.objects.filter(
            employee=self.employee,
            leave_type=self.leave_type,
            start_date__year=self.year,
            status='approved'
        ).aggregate(total_days=Sum('days_count'))

        return used_leaves['total_days'] or 0

    class Meta:
        verbose_name = _('Leave Balance')
        verbose_name_plural = _('Leave Balances')
        unique_together = ['employee', 'leave_type', 'year']

    def __str__(self):
        return f"{self.employee.full_name} - {self.leave_type} - {self.year}"

class Leave(models.Model):
    """Model for storing employee leaves"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='leaves')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE, related_name='leaves')
    start_date = models.DateField(_('Start Date'))
    end_date = models.DateField(_('End Date'))
    days_count = models.PositiveIntegerField(_('Days Count'))
    reason = models.TextField(_('Reason'), blank=True, null=True)
    status = models.CharField(_('Status'), max_length=20, choices=[
        ('pending', _('قيد الانتظار')),
        ('approved', _('مقبولة')),
        ('rejected', _('مرفوضة')),
    ], default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Leave')
        verbose_name_plural = _('Leaves')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.leave_type} - {self.start_date}"

class Departure(models.Model):
    """Model for storing employee departures"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='departures')
    departure_type = models.CharField(_('Departure Type'), max_length=20, choices=[
        ('personal', _('خاصة')),
        ('official', _('رسمية')),
    ])
    date = models.DateField(_('Date'))
    time_from = models.TimeField(_('Time From'))
    time_to = models.TimeField(_('Time To'))
    reason = models.TextField(_('Reason'), blank=True, null=True)
    status = models.CharField(_('Status'), max_length=20, choices=[
        ('pending', _('قيد الانتظار')),
        ('approved', _('مقبولة')),
        ('rejected', _('مرفوضة')),
    ], default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Departure')
        verbose_name_plural = _('Departures')
        ordering = ['-date']

    def calculate_duration_minutes(self):
        """حساب مدة المغادرة بالدقائق"""
        if self.time_from and self.time_to:
            # تحويل الأوقات إلى دقائق
            from_minutes = self.time_from.hour * 60 + self.time_from.minute
            to_minutes = self.time_to.hour * 60 + self.time_to.minute

            # حساب الفرق بالدقائق
            duration_minutes = to_minutes - from_minutes

            # إذا كان الوقت النهائي أقل من البداية (عبور منتصف الليل)
            if duration_minutes < 0:
                duration_minutes += 24 * 60  # إضافة 24 ساعة

            return duration_minutes
        return 0

    def calculate_duration_days(self):
        """حساب مدة المغادرة بالأيام (كل 420 دقيقة = يوم واحد)"""
        duration_minutes = self.calculate_duration_minutes()
        if duration_minutes > 0:
            # تحويل إلى أيام (كل 420 دقيقة = يوم واحد)
            duration_days = duration_minutes / 420
            return round(duration_days, 2)
        return 0

    def is_duration_valid(self):
        """التحقق من أن مدة المغادرة لا تتجاوز 4 ساعات (240 دقيقة)"""
        duration_minutes = self.calculate_duration_minutes()
        return duration_minutes <= 240

    def get_duration_hours_minutes(self):
        """الحصول على المدة بصيغة ساعات ودقائق"""
        duration_minutes = self.calculate_duration_minutes()
        hours = duration_minutes // 60
        minutes = duration_minutes % 60
        return hours, minutes

    def save(self, *args, **kwargs):
        """حفظ النموذج مع تحديث رصيد الإجازات السنوية"""
        is_new = self.pk is None
        super().save(*args, **kwargs)

        # تحديث رصيد الإجازات السنوية بعد حفظ المغادرة
        if is_new and self.status == 'approved':
            self.update_annual_leave_balance()

    def update_annual_leave_balance(self):
        """تحديث رصيد الإجازات السنوية للموظف"""
        from django.utils import timezone

        try:
            # البحث عن نوع الإجازة السنوية
            annual_leave_type = LeaveType.objects.filter(
                name__icontains='سنوية'
            ).first()

            if annual_leave_type:
                # البحث عن رصيد الإجازة السنوية للموظف
                leave_balance, created = LeaveBalance.objects.get_or_create(
                    employee=self.employee,
                    leave_type=annual_leave_type,
                    year=self.date.year,
                    defaults={'initial_balance': 30}  # رصيد افتراضي
                )

                # حساب مجموع المغادرات المعتمدة للموظف في نفس السنة
                total_departures = Departure.objects.filter(
                    employee=self.employee,
                    date__year=self.date.year,
                    status='approved'
                ).exclude(pk=self.pk)  # استثناء المغادرة الحالية

                departures_days = sum(d.calculate_duration_days() for d in total_departures)
                departures_days += self.calculate_duration_days()  # إضافة المغادرة الحالية

                # تحديث الرصيد المستخدم (لا نحفظ المغادرات في used_balance، فقط للعرض)
                # الحساب سيتم في التقارير

        except Exception as e:
            # في حالة حدوث خطأ، نسجله ولا نوقف العملية
            import logging
            logging.error(f"Error updating annual leave balance: {e}")

    def delete(self, *args, **kwargs):
        """حذف المغادرة مع تحديث رصيد الإجازات"""
        if self.status == 'approved':
            # حفظ معلومات المغادرة قبل الحذف
            employee = self.employee
            year = self.date.year
            duration = self.calculate_duration_days()

            # حذف المغادرة
            super().delete(*args, **kwargs)

            # تحديث رصيد الإجازات بعد الحذف
            self._update_balance_after_deletion(employee, year, duration)
        else:
            super().delete(*args, **kwargs)

    def _update_balance_after_deletion(self, employee, year, duration):
        """تحديث الرصيد بعد حذف المغادرة"""
        try:
            annual_leave_type = LeaveType.objects.filter(
                name__icontains='سنوية'
            ).first()

            if annual_leave_type:
                leave_balance = LeaveBalance.objects.filter(
                    employee=employee,
                    leave_type=annual_leave_type,
                    year=year
                ).first()

                if leave_balance:
                    # إعادة حساب المغادرات المتبقية
                    remaining_departures = Departure.objects.filter(
                        employee=employee,
                        date__year=year,
                        status='approved'
                    )

                    total_departures_days = sum(d.calculate_duration_days() for d in remaining_departures)

        except Exception as e:
            import logging
            logging.error(f"Error updating balance after deletion: {e}")

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_departure_type_display()} - {self.date}"
