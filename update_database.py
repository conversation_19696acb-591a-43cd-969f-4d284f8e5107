"""
Manual database update script for adding duration_days column
Run this in Django shell: python manage.py shell < update_database.py
"""

from django.db import connection

def update_database():
    with connection.cursor() as cursor:
        try:
            # Check if column exists
            cursor.execute("PRAGMA table_info(leaves_departure)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'duration_days' not in columns:
                print("Adding duration_days column...")
                
                # Add the column
                cursor.execute("""
                    ALTER TABLE leaves_departure 
                    ADD COLUMN duration_days DECIMAL(5,2) NULL
                """)
                
                print("Column added successfully!")
                
                # Update existing records
                print("Updating existing records...")
                
                # Get all departures and update them
                from leaves.models import Departure
                departures = Departure.objects.all()
                
                for departure in departures:
                    if departure.time_from and departure.time_to:
                        duration = departure.calculate_duration_days()
                        cursor.execute(
                            "UPDATE leaves_departure SET duration_days = ? WHERE id = ?",
                            [duration, departure.id]
                        )
                
                print(f"Updated {departures.count()} records")
                
            else:
                print("Column already exists!")
                
        except Exception as e:
            print(f"Error: {e}")

# Run the update
update_database()
print("Database update completed!")
