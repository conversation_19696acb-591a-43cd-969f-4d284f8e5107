{% extends 'base.html' %}
{% load static %}

{% block title %}الدرجات الوظيفية{% endblock %}

{% block extra_css %}

<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
    /* تنسيق البطاقات مثل لوحة التحكم */
    .dashboard-card {
        border-right: 4px solid;
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
        background-color: white;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }

    .dashboard-card .card-body {
        padding: 1.5rem;
    }

    .dashboard-card .text-xs {
        font-size: 0.85rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        display: block;
    }

    .dashboard-card .h3 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0;
    }

    /* تنسيق الأيقونات */
    .dashboard-card .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    .dashboard-card .icon-circle i {
        font-size: 1.8rem;
    }

    /* ألوان البطاقات */
    .card-primary {
        border-color: #4e73df;
    }

    .card-primary .text-xs {
        color: #4e73df;
    }

    .card-primary .icon-circle {
        background-color: rgba(78, 115, 223, 0.1);
        color: #4e73df;
    }

    .card-success {
        border-color: #1cc88a;
    }

    .card-success .text-xs {
        color: #1cc88a;
    }

    .card-success .icon-circle {
        background-color: rgba(28, 200, 138, 0.1);
        color: #1cc88a;
    }

    .card-info {
        border-color: #36b9cc;
    }

    .card-info .text-xs {
        color: #36b9cc;
    }

    .card-info .icon-circle {
        background-color: rgba(54, 185, 204, 0.1);
        color: #36b9cc;
    }

    .card-warning {
        border-color: #f6c23e;
    }

    .card-warning .text-xs {
        color: #f6c23e;
    }

    .card-warning .icon-circle {
        background-color: rgba(246, 194, 62, 0.1);
        color: #f6c23e;
    }

    /* تنسيق عام */
    .fade-in {
        animation: fadeIn 0.6s ease-in-out;
    }

    .search-active #searchInput {
        background-color: #fff3cd;
        border-color: #ffc107;
    }

    .table-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        padding: 20px;
        margin-top: 20px;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 10px;
    }

    .btn-group .btn {
        margin-right: 10px;
        margin-bottom: 10px;
    }

    .grade-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
    }

    .grade-1 { background-color: #d4edda; color: #155724; }
    .grade-2 { background-color: #d1ecf1; color: #0c5460; }
    .grade-3 { background-color: #fff3cd; color: #856404; }
    .grade-4 { background-color: #f8d7da; color: #721c24; }
    .grade-5 { background-color: #e2e3e5; color: #383d41; }
    .grade-special { background-color: #f0e68c; color: #8b7355; }

    .form-label.small {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
    }

    .form-label.small i {
        margin-left: 5px;
    }

    .card-header.bg-light {
        background-color: #f8f9fa !important;
        border-bottom: 1px solid #dee2e6;
    }

    .card-header h6 {
        color: #495057;
        font-weight: 600;
    }

    .card-header h6 i {
        margin-left: 8px;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .text-muted {
        color: #6c757d !important;
    }

    .table-responsive {
        border-radius: 0.375rem;
    }

    #gradesTable {
        margin-bottom: 0;
    }

    #gradesTable th {
        background-color: #343a40;
        color: white;
        border-color: #454d55;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        padding: 12px 8px;
    }

    #gradesTable td {
        text-align: center;
        vertical-align: middle;
        padding: 12px 8px;
        border-color: #dee2e6;
    }

    #gradesTable tbody tr:hover {
        background-color: rgba(0,123,255,.075);
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.2rem;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1><i class="fas fa-medal"></i> الدرجات الوظيفية للموظفين</h1>
        <p class="lead">إدارة وتتبع الدرجات الوظيفية لجميع الموظفين</p>

        <!-- Action Buttons -->
        <div class="mt-4">
            <a href="{% url 'employees:employee_grade_create' %}" class="btn btn-success btn-lg me-3">
                <i class="fas fa-plus"></i> إضافة درجة وظيفية
            </a>
            <a href="{% url 'employees:job_grades_list' %}" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-cogs"></i> الدرجات
            </a>
            <a href="{% url 'employees:promotion_types_list' %}" class="btn btn-info btn-lg me-3">
                <i class="fas fa-arrow-up"></i> نوع الترفيع
            </a>
            <a href="{% url 'employees:export_employee_grades_excel' %}{% if search_query %}?search={{ search_query }}{% endif %}" class="btn btn-warning btn-lg me-3" id="exportExcelBtn">
                <i class="fas fa-file-excel"></i> تصدير إلى Excel
            </a>
            <a href="{% url 'employees:employee_list' %}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-arrow-left"></i> العودة لبيانات الموظفين
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card card-primary fade-in" style="animation-delay: 0.1s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-xs text-uppercase">إجمالي الدرجات</span>
                            <div class="h3 text-dark">{{ total_grades|default:0 }}</div>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-medal"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card card-success fade-in" style="animation-delay: 0.2s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-xs text-uppercase">الدرجات المعروضة</span>
                            <div class="h3 text-dark">{{ grades|length|default:0 }}</div>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-list"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card card-warning fade-in" style="animation-delay: 0.3s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-xs text-uppercase">عدد الصفحات</span>
                            <div class="h3 text-dark">{% if page_obj %}{{ page_obj.paginator.num_pages }}{% else %}1{% endif %}</div>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card card-info fade-in" style="animation-delay: 0.4s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-xs text-uppercase">الموظفين المختلفين</span>
                            <div class="h3 text-dark">{{ unique_employees|default:0 }}</div>
                        </div>
                        <div class="icon-circle">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h6 class="mb-0">
                <i class="fas fa-filter"></i> البحث والفلترة
            </h6>
        </div>
        <div class="card-body py-3">
            <form method="GET" id="filterForm">
                <div class="row g-3">
                    <!-- Search Input -->
                    <div class="col-md-4">
                        <label class="form-label small">
                            <i class="fas fa-search text-primary"></i> البحث العام
                        </label>
                        <input type="text" name="search" class="form-control form-control-sm"
                               placeholder="ابحث بالاسم، الرقم الوزاري، التخصص..."
                               value="{{ search_query|default:'' }}" id="searchInput">
                    </div>

                    <!-- Grade Filter -->
                    <div class="col-md-3">
                        <label class="form-label small">
                            <i class="fas fa-medal text-info"></i> الدرجة الوظيفية
                        </label>
                        <select class="form-select form-select-sm select2-filter" name="grade"
                                data-placeholder="جميع الدرجات">
                            <option value="">جميع الدرجات</option>
                            <option value="grade_9">الدرجة التاسعة</option>
                            <option value="grade_8">الدرجة الثامنة</option>
                            <option value="grade_7">الدرجة السابعة</option>
                            <option value="grade_6">الدرجة السادسة</option>
                            <option value="grade_5">الدرجة الخامسة</option>
                            <option value="grade_4">الدرجة الرابعة</option>
                            <option value="grade_3">الدرجة الثالثة</option>
                            <option value="grade_2">الدرجة الثانية</option>
                            <option value="grade_1">الدرجة الأولى</option>
                            <option value="special_grade">الدرجة الخاصة</option>
                        </select>
                    </div>

                    <!-- Department Filter -->
                    <div class="col-md-3">
                        <label class="form-label small">
                            <i class="fas fa-building text-success"></i> القسم
                        </label>
                        <select class="form-select form-select-sm select2-filter" name="department"
                                data-placeholder="جميع الأقسام">
                            <option value="">جميع الأقسام</option>
                            {% for dept in departments %}
                                <option value="{{ dept }}">{{ dept }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Action Buttons -->
                    <div class="col-md-2">
                        <label class="form-label small text-white">.</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary btn-sm flex-fill">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="{% url 'employees:employee_grades_list' %}" class="btn btn-outline-secondary btn-sm flex-fill">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Grades Table -->
    <div class="table-container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
                <i class="fas fa-list"></i>
                قائمة الدرجات الوظيفية
                {% if search_query %}
                <small class="text-muted">(نتائج البحث عن: "{{ search_query }}")</small>
                {% endif %}
            </h5>
            <div class="text-muted small">
                إجمالي النتائج: {{ total_grades }}
            </div>
        </div>

        <div id="gradesTableContainer">
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2 for filters
    $('.select2-filter').select2({
        theme: 'bootstrap-5',
        width: '100%',
        allowClear: true
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // AJAX search functionality
    let searchTimeout;

    function performSearch() {
        const searchQuery = $('#searchInput').val();
        const gradeFilter = $('select[name="grade"]').val();
        const departmentFilter = $('select[name="department"]').val();

        // Show loading indicator
        $('#gradesTableContainer').html('<div class="text-center py-5"><i class="fas fa-spinner fa-spin fa-2x text-primary"></i><br><span class="text-muted mt-2">جاري البحث...</span></div>');

        $.ajax({
            url: '{% url "employees:employee_grades_list" %}',
            type: 'GET',
            data: {
                'search': searchQuery,
                'grade': gradeFilter,
                'department': departmentFilter,
                'ajax': '1'
            },
            success: function(response) {
                $('#gradesTableContainer').html(response);
                // Re-initialize tooltips for new content
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            },
            error: function() {
                $('#gradesTableContainer').html('<div class="alert alert-danger text-center">حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.</div>');
            }
        });
    }

    // Search on input with debounce
    $('#searchInput').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(performSearch, 500);
    });

    // Search on filter change
    $('select[name="grade"], select[name="department"]').on('change', function() {
        performSearch();
    });

    // Prevent form submission
    $('#filterForm').on('submit', function(e) {
        e.preventDefault();
        performSearch();
    });

    // Function to load specific page
    window.loadPage = function(pageNumber) {
        const searchQuery = $('#searchInput').val();
        const gradeFilter = $('select[name="grade"]').val();
        const departmentFilter = $('select[name="department"]').val();

        // Show loading indicator
        $('#gradesTableContainer').html('<div class="text-center py-5"><i class="fas fa-spinner fa-spin fa-2x text-primary"></i><br><span class="text-muted mt-2">جاري التحميل...</span></div>');

        $.ajax({
            url: '{% url "employees:employee_grades_list" %}',
            type: 'GET',
            data: {
                'search': searchQuery,
                'grade': gradeFilter,
                'department': departmentFilter,
                'page': pageNumber,
                'ajax': '1'
            },
            success: function(response) {
                $('#gradesTableContainer').html(response);
                // Re-initialize tooltips for new content
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            },
            error: function() {
                $('#gradesTableContainer').html('<div class="alert alert-danger text-center">حدث خطأ أثناء التحميل. يرجى المحاولة مرة أخرى.</div>');
            }
        });
    };

    // Load initial data
    performSearch();

    // Search functionality
    $('#searchInput').on('input', function() {
        const searchValue = $(this).val();
        if (searchValue.length > 0) {
            $('#filterForm').addClass('search-active');
        } else {
            $('#filterForm').removeClass('search-active');
        }
    });

    // Auto-submit form on filter change
    $('.select2-filter').on('change', function() {
        $('#filterForm').submit();
    });

    // Export Excel functionality with loading state
    $('#exportExcelBtn').click(function(e) {
        const btn = $(this);
        const originalText = btn.html();

        // Show loading state
        btn.html('<i class="fas fa-spinner fa-spin"></i> جاري التصدير...');
        btn.prop('disabled', true);

        // Reset button after 3 seconds
        setTimeout(function() {
            btn.html(originalText);
            btn.prop('disabled', false);
        }, 3000);
    });

    // Fade in animation for cards
    $('.fade-in').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
    });
});
</script>
{% endblock %}
