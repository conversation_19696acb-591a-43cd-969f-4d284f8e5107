# استبدال إجازات الأمومة بالإجازات العرضية
# Replace Maternity Leave with Casual Leave

## ✅ التحديث المنفذ

### 🎯 الهدف:
استبدال إجازات الأمومة بالإجازات العرضية في صفحة تقارير الإجازات.

## 🔧 التغيير المطبق

### **في `leaves/views.py` - دالة `leave_reports`:**

#### **قبل التحديث:**
```python
# استثناء الإجازة بدون راتب
leave_types = LeaveType.objects.exclude(name='unpaid')
```

#### **بعد التحديث:**
```python
# استثناء الإجازة بدون راتب وإجازات الأمومة
leave_types = LeaveType.objects.exclude(name__in=['unpaid', 'maternity'])
```

### **النتيجة:**
- ❌ **إجازات الأمومة**: لن تظهر في التقرير
- ✅ **الإجازات العرضية**: ستظهر في التقرير (إذا كانت موجودة في قاعدة البيانات)

## 📊 أنواع الإجازات المعروضة الآن

### **الإجازات المعروضة في التقرير:**
| نوع الإجازة | الاسم في النظام | الحالة |
|-------------|----------------|--------|
| **إجازة سنوية** | `annual` | ✅ معروضة |
| **إجازة مرضية** | `sick` | ✅ معروضة |
| **إجازة عرضية** | `casual` | ✅ معروضة |
| **إجازة الحج** | `hajj` | ✅ معروضة |
| **إجازة وفاة** | `bereavement` | ✅ معروضة |
| **إجازة الأبوة** | `paternity` | ✅ معروضة |

### **الإجازات المستثناة:**
| نوع الإجازة | الاسم في النظام | السبب |
|-------------|----------------|--------|
| **إجازة بدون راتب** | `unpaid` | ❌ لها نظام منفصل |
| **إجازة الأمومة** | `maternity` | ❌ مستبدلة بالعرضية |

## 🎨 التصميم في الجدول

### **الجدول المحدث:**
| الرقم الوزاري | الموظف | إجازة سنوية | إجازة مرضية | **إجازة عرضية** | إجازة الحج |
|---------------|--------|-------------|-------------|----------------|-----------|
| | | الرصيد \| المستخدم \| المغادرات \| المتبقي | الرصيد \| المستخدم \| المتبقي | **الرصيد \| المستخدم \| المتبقي** | الرصيد \| المستخدم \| المتبقي |

### **خصائص الإجازات العرضية:**
- ✅ **عرض الرصيد**: الرصيد الأولي المخصص
- ✅ **عرض المستخدم**: الإجازات المأخوذة
- ✅ **عرض المتبقي**: بأرقام صحيحة (floatformat:0)
- ✅ **لا تتأثر بالمغادرات**: المغادرات تُطرح من السنوية فقط

## 🔍 الفرق بين الإجازات

### **الإجازات السنوية (مميزة):**
- 🟣 **عمود المغادرات**: يظهر مجموع المغادرات
- 🔢 **أرقام عشرية**: المتبقي بكسور (مثل: 17.25)
- ➖ **طرح المغادرات**: من الرصيد المتبقي

### **الإجازات العرضية (عادية):**
- 🚫 **لا عمود مغادرات**: لا يظهر عمود المغادرات
- 🔢 **أرقام صحيحة**: المتبقي بدون كسور (مثل: 15)
- ➕ **لا طرح مغادرات**: المغادرات لا تؤثر عليها

### **مقارنة سريعة:**
| الخاصية | الإجازة السنوية | الإجازة العرضية |
|---------|-----------------|-----------------|
| **عمود المغادرات** | ✅ يظهر | ❌ لا يظهر |
| **تنسيق المتبقي** | عشري (17.25) | صحيح (15) |
| **تأثر بالمغادرات** | ✅ نعم | ❌ لا |
| **اللون** | 🟣 بنفسجي للمغادرات | 🔵 أزرق للمتبقي |

## 🧪 للاختبار

### **خطوات التحقق:**
1. **افتح صفحة التقارير**: http://localhost:8000/leaves/reports/
2. **تحقق من الأعمدة**:
   - ✅ يجب أن تظهر "إجازة عرضية"
   - ❌ يجب ألا تظهر "إجازة الأمومة"
3. **تحقق من التنسيق**:
   - الإجازة العرضية: 3 أعمدة (الرصيد، المستخدم، المتبقي)
   - الإجازة السنوية: 4 أعمدة (الرصيد، المستخدم، المغادرات، المتبقي)
4. **تحقق من الأرقام**:
   - الإجازة العرضية: أرقام صحيحة في المتبقي
   - الإجازة السنوية: أرقام عشرية في المتبقي

### **النتائج المتوقعة:**
```
✅ إجازة عرضية تظهر في الجدول
❌ إجازة الأمومة لا تظهر في الجدول
✅ الإجازة العرضية بتنسيق صحيح (3 أعمدة)
✅ الأرقام المتبقية للعرضية صحيحة (بدون كسور)
```

## 📋 ملاحظات مهمة

### **1. البيانات الموجودة:**
- إذا كان هناك موظفين لديهم رصيد إجازات أمومة، فلن تظهر في التقرير
- الإجازات العرضية ستظهر فقط إذا كان لديها رصيد في قاعدة البيانات

### **2. إضافة رصيد للإجازات العرضية:**
إذا لم تظهر الإجازات العرضية، يمكن إضافة رصيد لها من:
- **صفحة الإدارة**: Django Admin
- **صفحة إدارة الأرصدة**: في النظام
- **إضافة يدوية**: في قاعدة البيانات

### **3. التأثير على التقارير الأخرى:**
- هذا التغيير يؤثر فقط على صفحة تقارير الإجازات
- صفحات الإجازات الأخرى لا تتأثر
- إجازات الأمومة ما زالت موجودة في النظام

## 🔄 إذا أردت التراجع

### **لإعادة إجازات الأمومة:**
```python
# في leaves/views.py
# استثناء الإجازة بدون راتب فقط
leave_types = LeaveType.objects.exclude(name='unpaid')
```

### **لاستثناء الإجازات العرضية بدلاً من الأمومة:**
```python
# في leaves/views.py
# استثناء الإجازة بدون راتب والإجازات العرضية
leave_types = LeaveType.objects.exclude(name__in=['unpaid', 'casual'])
```

## الملفات المحدثة

1. **`leaves/views.py`**:
   - تحديث دالة `leave_reports`
   - إضافة استثناء إجازات الأمومة
   - الحفاظ على استثناء الإجازة بدون راتب

## الخلاصة

✅ **تم استبدال إجازات الأمومة بالإجازات العرضية بنجاح**
✅ **الإجازات العرضية تظهر بتنسيق صحيح (أرقام صحيحة)**
✅ **إجازات الأمومة لا تظهر في التقرير**
✅ **باقي الإجازات تعمل بشكل طبيعي**
✅ **التغيير بسيط وقابل للتراجع**

**التقرير الآن يعرض الإجازات العرضية بدلاً من إجازات الأمومة! 🎉**
