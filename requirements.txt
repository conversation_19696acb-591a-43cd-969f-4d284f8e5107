# HR System Requirements
# نظام شؤون الموظفين - متطلبات النظام الكاملة
# HR Management System - Complete Requirements

# ===== CORE DJANGO FRAMEWORK =====
Django==5.2

# ===== AUTHENTICATION & SECURITY =====
django-allauth==0.57.0
django-guardian==2.4.0
django-crispy-forms==2.1
crispy-bootstrap5==2023.10
django-widget-tweaks==1.5.0

# ===== DATABASE SUPPORT =====
# SQLite (built-in with Python)
psycopg2-binary==2.9.9  # PostgreSQL support
mysqlclient==2.2.4      # MySQL support

# ===== FILE HANDLING & MEDIA =====
Pillow==10.1.0           # Image processing
django-storages==1.14.2  # Cloud storage support
boto3==1.34.0            # AWS S3 support
botocore==1.34.0

# ===== EXCEL & DATA PROCESSING =====
openpyxl==3.1.2          # Excel files (.xlsx)
xlsxwriter==3.1.9        # Excel writing
xlrd==2.0.1              # Excel reading (.xls)
xlwt==1.3.0              # Excel writing (.xls)
pandas==2.1.4            # Data analysis
numpy==1.26.2            # Numerical computing

# ===== PDF GENERATION =====
reportlab==4.0.9         # PDF creation
WeasyPrint==60.2         # HTML to PDF
xhtml2pdf==0.2.13        # Alternative PDF generator
arabic-reshaper==3.0.0   # Arabic text support
python-bidi==0.4.2       # Bidirectional text

# ===== DATE & TIME =====
python-dateutil==2.8.2
pytz==2023.3

# ===== HTTP & API =====
requests==2.31.0
urllib3==2.1.0
djangorestframework==3.14.0
django-cors-headers==4.3.1

# ===== FORMS & UI =====
django-filter==23.5
django-tables2==2.7.0
django-bootstrap5==23.3

# ===== CACHING & PERFORMANCE =====
redis==5.0.1
django-redis==5.4.0

# ===== BACKGROUND TASKS =====
celery==5.3.4
kombu==5.3.4
django-rq==2.8.1

# ===== DEVELOPMENT TOOLS =====
django-debug-toolbar==4.2.0
django-silk==5.0.4
black==23.11.0
isort==5.12.0
flake8==6.1.0

# ===== TESTING =====
pytest==7.4.3
pytest-django==4.7.0
factory-boy==3.3.0

# ===== MONITORING & LOGGING =====
sentry-sdk==1.38.0

# ===== BACKUP & MIGRATION =====
django-dbbackup==4.0.2
django-import-export==3.3.1

# ===== EMAIL =====
django-ses==3.5.0

# ===== ENVIRONMENT & CONFIG =====
python-dotenv==1.0.0
python-decouple==3.8

# ===== UTILITIES =====
six==1.16.0
certifi==2023.11.17
charset-normalizer==3.3.2
idna==3.6
humanize==4.8.0
django-mptt==0.15.0

# ===== ADDITIONAL UTILITIES =====
whitenoise==6.6.0           # Static files serving
django-extensions==3.2.3    # Django extensions
gunicorn==21.2.0            # WSGI HTTP Server
psutil==5.9.6               # System monitoring

# ===== DOCUMENTATION =====
Sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# ===== ADDITIONAL DEPENDENCIES =====
# These are often installed as dependencies but listed for completeness
setuptools>=65.0.0          # Package management
wheel>=0.38.0               # Package building
pip>=22.0.0                 # Package installer

