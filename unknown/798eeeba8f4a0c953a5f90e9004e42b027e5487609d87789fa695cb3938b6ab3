import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

# Import User model
from accounts.models import User

# Check if admin user exists
if User.objects.filter(username='admin').exists():
    admin_user = User.objects.get(username='admin')
    print(f"Admin user already exists: {admin_user.username}")
    
    # Update admin user
    admin_user.is_admin = True
    admin_user.is_staff = True
    admin_user.is_superuser = True
    admin_user.set_password('admin123')
    admin_user.save()
    print(f"Admin user updated with password 'admin123'")
else:
    # Create new admin user
    admin_user = User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        is_admin=True
    )
    print(f"New admin user created: {admin_user.username}")

print(f"Admin user details:")
print(f"Username: {admin_user.username}")
print(f"Is admin: {admin_user.is_admin}")
print(f"Is staff: {admin_user.is_staff}")
print(f"Is superuser: {admin_user.is_superuser}")
