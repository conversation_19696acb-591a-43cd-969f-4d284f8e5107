#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script to check if all required dependencies are installed.
This script will:
1. Read the requirements.txt file
2. Check if each package is installed
3. Report any missing packages
"""

import sys
import subprocess
import pkg_resources
import platform
import os

def print_header(message):
    print(f"\n=== {message} ===\n")

def print_success(message):
    print(f"✓ {message}")

def print_warning(message):
    print(f"⚠ {message}")

def print_error(message):
    print(f"✗ {message}")

def print_info(message):
    print(f"ℹ {message}")

def get_installed_packages():
    """Get a dictionary of installed packages and their versions"""
    installed_packages = {}
    for package in pkg_resources.working_set:
        installed_packages[package.key] = package.version
    return installed_packages

def parse_requirements(filename):
    """Parse the requirements file and return a list of required packages"""
    required_packages = []
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # Handle package with version specifier
            if '==' in line:
                package_name, version = line.split('==', 1)
                required_packages.append((package_name.lower(), version))
            else:
                required_packages.append((line.lower(), None))
    
    return required_packages

def check_dependencies():
    """Check if all required dependencies are installed"""
    print_header("Checking Dependencies")
    
    # Check if requirements.txt exists
    if not os.path.exists('requirements.txt'):
        print_error("requirements.txt file not found")
        return False
    
    # Get installed packages
    installed_packages = get_installed_packages()
    
    # Parse requirements
    required_packages = parse_requirements('requirements.txt')
    
    # Check each required package
    missing_packages = []
    outdated_packages = []
    
    for package_name, required_version in required_packages:
        # Skip comments and empty lines
        if not package_name or package_name.startswith('#'):
            continue
        
        # Handle package with comment
        if '#' in package_name:
            package_name = package_name.split('#')[0].strip()
        
        # Check if package is installed
        if package_name.lower() not in installed_packages:
            missing_packages.append((package_name, required_version))
            continue
        
        # Check version if specified
        if required_version:
            installed_version = installed_packages[package_name.lower()]
            if installed_version != required_version:
                outdated_packages.append((package_name, installed_version, required_version))
    
    # Report results
    if not missing_packages and not outdated_packages:
        print_success("All required packages are installed with correct versions")
        return True
    
    if missing_packages:
        print_error(f"Missing packages ({len(missing_packages)}):")
        for package_name, required_version in missing_packages:
            version_str = f" (version {required_version})" if required_version else ""
            print(f"  - {package_name}{version_str}")
    
    if outdated_packages:
        print_warning(f"Outdated packages ({len(outdated_packages)}):")
        for package_name, installed_version, required_version in outdated_packages:
            print(f"  - {package_name}: installed {installed_version}, required {required_version}")
    
    # Suggest fix
    print_info("\nTo install missing or update outdated packages, run:")
    print("  pip install -r requirements.txt")
    
    return False

def main():
    """Main function"""
    print_header("Dependency Checker for HR System")
    
    # Check Python version
    python_version = platform.python_version()
    print_info(f"Python version: {python_version}")
    
    major, minor, *_ = python_version.split(".")
    if int(major) < 3 or (int(major) == 3 and int(minor) < 8):
        print_warning(f"Python {python_version} is installed, but version 3.8 or higher is recommended")
    
    # Check dependencies
    check_dependencies()
    
    # Keep the terminal open on Windows
    if platform.system() == "Windows":
        print("\nPress Enter to exit...")
        input()

if __name__ == "__main__":
    main()
