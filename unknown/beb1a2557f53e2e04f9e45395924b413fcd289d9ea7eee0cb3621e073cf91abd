import os
import django
import sqlite3
import sys

print("Starting database check and fix...")

# Check if database file exists
if not os.path.exists('db.sqlite3'):
    print("Database file not found. Please run 'python manage.py migrate' to create the database.")
    sys.exit(1)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

# Connect to the database
conn = sqlite3.connect('db.sqlite3')
cursor = conn.cursor()

# Check if tables exist
try:
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    table_names = [table[0] for table in tables]
    print(f"Found {len(table_names)} tables in the database.")

    # Check for required tables
    required_tables = [
        'accounts_user',
        'employees_employee',
        'file_management_filemovement',
        'leaves_leave',
        'ranks_employeerank',
        'system_logs_systemlog'
    ]

    for table in required_tables:
        if table not in table_names:
            print(f"WARNING: Required table '{table}' not found in database!")

    # Check if file_id column exists in file_management_filemovement
    if 'file_management_filemovement' in table_names:
        cursor.execute("PRAGMA table_info(file_management_filemovement)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        print(f"Columns in file_management_filemovement: {column_names}")

        # Add file_id column if it doesn't exist
        if 'file_id' not in column_names:
            print("Adding file_id column to file_management_filemovement table...")
            try:
                cursor.execute("ALTER TABLE file_management_filemovement ADD COLUMN file_id integer REFERENCES file_management_file(id)")
                conn.commit()
                print("Column added successfully!")
            except Exception as e:
                print(f"Error adding column: {e}")
        else:
            print("file_id column already exists.")
    else:
        print("Table file_management_filemovement not found. Skipping column check.")

    # Check for admin user in accounts_user table
    if 'accounts_user' in table_names:
        cursor.execute("SELECT COUNT(*) FROM accounts_user WHERE username='admin'")
        admin_count = cursor.fetchone()[0]
        print(f"Found {admin_count} admin users in the database.")
        if admin_count == 0:
            print("WARNING: No admin user found! Please run 'python create_admin.py' to create an admin user.")
    else:
        print("Table accounts_user not found. Cannot check for admin user.")

except Exception as e:
    print(f"Error checking database: {e}")

# Close the connection
conn.close()

print("Database check and fix completed.")
