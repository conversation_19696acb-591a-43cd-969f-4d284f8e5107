from django.http import HttpResponse, Http404
from django.core.exceptions import PermissionDenied, SuspiciousOperation
from django.shortcuts import render

def test_404(request):
    """Test 404 error page"""
    context = {
        'error_code': '404',
        'error_title': 'الصفحة غير موجودة',
        'error_message': 'يبدو أن الصفحة التي تبحث عنها غير موجودة أو تم نقلها.'
    }
    return render(request, 'errors/error.html', context, status=404)

def test_500(request):
    """Test 500 error page"""
    context = {
        'error_code': '500',
        'error_title': 'خطأ في النظام',
        'error_message': 'حدث خطأ غير متوقع في النظام. يرجى المحاولة مرة أخرى أو الاتصال بمسؤول النظام.'
    }
    return render(request, 'errors/error.html', context, status=500)

def test_403(request):
    """Test 403 error page"""
    context = {
        'error_code': '403',
        'error_title': 'غير مصرح بالوصول',
        'error_message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة.'
    }
    return render(request, 'errors/error.html', context, status=403)

def test_400(request):
    """Test 400 error page"""
    context = {
        'error_code': '400',
        'error_title': 'طلب غير صحيح',
        'error_message': 'الطلب الذي قمت به غير صحيح أو غير مكتمل.'
    }
    return render(request, 'errors/error.html', context, status=400)
