import sys
import os

print("Python version:", sys.version)
print("Python executable:", sys.executable)
print("Current directory:", os.getcwd())

try:
    import django
    print("Django version:", django.get_version())
    
    # Try to import some modules from the project
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
        django.setup()
        print("Django setup successful")
        
        # Try to import some models
        try:
            from django.contrib.auth.models import User
            print("Django auth models imported successfully")
        except Exception as e:
            print("Error importing Django auth models:", e)
            
        # Try to import project-specific models
        try:
            from employees.models import Employee
            print("Project models imported successfully")
        except Exception as e:
            print("Error importing project models:", e)
            
    except Exception as e:
        print("Error setting up Django:", e)
        
except ImportError:
    print("Django is not installed")
    
print("<PERSON><PERSON><PERSON> completed")
