import re

# قراءة محتوى الملف
with open('system_logs/views.py', 'r', encoding='utf-8') as file:
    content = file.read()

# تعديل الدالة الأولى system_log_list
pattern1 = r'(def system_log_list.*?# Base queryset\s*\n\s*logs = SystemLog\.objects\.all\(\))'
replacement1 = r'\1\n\n    # استبعاد سجلات العرض\n    logs = logs.exclude(action=SystemLog.VIEW)'

# تعديل الدالة الثانية get_system_logs_ajax
pattern2 = r'(def get_system_logs_ajax.*?# Base queryset\s*\n\s*logs = SystemLog\.objects\.all\(\))'
replacement2 = r'\1\n\n    # استبعاد سجلات العرض\n    logs = logs.exclude(action=SystemLog.VIEW)'

# تطبيق التعديلات
modified_content = re.sub(pattern1, replacement1, content, flags=re.DOTALL)
modified_content = re.sub(pattern2, replacement2, modified_content, flags=re.DOTALL)

# كتابة المحتوى المعدل إلى الملف
with open('system_logs/views.py', 'w', encoding='utf-8') as file:
    file.write(modified_content)

print("تم تعديل الملف بنجاح!")
