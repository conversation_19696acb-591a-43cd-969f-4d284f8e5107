#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script to set up the environment for the HR System.
This script will:
1. Check if Python is installed
2. Create a virtual environment if it doesn't exist
3. Install all required packages from requirements.txt
4. Check if the database is properly configured
5. Run migrations if needed
6. Create a superuser if needed
"""

import os
import sys
import subprocess
import platform
import time

# ANSI colors for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_header(message):
    print(f"\n{Colors.HEADER}{Colors.BOLD}=== {message} ==={Colors.ENDC}\n")

def print_success(message):
    print(f"{Colors.GREEN}✓ {message}{Colors.ENDC}")

def print_warning(message):
    print(f"{Colors.WARNING}⚠ {message}{Colors.ENDC}")

def print_error(message):
    print(f"{Colors.FAIL}✗ {message}{Colors.ENDC}")

def print_info(message):
    print(f"{Colors.BLUE}ℹ {message}{Colors.ENDC}")

def run_command(command, shell=True):
    """Run a command and return its output and success status"""
    try:
        result = subprocess.run(
            command,
            shell=shell,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def check_python():
    """Check if Python is installed and its version"""
    print_header("Checking Python Installation")
    
    success, output = run_command("python --version")
    if not success:
        success, output = run_command("py --version")
        if not success:
            print_error("Python is not installed or not in PATH")
            print_info("Please install Python 3.8 or higher from https://www.python.org/downloads/")
            return False, None
    
    python_command = "py" if "py --version" else "python"
    version = output.strip().split(" ")[1]
    print_success(f"Python {version} is installed")
    
    # Check if version is 3.8 or higher
    major, minor, *_ = version.split(".")
    if int(major) < 3 or (int(major) == 3 and int(minor) < 8):
        print_warning(f"Python {version} is installed, but version 3.8 or higher is recommended")
    
    return True, python_command

def setup_virtual_env(python_command):
    """Set up a virtual environment if it doesn't exist"""
    print_header("Setting up Virtual Environment")
    
    venv_dir = "venv"
    if os.path.exists(venv_dir):
        print_info(f"Virtual environment already exists at {venv_dir}")
        return True
    
    print_info("Creating virtual environment...")
    success, output = run_command(f"{python_command} -m venv {venv_dir}")
    if not success:
        print_error(f"Failed to create virtual environment: {output}")
        return False
    
    print_success(f"Virtual environment created at {venv_dir}")
    return True

def install_requirements(python_command):
    """Install required packages from requirements.txt"""
    print_header("Installing Required Packages")
    
    # Determine the pip command based on the environment
    if os.path.exists("venv"):
        if platform.system() == "Windows":
            pip_command = os.path.join("venv", "Scripts", "pip")
        else:
            pip_command = os.path.join("venv", "bin", "pip")
    else:
        pip_command = f"{python_command} -m pip"
    
    # Upgrade pip
    print_info("Upgrading pip...")
    success, output = run_command(f"{pip_command} install --upgrade pip")
    if not success:
        print_warning(f"Failed to upgrade pip: {output}")
    else:
        print_success("Pip upgraded successfully")
    
    # Install requirements
    print_info("Installing required packages from requirements.txt...")
    success, output = run_command(f"{pip_command} install -r requirements.txt")
    if not success:
        print_error(f"Failed to install required packages: {output}")
        return False
    
    print_success("All required packages installed successfully")
    return True

def check_database():
    """Check if the database is properly configured"""
    print_header("Checking Database Configuration")
    
    # Determine the python command based on the environment
    if os.path.exists("venv"):
        if platform.system() == "Windows":
            python_command = os.path.join("venv", "Scripts", "python")
        else:
            python_command = os.path.join("venv", "bin", "python")
    
    # Check if database exists
    if os.path.exists("db.sqlite3"):
        print_success("Database file exists")
    else:
        print_warning("Database file does not exist, will be created during migrations")
    
    # Check if migrations are needed
    print_info("Checking if migrations are needed...")
    success, output = run_command(f"{python_command} manage.py showmigrations")
    if not success:
        print_error(f"Failed to check migrations: {output}")
        return False
    
    # Run migrations if needed
    print_info("Running migrations...")
    success, output = run_command(f"{python_command} manage.py migrate")
    if not success:
        print_error(f"Failed to run migrations: {output}")
        return False
    
    print_success("Database is properly configured")
    return True

def create_superuser():
    """Create a superuser if needed"""
    print_header("Checking Superuser")
    
    # Determine the python command based on the environment
    if os.path.exists("venv"):
        if platform.system() == "Windows":
            python_command = os.path.join("venv", "Scripts", "python")
        else:
            python_command = os.path.join("venv", "bin", "python")
    
    # Check if superuser exists
    print_info("Checking if superuser exists...")
    
    # Run the create_admin.py script if it exists
    if os.path.exists("create_admin.py"):
        print_info("Running create_admin.py script...")
        success, output = run_command(f"{python_command} create_admin.py")
        if not success:
            print_error(f"Failed to run create_admin.py: {output}")
            print_info("You can create a superuser manually by running: python manage.py createsuperuser")
            return False
        
        print_success("Superuser check completed")
        return True
    else:
        print_warning("create_admin.py script not found")
        print_info("You can create a superuser manually by running: python manage.py createsuperuser")
        return True

def main():
    """Main function to set up the environment"""
    print_header("HR System Environment Setup")
    
    # Check Python
    python_success, python_command = check_python()
    if not python_success:
        sys.exit(1)
    
    # Setup virtual environment
    if not setup_virtual_env(python_command):
        sys.exit(1)
    
    # Install requirements
    if not install_requirements(python_command):
        sys.exit(1)
    
    # Check database
    if not check_database():
        sys.exit(1)
    
    # Create superuser
    create_superuser()
    
    print_header("Setup Complete")
    print_success("The HR System environment has been successfully set up!")
    print_info("You can now run the system with: python manage.py runserver")
    print_info("Or use the provided batch files to start the server.")
    
    # Keep the terminal open on Windows
    if platform.system() == "Windows":
        print("\nPress Enter to exit...")
        input()

if __name__ == "__main__":
    main()
