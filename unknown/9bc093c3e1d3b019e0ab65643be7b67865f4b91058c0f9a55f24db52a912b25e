import re

# قراءة محتوى الملف
with open('system_logs/views.py', 'r', encoding='utf-8') as file:
    content = file.read()

# تعديل الكود الذي يقوم بإنشاء قائمة actions_data
pattern = r'(actions_data = \[\]\s*\n\s*for action_choice in SystemLog\.LOG_TYPE_CHOICES:.*?if SystemLog\.objects\.filter\(action=action_choice\[0\]\)\.exists\(\):.*?actions_data\.append\({\s*\n\s*\'value\': action_choice\[0\],\s*\n\s*\'display\': action_choice\[1\]\s*\n\s*}\))'
replacement = r'actions_data = []\n    for action_choice in SystemLog.LOG_TYPE_CHOICES:\n        # استبعاد نوع الإجراء "عرض" من قائمة التصفية\n        if action_choice[0] != SystemLog.VIEW and SystemLog.objects.filter(action=action_choice[0]).exists():\n            actions_data.append({\n                \'value\': action_choice[0],\n                \'display\': action_choice[1]\n            })'

# تطبيق التعديلات
modified_content = re.sub(pattern, replacement, content, flags=re.DOTALL)

# كتابة المحتوى المعدل إلى الملف
with open('system_logs/views.py', 'w', encoding='utf-8') as file:
    file.write(modified_content)

print("تم تعديل قائمة تصفية الإجراءات بنجاح!")
