import re

# قراءة محتوى الملف
with open('employees/views.py', 'r', encoding='utf-8') as file:
    content = file.read()

# تعديل الكود الذي يقوم بحساب عدد سجلات النظام
pattern = r'(system_log_count = SystemLog\.objects\.count\(\))'
replacement = r'system_log_count = SystemLog.objects.exclude(action=SystemLog.VIEW).count()'

# تطبيق التعديلات
modified_content = re.sub(pattern, replacement, content, flags=re.DOTALL)

# كتابة المحتوى المعدل إلى الملف
with open('employees/views.py', 'w', encoding='utf-8') as file:
    file.write(modified_content)

print("تم تعديل عدد سجلات النظام في لوحة التحكم بنجاح!")
