{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
    .form-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    .employee-info-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        display: none;
    }
    .employee-info-card.show {
        display: block;
        animation: fadeInUp 0.5s ease;
    }
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    .btn-custom {
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    .btn-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .search-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        border: 2px dashed #dee2e6;
    }
    .alert-info {
        border-radius: 10px;
        border: none;
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
    }
    .loading-spinner {
        display: none;
    }
    .loading-spinner.show {
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-share-alt text-primary me-2"></i>
                        {{ title }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'employees:shared_employees_list' %}">المعلمين المشتركين</a></li>
                            <li class="breadcrumb-item active">{{ title }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'employees:shared_employees_list' %}" class="btn btn-secondary btn-custom">
                        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                <form method="post" id="sharedEmployeeForm">
                    {% csrf_token %}
                    
                    <!-- Employee Search Section -->
                    <div class="search-section">
                        <h5 class="mb-3">
                            <i class="fas fa-search text-primary me-2"></i>
                            البحث عن الموظف
                        </h5>
                        <div class="row">
                            <div class="col-md-8">
                                <label for="{{ form.ministry_number.id_for_label }}" class="form-label">
                                    {{ form.ministry_number.label }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" name="ministry_number" class="form-control" id="id_ministry_number" placeholder="أدخل الرقم الوزاري واضغط Enter أو زر البحث" required>
                                {% if form.ministry_number.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.ministry_number.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" id="searchEmployeeBtn" class="btn btn-primary btn-custom w-100">
                                    <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status"></span>
                                    <i class="fas fa-search me-2"></i>بحث
                                </button>
                            </div>
                        </div>
                        
                        <!-- Employee Name Display -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <label for="{{ form.employee_name.id_for_label }}" class="form-label">
                                    {{ form.employee_name.label }}
                                </label>
                                <input type="text" name="employee_name" class="form-control" id="id_employee_name" readonly placeholder="سيتم عرض اسم الموظف هنا بعد البحث" value="{{ form.employee_name.value|default:'' }}" style="background-color: #f8f9fa; font-weight: 600; color: #495057;">
                            </div>
                        </div>
                    </div>

                    <!-- Employee Information Card -->
                    <div class="employee-info-card" id="employeeInfoCard">
                        <h5 class="mb-3">
                            <i class="fas fa-user text-white me-2"></i>
                            معلومات الموظف
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>الرقم الوزاري:</strong> <span id="displayMinistryNumber"></span>
                            </div>
                            <div class="col-md-6">
                                <strong>الاسم الكامل:</strong> <span id="displayFullName"></span>
                            </div>
                            <div class="col-md-6 mt-2">
                                <strong>التخصص:</strong> <span id="displaySpecialization"></span>
                            </div>
                            <div class="col-md-6 mt-2">
                                <strong>المدرسة الحالية:</strong> <span id="displaySchool"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden Fields -->
                    {{ form.employee_id }}
                    {{ form.employee }}

                    <!-- Sharing Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <label for="{{ form.original_department.id_for_label }}" class="form-label">
                                {{ form.original_department.label }}
                            </label>
                            {{ form.original_department }}
                            {% if form.original_department.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.original_department.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.shared_department.id_for_label }}" class="form-label">
                                {{ form.shared_department.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.shared_department }}
                            {% if form.shared_department.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.shared_department.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="{{ form.sharing_date.id_for_label }}" class="form-label">
                                {{ form.sharing_date.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.sharing_date }}
                            {% if form.sharing_date.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.sharing_date.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger mt-3">
                            {{ form.non_field_errors.0 }}
                        </div>
                    {% endif %}

                    <!-- Submit Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{% url 'employees:shared_employees_list' %}" class="btn btn-secondary btn-custom">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary btn-custom">
                                    <i class="fas fa-save me-2"></i>حفظ
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Check if we're editing an existing record
    const isEditing = $('#id_employee_name').val().trim() !== '';

    if (isEditing) {
        // Show employee info card if editing
        $('#employeeInfoCard').addClass('show');
        // Style the employee name field for editing
        $('#id_employee_name').css({
            'background-color': '#d4edda',
            'border-color': '#c3e6cb',
            'color': '#155724'
        });
    }
    // Setup CSRF token for AJAX requests
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    const csrftoken = getCookie('csrftoken');

    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!(/^http:.*/.test(settings.url) || /^https:.*/.test(settings.url))) {
                xhr.setRequestHeader("X-CSRFToken", csrftoken);
            }
        }
    });

    // Initialize Select2 for shared department
    $('#id_shared_department').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر القسم المشترك...',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "لا توجد نتائج مطابقة";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });

    // Search employee functionality
    $('#searchEmployeeBtn').click(function() {
        const ministryNumber = $('#id_ministry_number').val().trim();
        
        if (!ministryNumber) {
            alert('الرجاء إدخال الرقم الوزاري');
            return;
        }

        const $btn = $(this);
        const $spinner = $btn.find('.loading-spinner');
        
        // Show loading state
        $btn.prop('disabled', true);
        $spinner.addClass('show');

        $.ajax({
            url: '{% url "employees:search_employee_for_sharing" %}',
            method: 'GET',
            data: {
                'ministry_number': ministryNumber
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    const employee = response.employee;

                    // Fill form fields with visual feedback
                    $('#id_employee_name').val(employee.full_name).css({
                        'background-color': '#d4edda',
                        'border-color': '#c3e6cb',
                        'color': '#155724'
                    });
                    $('#id_employee_id').val(employee.id);
                    $('#id_employee').val(employee.id);
                    $('#id_original_department').val(employee.school);

                    // Display employee info
                    $('#displayMinistryNumber').text(employee.ministry_number);
                    $('#displayFullName').text(employee.full_name);
                    $('#displaySpecialization').text(employee.specialization);
                    $('#displaySchool').text(employee.school);

                    // Show employee info card
                    $('#employeeInfoCard').addClass('show');

                    // Show success message
                    if (!$('.search-success-msg').length) {
                        $('#searchEmployeeBtn').after('<div class="search-success-msg text-success small mt-1"><i class="fas fa-check-circle"></i> تم العثور على الموظف بنجاح</div>');
                    }
                } else {
                    alert(response.error);
                    $('#employeeInfoCard').removeClass('show');
                    // Clear form fields and reset styling
                    $('#id_employee_name').val('').css({
                        'background-color': '#f8f9fa',
                        'border-color': '#ced4da',
                        'color': '#495057'
                    });
                    $('#id_employee_id').val('');
                    $('#id_employee').val('');
                    $('#id_original_department').val('');
                    // Remove success message
                    $('.search-success-msg').remove();
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                console.error('Response:', xhr.responseText);
                alert('حدث خطأ أثناء البحث عن الموظف. الرجاء المحاولة مرة أخرى.');
                $('#employeeInfoCard').removeClass('show');
                // Clear form fields and reset styling
                $('#id_employee_name').val('').css({
                    'background-color': '#f8f9fa',
                    'border-color': '#ced4da',
                    'color': '#495057'
                });
                $('#id_employee_id').val('');
                $('#id_employee').val('');
                $('#id_original_department').val('');
                // Remove success message
                $('.search-success-msg').remove();
            },
            complete: function() {
                // Hide loading state
                $btn.prop('disabled', false);
                $spinner.removeClass('show');
            }
        });
    });

    // Auto-search on Enter key
    $('#id_ministry_number').keypress(function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $('#searchEmployeeBtn').click();
        }
    });

    // Clear employee info when ministry number changes
    $('#id_ministry_number').on('input', function() {
        if ($(this).val().trim() === '') {
            $('#employeeInfoCard').removeClass('show');
            $('#id_employee_name').val('').css({
                'background-color': '#f8f9fa',
                'border-color': '#ced4da',
                'color': '#495057'
            });
            $('#id_employee_id').val('');
            $('#id_employee').val('');
            $('#id_original_department').val('');
            // Remove success message
            $('.search-success-msg').remove();
        }
    });

    // Show employee info if editing existing record
    {% if shared_employee %}
        $('#employeeInfoCard').addClass('show');
        $('#displayMinistryNumber').text('{{ shared_employee.employee.ministry_number }}');
        $('#displayFullName').text('{{ shared_employee.employee.full_name }}');
        $('#displaySpecialization').text('{{ shared_employee.employee.specialization|default:"غير محدد" }}');
        $('#displaySchool').text('{{ shared_employee.employee.school|default:"غير محدد" }}');
    {% endif %}
});
</script>
{% endblock %}
