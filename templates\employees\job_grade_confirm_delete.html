{% extends 'base.html' %}
{% load static %}

{% block title %}حذف الدرجة الوظيفية{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .delete-container {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        border: 2px solid #dc3545;
    }

    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 1rem;
    }

    .btn-action {
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        min-width: 120px;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }

    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .grade-info {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        border-left: 4px solid #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1 class="mb-3">
            <i class="fas fa-exclamation-triangle"></i>
            حذف الدرجة الوظيفية
        </h1>
        <p class="lead mb-0">تأكيد حذف الدرجة الوظيفية من النظام</p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="delete-container fade-in text-center">
                <div class="warning-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                
                <h4 class="text-danger mb-3">تحذير!</h4>
                <p class="lead mb-4">
                    هل أنت متأكد من حذف هذه الدرجة الوظيفية؟
                </p>

                <!-- Grade Information -->
                <div class="grade-info text-start">
                    <h6 class="text-danger mb-3">
                        <i class="fas fa-info-circle"></i>
                        معلومات الدرجة المراد حذفها:
                    </h6>
                    
                    <div class="row">
                        <div class="col-sm-4">
                            <strong>اسم الدرجة:</strong>
                        </div>
                        <div class="col-sm-8">
                            {{ job_grade.name }}
                        </div>
                    </div>

                    {% if job_grade.description %}
                    <div class="row mt-2">
                        <div class="col-sm-4">
                            <strong>الوصف:</strong>
                        </div>
                        <div class="col-sm-8">
                            {{ job_grade.description }}
                        </div>
                    </div>
                    {% endif %}

                    <div class="row mt-2">
                        <div class="col-sm-4">
                            <strong>تاريخ الإنشاء:</strong>
                        </div>
                        <div class="col-sm-8">
                            {{ job_grade.created_at|date:"Y/m/d H:i" }}
                        </div>
                    </div>
                </div>

                <div class="alert alert-warning mt-4" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الدرجة الوظيفية نهائياً من النظام.
                </div>

                <!-- Action Buttons -->
                <form method="post" class="d-inline">
                    {% csrf_token %}
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <a href="{% url 'employees:job_grades_list' %}" class="btn btn-outline-secondary btn-action">
                            <i class="fas fa-arrow-left"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger btn-action" id="deleteBtn">
                            <i class="fas fa-trash"></i> تأكيد الحذف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Confirm delete action
    $('#deleteBtn').on('click', function(e) {
        e.preventDefault();
        
        if (confirm('هل أنت متأكد تماماً من حذف هذه الدرجة الوظيفية؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
            // Show loading state
            $(this).html('<i class="fas fa-spinner fa-spin"></i> جاري الحذف...');
            $(this).prop('disabled', true);
            
            // Submit the form
            $(this).closest('form').submit();
        }
    });
});
</script>
{% endblock %}
