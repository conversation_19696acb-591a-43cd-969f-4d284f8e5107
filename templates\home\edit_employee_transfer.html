{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .btn-outline-primary:hover {
        transform: translateY(-1px);
        transition: all 0.2s ease-in-out;
    }
    
    .btn-outline-success:hover {
        background-color: #28a745;
        border-color: #28a745;
        color: #fff;
        transform: translateY(-1px);
        transition: all 0.2s ease-in-out;
    }
    
    .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        color: #fff;
        transform: translateY(-1px);
        transition: all 0.2s ease-in-out;
    }
    
    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 36px;
        padding-left: 12px;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }
    
    .select2-dropdown {
        min-width: 350px !important;
        max-width: 500px !important;
        z-index: 9999 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{{ title }}</h6>
                    <div>
                        <a href="{% url 'home:employee_transfer_management' %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left"></i> العودة لإدارة النقل
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الرقم الوزاري <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="ministry_number" 
                                       value="{{ transfer.ministry_number }}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="employee_name" 
                                       value="{{ transfer.employee_name }}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">التخصص</label>
                                <input type="text" class="form-control" name="specialization" 
                                       value="{{ transfer.specialization|default:'' }}">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الخدمة الفعلية</label>
                                <input type="text" class="form-control" name="actual_service"
                                       value="{{ transfer.calculated_actual_service|default:transfer.actual_service|default:'' }}" readonly>
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle"></i> محسوبة تلقائياً من صفحة الخدمة الفعلية (مع خصم الإجازات بدون راتب)
                                </small>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">القسم الحالي <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="current_department" 
                                       value="{{ transfer.current_department }}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">القسم الجديد <span class="text-danger">*</span></label>
                                <select class="form-control select2" name="new_department_id" required>
                                    <option value="">اختر القسم الجديد...</option>
                                    {% for department in departments %}
                                    <option value="{{ department.id }}" 
                                            {% if transfer.new_department and transfer.new_department.id == department.id %}selected{% endif %}>
                                        {{ department.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">صفة النقل <span class="text-danger">*</span></label>
                                <select class="form-control" name="transfer_type" required>
                                    <option value="">اختر صفة النقل...</option>
                                    <option value="internal_transfer" {% if transfer.transfer_type == 'internal_transfer' %}selected{% endif %}>
                                        النقل الداخلي
                                    </option>
                                    <option value="temporary_assignment" {% if transfer.transfer_type == 'temporary_assignment' %}selected{% endif %}>
                                        التكليف المؤقت
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">التنسيب <span class="text-danger">*</span></label>
                                <select class="form-control" name="endorsement" required>
                                    <option value="">اختر التنسيب...</option>
                                    <option value="admin_financial_manager" {% if transfer.endorsement == 'admin_financial_manager' %}selected{% endif %}>
                                        تنسيب مدير الشؤون الادارية والمالية
                                    </option>
                                    <option value="hr_committee" {% if transfer.endorsement == 'hr_committee' %}selected{% endif %}>
                                        تنسيب لجنة الموارد البشرية
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea class="form-control" name="notes" rows="3" 
                                          placeholder="أدخل الملاحظات (اختياري)">{{ transfer.notes|default:'' }}</textarea>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <a href="{% url 'home:employee_transfer_management' %}" class="btn btn-outline-primary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-outline-success">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'ابحث عن القسم الجديد...',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "لا توجد أقسام متاحة";
            },
            searching: function() {
                return "جاري البحث عن الأقسام...";
            }
        }
    });
});
</script>
{% endblock %}
