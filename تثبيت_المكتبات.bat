@echo off
chcp 65001 >nul
title تثبيت مكتبات نظام إدارة شؤون الموظفين

echo.
echo ===============================================
echo    تثبيت مكتبات نظام إدارة شؤون الموظفين
echo    HR Management System - Install Libraries
echo ===============================================
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير متاح في PATH
    echo 💡 يرجى تثبيت Python أولاً من: https://python.org
    pause
    exit /b 1
)

python --version
echo ✅ Python متاح

echo.
echo 🔍 التحقق من pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متاح
    echo 💡 يرجى تثبيت pip أولاً
    pause
    exit /b 1
)

pip --version
echo ✅ pip متاح

echo.
echo 🔍 التحقق من ملف requirements.txt...
if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    echo 💡 تأكد من وجود الملف في نفس مجلد هذا الملف
    pause
    exit /b 1
)

echo ✅ ملف requirements.txt موجود

echo.
echo 📦 بدء تثبيت المكتبات...
echo ⏳ قد تستغرق هذه العملية عدة دقائق...
echo.

REM محاولة تثبيت المكتبات بطرق مختلفة
echo 🔄 المحاولة 1: pip install -r requirements.txt
pip install -r requirements.txt
if errorlevel 0 (
    echo ✅ تم تثبيت المكتبات بنجاح!
    goto :verify
)

echo ⚠️ فشلت المحاولة الأولى، جاري المحاولة الثانية...
echo 🔄 المحاولة 2: python -m pip install -r requirements.txt
python -m pip install -r requirements.txt
if errorlevel 0 (
    echo ✅ تم تثبيت المكتبات بنجاح!
    goto :verify
)

echo ⚠️ فشلت المحاولة الثانية، جاري المحاولة الثالثة...
echo 🔄 المحاولة 3: py -m pip install -r requirements.txt
py -m pip install -r requirements.txt
if errorlevel 0 (
    echo ✅ تم تثبيت المكتبات بنجاح!
    goto :verify
)

echo ❌ فشل في تثبيت المكتبات بجميع الطرق
echo 💡 يرجى المحاولة يدوياً:
echo    pip install -r requirements.txt
goto :end

:verify
echo.
echo 🔍 التحقق من تثبيت المكتبات الأساسية...
python -c "
import sys
packages = [
    ('django', 'Django'),
    ('PIL', 'Pillow'),
    ('openpyxl', 'OpenPyXL'),
    ('pandas', 'Pandas'),
    ('reportlab', 'ReportLab'),
    ('import_export', 'Django Import Export'),
    ('dateutil', 'Python DateUtil'),
    ('requests', 'Requests')
]

success = 0
total = len(packages)

for module, name in packages:
    try:
        __import__(module)
        print(f'✅ {name}')
        success += 1
    except ImportError:
        print(f'❌ {name}')

percentage = (success / total) * 100
print(f'')
print(f'📊 معدل النجاح: {percentage:.1f}%% ({success}/{total})')

if percentage >= 80:
    print('🎉 التثبيت مكتمل والنظام جاهز للتشغيل!')
    print('')
    print('🚀 لتشغيل النظام:')
    print('   python تشغيل_النظام.py')
    print('   أو')
    print('   python manage.py runserver')
    sys.exit(0)
else:
    print('⚠️ بعض المكتبات لم يتم تثبيتها بشكل صحيح')
    print('💡 يرجى إعادة تشغيل هذا الملف أو التثبيت اليدوي')
    sys.exit(1)
"

if errorlevel 0 (
    echo.
    echo ===============================================
    echo ✅ تم تثبيت المكتبات بنجاح!
    echo    النظام جاهز للتشغيل
    echo ===============================================
) else (
    echo.
    echo ===============================================
    echo ⚠️ بعض المكتبات لم يتم تثبيتها بشكل صحيح
    echo    يرجى إعادة المحاولة
    echo ===============================================
)

:end
echo.
echo 📱 اضغط أي مفتاح للخروج...
pause >nul
