from django.contrib import admin
from .models import RankType, EmployeeRank, Course, EmployeeCourse

@admin.register(RankType)
class RankTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ('name', 'description')

@admin.register(EmployeeRank)
class EmployeeRankAdmin(admin.ModelAdmin):
    list_display = ('employee', 'rank_type', 'date_obtained')
    list_filter = ('rank_type', 'date_obtained')
    search_fields = ('employee__full_name', 'employee__ministry_number', 'rank_type__name')
    date_hierarchy = 'date_obtained'


@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ('name', 'description')


@admin.register(EmployeeCourse)
class EmployeeCourseAdmin(admin.ModelAdmin):
    list_display = ('employee', 'course', 'hours', 'completion_date')
    list_filter = ('course', 'completion_date')
    search_fields = ('employee__full_name', 'employee__ministry_number', 'course__name')
    date_hierarchy = 'completion_date'
