#!/usr/bin/env python3
"""
تثبيت مبسط وفعال لجميع مكتبات النظام
Simple and effective installation of all system libraries
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command):
    """تنفيذ أمر وإرجاع النتيجة"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def install_requirements():
    """تثبيت جميع المكتبات من requirements.txt"""
    
    print("🔧 تثبيت مكتبات نظام إدارة شؤون الموظفين")
    print("=" * 60)
    
    # التحقق من وجود ملف requirements.txt
    if not Path("requirements.txt").exists():
        print("❌ ملف requirements.txt غير موجود!")
        return False
    
    # قائمة الأوامر للتجربة
    commands = [
        "pip install -r requirements.txt",
        "python -m pip install -r requirements.txt",
        "py -m pip install -r requirements.txt"
    ]
    
    print("📦 جاري تثبيت المكتبات...")
    
    for i, cmd in enumerate(commands, 1):
        print(f"\n🔄 المحاولة {i}: {cmd}")
        success, output = run_command(cmd)
        
        if success:
            print("✅ تم تثبيت المكتبات بنجاح!")
            print("\n📋 تفاصيل التثبيت:")
            print(output[-500:] if len(output) > 500 else output)
            return True
        else:
            print(f"❌ فشل: {output}")
    
    print("\n❌ فشل في تثبيت المكتبات بجميع الطرق!")
    return False

def install_essential_packages():
    """تثبيت المكتبات الأساسية فقط"""
    
    print("\n🎯 تثبيت المكتبات الأساسية فقط...")
    
    essential_packages = [
        "Django==5.2",
        "Pillow==10.1.0",
        "openpyxl==3.1.2",
        "pandas==2.1.4",
        "reportlab==4.0.9",
        "django-import-export==3.3.1",
        "python-dateutil==2.8.2",
        "requests==2.31.0"
    ]
    
    success_count = 0
    
    for package in essential_packages:
        print(f"📦 تثبيت {package}...")
        success, output = run_command(f"pip install {package}")
        
        if success:
            print(f"✅ تم تثبيت {package}")
            success_count += 1
        else:
            print(f"❌ فشل تثبيت {package}: {output}")
    
    print(f"\n📊 تم تثبيت {success_count}/{len(essential_packages)} مكتبة أساسية")
    return success_count == len(essential_packages)

def verify_installation():
    """التحقق من نجاح التثبيت"""
    
    print("\n🔍 التحقق من التثبيت...")
    
    test_imports = [
        ("django", "Django"),
        ("PIL", "Pillow"),
        ("openpyxl", "OpenPyXL"),
        ("pandas", "Pandas"),
        ("reportlab", "ReportLab"),
        ("import_export", "Django Import Export"),
        ("dateutil", "Python DateUtil"),
        ("requests", "Requests")
    ]
    
    success_count = 0
    
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name}")
            success_count += 1
        except ImportError:
            print(f"❌ {name}")
    
    percentage = (success_count / len(test_imports)) * 100
    print(f"\n📈 معدل النجاح: {percentage:.1f}% ({success_count}/{len(test_imports)})")
    
    return percentage >= 80

def main():
    """الدالة الرئيسية"""
    
    print("🚀 مثبت مكتبات نظام إدارة شؤون الموظفين")
    print("=" * 60)
    print(f"📍 مجلد العمل: {os.getcwd()}")
    print(f"🐍 إصدار Python: {sys.version}")
    
    # محاولة تثبيت جميع المكتبات
    if install_requirements():
        print("\n🎉 تم تثبيت جميع المكتبات بنجاح!")
    else:
        print("\n⚠️ فشل في تثبيت جميع المكتبات، محاولة تثبيت الأساسية...")
        if install_essential_packages():
            print("\n✅ تم تثبيت المكتبات الأساسية بنجاح!")
        else:
            print("\n❌ فشل في تثبيت المكتبات الأساسية!")
            return False
    
    # التحقق من التثبيت
    if verify_installation():
        print("\n🎯 التثبيت مكتمل والنظام جاهز للتشغيل!")
        print("\n🚀 لتشغيل النظام:")
        print("   python تشغيل_النظام.py")
        print("   أو")
        print("   python manage.py runserver")
        return True
    else:
        print("\n⚠️ بعض المكتبات لم يتم تثبيتها بشكل صحيح")
        print("💡 يرجى المحاولة مرة أخرى أو التثبيت اليدوي")
        return False

if __name__ == "__main__":
    try:
        success = main()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ اكتمل التثبيت بنجاح!")
        else:
            print("❌ فشل في التثبيت!")
        print("=" * 60)
        
        # إبقاء النافذة مفتوحة
        input("\n📱 اضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء التثبيت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("\n📱 اضغط Enter للخروج...")
