{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحليلات البيانات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* Modern Analytics Dashboard Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --info-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .analytics-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin: -1.5rem -1.5rem 2rem -1.5rem;
        border-radius: 0 0 20px 20px;
        position: relative;
        overflow: hidden;
    }

    .analytics-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .analytics-header .container-fluid {
        position: relative;
        z-index: 1;
    }

    .dashboard-card {
        border: none;
        border-radius: 20px;
        box-shadow: var(--card-shadow);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        overflow: hidden;
        position: relative;
        background: white;
    }

    .dashboard-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .dashboard-card:hover {
        transform: translateY(-10px);
        box-shadow: var(--card-hover-shadow);
    }

    .dashboard-card:hover::before {
        transform: scaleX(1);
    }

    .dashboard-card.primary::before {
        background: var(--primary-gradient);
    }

    .dashboard-card.success::before {
        background: var(--success-gradient);
    }

    .dashboard-card.info::before {
        background: var(--info-gradient);
    }

    .dashboard-card.warning::before {
        background: var(--warning-gradient);
    }

    .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }

    .icon-circle::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: inherit;
        opacity: 0.1;
        border-radius: 50%;
    }

    .icon-circle.primary {
        background: var(--primary-gradient);
    }

    .icon-circle.success {
        background: var(--success-gradient);
    }

    .icon-circle.info {
        background: var(--info-gradient);
    }

    .icon-circle.warning {
        background: var(--warning-gradient);
    }

    .animated-counter {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .chart-card {
        border: none;
        border-radius: 20px;
        box-shadow: var(--card-shadow);
        transition: all 0.3s ease;
        overflow: hidden;
        background: white;
    }

    .chart-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-hover-shadow);
    }

    .chart-header {
        background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
        border-bottom: 1px solid #e3e6f0;
        padding: 1.5rem;
        position: relative;
    }

    .chart-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 3px;
        background: var(--primary-gradient);
        border-radius: 2px;
    }

    .progress-modern {
        height: 25px;
        border-radius: 15px;
        background: #f8f9fc;
        overflow: hidden;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .progress-bar-modern {
        border-radius: 15px;
        position: relative;
        overflow: hidden;
    }

    .progress-bar-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.2) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.2) 75%, transparent 75%, transparent);
        background-size: 20px 20px;
        animation: progress-animation 1s linear infinite;
    }

    @keyframes progress-animation {
        0% { background-position: 0 0; }
        100% { background-position: 20px 0; }
    }

    .table-modern {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: var(--card-shadow);
    }

    .table-modern thead th {
        background: var(--primary-gradient);
        color: white;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 1rem;
    }

    .table-modern tbody tr {
        transition: all 0.3s ease;
    }

    .table-modern tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
        transform: scale(1.02);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-modern {
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .search-box {
        border-radius: 25px;
        border: 2px solid #e3e6f0;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        background: white;
    }

    .search-box:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        transform: scale(1.05);
    }

    .floating-action {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 1000;
    }

    .floating-btn {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--primary-gradient);
        border: none;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        transition: all 0.3s ease;
    }

    .floating-btn:hover {
        transform: scale(1.1) rotate(360deg);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
    }

    /* Loading Animation */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .loading-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Additional Enhancements */
    .text-purple {
        color: #6610f2 !important;
    }

    .chart-container canvas {
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }

    .badge {
        font-size: 0.85rem;
        font-weight: 600;
    }

    /* Pulse animation for important elements */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .dashboard-card:hover .icon-circle {
        animation: pulse 2s infinite;
    }

    /* Smooth transitions for all interactive elements */
    * {
        transition: all 0.3s ease;
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .analytics-header {
            padding: 1.5rem 0;
            margin: -1rem -1rem 2rem -1rem;
        }

        .animated-counter {
            font-size: 2rem;
        }

        .floating-action {
            bottom: 20px;
            right: 20px;
        }

        .dashboard-card {
            margin-bottom: 1.5rem;
        }

        .chart-card {
            margin-bottom: 2rem;
        }

        .search-box {
            width: 100% !important;
            margin-bottom: 1rem;
        }

        .btn-modern {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .analytics-header h1 {
            font-size: 1.5rem;
        }

        .analytics-header p {
            font-size: 0.875rem;
        }

        .icon-circle {
            width: 50px;
            height: 50px;
        }

        .animated-counter {
            font-size: 1.75rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner"></div>
</div>

<!-- Analytics Header -->
<div class="analytics-header">
    <div class="container-fluid">
        <div class="d-sm-flex align-items-center justify-content-between">
            <div>
                <h1 class="h2 mb-2 fw-bold">
                    <i class="fas fa-chart-line me-3"></i>لوحة تحليلات البيانات
                </h1>
                <p class="mb-0 opacity-75">تحليل شامل لبيانات الموظفين والإحصائيات المتقدمة</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-light btn-modern" onclick="window.print()">
                    <i class="fas fa-download me-2"></i>تصدير التقرير
                </button>
                <button id="refreshData" class="btn btn-outline-light btn-modern">
                    <i class="fas fa-sync-alt me-2"></i>تحديث البيانات
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">

    <!-- Overview Cards -->
    <div class="row mb-5">
        <!-- Total Employees Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card primary h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="flex-grow-1">
                            <div class="text-uppercase fw-bold text-muted mb-2" style="font-size: 0.75rem; letter-spacing: 1px;">
                                إجمالي الموظفين
                            </div>
                            <div class="animated-counter mb-2" data-target="{{ total_employees }}">0</div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-success bg-opacity-10 text-success px-2 py-1 rounded-pill">
                                    <i class="fas fa-arrow-up me-1"></i>{{ employee_growth }}%
                                </span>
                                <span class="text-muted small ms-2">مقارنة بالشهر السابق</span>
                            </div>
                        </div>
                        <div class="icon-circle primary">
                            <i class="fas fa-users fa-lg text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Departments Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card success h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="flex-grow-1">
                            <div class="text-uppercase fw-bold text-muted mb-2" style="font-size: 0.75rem; letter-spacing: 1px;">
                                الأقسام
                            </div>
                            <div class="animated-counter mb-2" data-target="{{ total_departments }}">0</div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-info bg-opacity-10 text-info px-2 py-1 rounded-pill">
                                    <i class="fas fa-check-circle me-1"></i>{{ departments_with_employees }} مشغول
                                </span>
                            </div>
                        </div>
                        <div class="icon-circle success">
                            <i class="fas fa-building fa-lg text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Positions Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card info h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="flex-grow-1">
                            <div class="text-uppercase fw-bold text-muted mb-2" style="font-size: 0.75rem; letter-spacing: 1px;">
                                المسميات الوظيفية
                            </div>
                            <div class="animated-counter mb-2" data-target="{{ total_positions }}">0</div>
                            <div class="text-muted small">{{ positions_with_employees }} مسمى وظيفي مشغول</div>
                        </div>
                        <div class="icon-circle info">
                            <i class="fas fa-briefcase fa-lg text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Leaves Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card warning h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="flex-grow-1">
                            <div class="text-uppercase fw-bold text-muted mb-2" style="font-size: 0.75rem; letter-spacing: 1px;">
                                الإجازات النشطة
                            </div>
                            <div class="animated-counter mb-2" data-target="{{ active_leaves }}">0</div>
                            <div class="text-muted small">{{ leave_percentage }}% من الموظفين</div>
                        </div>
                        <div class="icon-circle warning">
                            <i class="fas fa-calendar-alt fa-lg text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-5">
        <!-- Gender Distribution Chart -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="chart-card h-100">
                <div class="chart-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <h6 class="m-0 fw-bold text-dark">
                            <i class="fas fa-venus-mars me-2 text-primary"></i>توزيع الموظفين حسب الجنس
                        </h6>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog me-1"></i>خيارات
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow">
                                <li><h6 class="dropdown-header">نوع الرسم البياني</h6></li>
                                <li><a class="dropdown-item chart-type" href="#" data-chart="gender" data-type="pie">
                                    <i class="fas fa-chart-pie me-2"></i>رسم دائري
                                </a></li>
                                <li><a class="dropdown-item chart-type" href="#" data-chart="gender" data-type="doughnut">
                                    <i class="fas fa-circle-notch me-2"></i>رسم حلقي
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="exportChart('genderChart', 'توزيع الموظفين حسب الجنس')">
                                    <i class="fas fa-download me-2"></i>تصدير كصورة
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="chart-container" style="position: relative; height:350px;">
                        <canvas id="genderChart"></canvas>
                    </div>
                    <div class="mt-4 d-flex justify-content-center gap-4">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle bg-primary me-2" style="width: 12px; height: 12px;"></div>
                            <span class="small fw-bold">ذكور</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle bg-success me-2" style="width: 12px; height: 12px;"></div>
                            <span class="small fw-bold">إناث</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Qualification Distribution Chart -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="chart-card h-100">
                <div class="chart-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <h6 class="m-0 fw-bold text-dark">
                            <i class="fas fa-graduation-cap me-2 text-info"></i>توزيع الموظفين حسب المؤهل العلمي
                        </h6>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-info dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog me-1"></i>خيارات
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow">
                                <li><h6 class="dropdown-header">نوع الرسم البياني</h6></li>
                                <li><a class="dropdown-item chart-type" href="#" data-chart="qualification" data-type="bar">
                                    <i class="fas fa-chart-bar me-2"></i>رسم شريطي
                                </a></li>
                                <li><a class="dropdown-item chart-type" href="#" data-chart="qualification" data-type="horizontalBar">
                                    <i class="fas fa-chart-bar fa-rotate-90 me-2"></i>رسم شريطي أفقي
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="exportChart('qualificationChart', 'توزيع الموظفين حسب المؤهل العلمي')">
                                    <i class="fas fa-download me-2"></i>تصدير كصورة
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="chart-container" style="position: relative; height:350px;">
                        <canvas id="qualificationChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Charts Row -->
    <div class="row mb-5">
        <!-- Position Distribution Chart -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="chart-card h-100">
                <div class="chart-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <h6 class="m-0 fw-bold text-dark">
                            <i class="fas fa-briefcase me-2 text-purple"></i>توزيع الموظفين حسب المسمى الوظيفي
                        </h6>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog me-1"></i>خيارات
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow">
                                <li><h6 class="dropdown-header">نوع الرسم البياني</h6></li>
                                <li><a class="dropdown-item chart-type" href="#" data-chart="position" data-type="bar">
                                    <i class="fas fa-chart-bar me-2"></i>رسم شريطي
                                </a></li>
                                <li><a class="dropdown-item chart-type" href="#" data-chart="position" data-type="horizontalBar">
                                    <i class="fas fa-chart-bar fa-rotate-90 me-2"></i>رسم شريطي أفقي
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="exportChart('positionChart', 'توزيع الموظفين حسب المسمى الوظيفي')">
                                    <i class="fas fa-download me-2"></i>تصدير كصورة
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="chart-container" style="position: relative; height:400px;">
                        <canvas id="positionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee Growth Chart -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="chart-card h-100">
                <div class="chart-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <h6 class="m-0 fw-bold text-dark">
                            <i class="fas fa-chart-line me-2 text-warning"></i>نمو عدد الموظفين
                        </h6>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-warning dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog me-1"></i>خيارات
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow">
                                <li><h6 class="dropdown-header">نوع الرسم البياني</h6></li>
                                <li><a class="dropdown-item chart-type" href="#" data-chart="growth" data-type="line">
                                    <i class="fas fa-chart-line me-2"></i>رسم خطي
                                </a></li>
                                <li><a class="dropdown-item chart-type" href="#" data-chart="growth" data-type="bar">
                                    <i class="fas fa-chart-bar me-2"></i>رسم شريطي
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="exportChart('growthChart', 'نمو عدد الموظفين')">
                                    <i class="fas fa-download me-2"></i>تصدير كصورة
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="chart-container" style="position: relative; height:400px;">
                        <canvas id="growthChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Table -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="chart-card">
                <div class="chart-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <h6 class="m-0 fw-bold text-dark">
                            <i class="fas fa-table me-2 text-success"></i>توزيع الموظفين حسب الأقسام
                        </h6>
                        <div class="d-flex align-items-center gap-3">
                            <input type="text" id="departmentSearch" class="search-box" placeholder="🔍 بحث في الأقسام..." style="width: 250px;">
                            <button class="btn btn-sm btn-outline-success" onclick="exportTableData()">
                                <i class="fas fa-file-excel me-1"></i>تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-modern mb-0" id="departmentTable">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-building me-2"></i>القسم</th>
                                    <th><i class="fas fa-users me-2"></i>عدد الموظفين</th>
                                    <th><i class="fas fa-percentage me-2"></i>النسبة المئوية</th>
                                    <th><i class="fas fa-chart-bar me-2"></i>توزيع الجنس (ذكور/إناث)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dept in departments_data %}
                                <tr>
                                    <td class="fw-bold">{{ dept.name }}</td>
                                    <td>
                                        <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 rounded-pill">
                                            {{ dept.count }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-info">{{ dept.percentage }}%</span>
                                    </td>
                                    <td>
                                        <div class="progress-modern">
                                            <div class="progress-bar-modern bg-primary" role="progressbar" style="width: {{ dept.male_percentage }}%"
                                                aria-valuenow="{{ dept.male_percentage }}" aria-valuemin="0" aria-valuemax="100">
                                                <span class="small fw-bold text-white px-2">{{ dept.male_count }} ({{ dept.male_percentage }}%)</span>
                                            </div>
                                            <div class="progress-bar-modern bg-success" role="progressbar" style="width: {{ dept.female_percentage }}%"
                                                aria-valuenow="{{ dept.female_percentage }}" aria-valuemin="0" aria-valuemax="100">
                                                <span class="small fw-bold text-white px-2">{{ dept.female_count }} ({{ dept.female_percentage }}%)</span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <div class="floating-action">
        <button class="floating-btn" onclick="scrollToTop()" title="العودة للأعلى">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Chart.js Plugins -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>

<script>
    // Register Chart.js plugins
    Chart.register(ChartDataLabels);

    // Set Chart.js defaults
    Chart.defaults.font.family = 'Tajawal, sans-serif';
    Chart.defaults.font.size = 14;
    Chart.defaults.color = '#666';
    Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    Chart.defaults.plugins.tooltip.padding = 10;
    Chart.defaults.plugins.tooltip.cornerRadius = 6;
    Chart.defaults.plugins.tooltip.titleFont = { weight: 'bold' };
    Chart.defaults.plugins.datalabels.color = '#fff';
    Chart.defaults.plugins.datalabels.font = { weight: 'bold' };
    Chart.defaults.plugins.datalabels.formatter = (value, ctx) => {
        const dataset = ctx.chart.data.datasets[ctx.datasetIndex];
        const total = dataset.data.reduce((acc, data) => acc + data, 0);
        const percentage = Math.round((value / total) * 100) + '%';
        return percentage;
    };

    // Parse data from Django
    const genderData = JSON.parse('{{ gender_data|safe }}');
    const qualificationData = JSON.parse('{{ qualification_data|safe }}');
    const positionData = JSON.parse('{{ position_data|safe }}');
    const growthData = JSON.parse('{{ growth_data|safe }}');

    // Chart instances
    let genderChart, qualificationChart, positionChart, growthChart;

    // Create charts when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Show loading overlay
        showLoading();

        // Animate counters
        animateCounters();

        // Create charts with delay for better UX
        setTimeout(() => {
            createGenderChart('pie');
            createQualificationChart('bar');
            createPositionChart('horizontalBar');
            createGrowthChart('line');

            // Initialize search functionality for department table
            initDepartmentSearch();

            // Hide loading overlay
            hideLoading();
        }, 1000);

        // Handle chart type changes with loading animation
        document.querySelectorAll('.chart-type').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const chart = this.getAttribute('data-chart');
                const type = this.getAttribute('data-type');

                // Show loading for chart update
                showLoading();

                setTimeout(() => {
                    if (chart === 'gender') {
                        genderChart.destroy();
                        createGenderChart(type);
                    } else if (chart === 'qualification') {
                        qualificationChart.destroy();
                        createQualificationChart(type);
                    } else if (chart === 'position') {
                        positionChart.destroy();
                        createPositionChart(type);
                    } else if (chart === 'growth') {
                        growthChart.destroy();
                        createGrowthChart(type);
                    }
                    hideLoading();
                }, 500);
            });
        });

        // Handle refresh button with loading animation
        document.getElementById('refreshData').addEventListener('click', function() {
            showLoading();
            setTimeout(() => {
                location.reload();
            }, 1000);
        });

        // Add smooth scroll behavior
        document.documentElement.style.scrollBehavior = 'smooth';
    });

    // Create gender distribution chart
    function createGenderChart(type) {
        const ctx = document.getElementById('genderChart').getContext('2d');

        // Prepare labels with Arabic names
        const labels = genderData.labels.map(label =>
            genderData.label_names[label] || label
        );

        genderChart = new Chart(ctx, {
            type: type,
            data: {
                labels: labels,
                datasets: [{
                    data: genderData.data,
                    backgroundColor: [
                        'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)'
                    ],
                    borderColor: ['#667eea', '#11998e'],
                    borderWidth: 3,
                    hoverBorderWidth: 5,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    animateScale: true,
                    animateRotate: true,
                    duration: 2000,
                    easing: 'easeOutBounce'
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#667eea',
                        borderWidth: 2,
                        cornerRadius: 10,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                const dataset = context.chart.data.datasets[context.datasetIndex];
                                const total = dataset.data.reduce((acc, data) => acc + data, 0);
                                const percentage = Math.round((context.parsed / total) * 100);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    },
                    datalabels: {
                        color: '#fff',
                        font: {
                            weight: 'bold',
                            size: 16
                        },
                        formatter: (value, ctx) => {
                            const dataset = ctx.chart.data.datasets[ctx.datasetIndex];
                            const total = dataset.data.reduce((acc, data) => acc + data, 0);
                            const percentage = Math.round((value / total) * 100) + '%';
                            return percentage;
                        }
                    }
                }
            }
        });
    }

    // Create qualification distribution chart
    function createQualificationChart(type) {
        const ctx = document.getElementById('qualificationChart').getContext('2d');

        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, 'rgba(23, 162, 184, 0.9)');
        gradient.addColorStop(1, 'rgba(23, 162, 184, 0.3)');

        qualificationChart = new Chart(ctx, {
            type: type === 'horizontalBar' ? 'bar' : type,
            data: {
                labels: qualificationData.labels,
                datasets: [{
                    label: 'عدد الموظفين',
                    data: qualificationData.data,
                    backgroundColor: gradient,
                    borderColor: '#17a2b8',
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                    hoverBackgroundColor: 'rgba(23, 162, 184, 1)',
                    hoverBorderColor: '#0c5460',
                    hoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: type === 'horizontalBar' ? 'y' : 'x',
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#17a2b8',
                        borderWidth: 2,
                        cornerRadius: 10,
                        callbacks: {
                            label: function(context) {
                                return `عدد الموظفين: ${context.parsed.y || context.parsed.x}`;
                            }
                        }
                    },
                    datalabels: {
                        color: '#fff',
                        font: {
                            weight: 'bold',
                            size: 12
                        },
                        anchor: 'end',
                        align: 'start',
                        formatter: (value) => value
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    }
                }
            }
        });
    }

    // Create position distribution chart
    function createPositionChart(type) {
        const ctx = document.getElementById('positionChart').getContext('2d');

        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, 'rgba(102, 16, 242, 0.9)');
        gradient.addColorStop(1, 'rgba(102, 16, 242, 0.3)');

        positionChart = new Chart(ctx, {
            type: type === 'horizontalBar' ? 'bar' : type,
            data: {
                labels: positionData.labels,
                datasets: [{
                    label: 'عدد الموظفين',
                    data: positionData.data,
                    backgroundColor: gradient,
                    borderColor: '#6610f2',
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                    hoverBackgroundColor: 'rgba(102, 16, 242, 1)',
                    hoverBorderColor: '#4a0e4e',
                    hoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: type === 'horizontalBar' ? 'y' : 'x',
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#6610f2',
                        borderWidth: 2,
                        cornerRadius: 10,
                        callbacks: {
                            label: function(context) {
                                return `عدد الموظفين: ${context.parsed.y || context.parsed.x}`;
                            }
                        }
                    },
                    datalabels: {
                        color: '#fff',
                        font: {
                            weight: 'bold',
                            size: 12
                        },
                        anchor: 'end',
                        align: 'start',
                        formatter: (value) => value
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    }
                }
            }
        });
    }

    // Create employee growth chart
    function createGrowthChart(type) {
        const ctx = document.getElementById('growthChart').getContext('2d');

        // Create gradient for area fill
        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, 'rgba(255, 193, 7, 0.6)');
        gradient.addColorStop(1, 'rgba(255, 193, 7, 0.1)');

        growthChart = new Chart(ctx, {
            type: type,
            data: {
                labels: growthData.labels,
                datasets: [{
                    label: 'عدد الموظفين',
                    data: growthData.data,
                    backgroundColor: type === 'line' ? gradient : 'rgba(255, 193, 7, 0.8)',
                    borderColor: '#ffc107',
                    borderWidth: 3,
                    tension: 0.4,
                    fill: type === 'line',
                    pointBackgroundColor: '#ffc107',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    pointHoverBackgroundColor: '#e0a800',
                    pointHoverBorderColor: '#fff',
                    pointHoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#ffc107',
                        borderWidth: 2,
                        cornerRadius: 10,
                        callbacks: {
                            label: function(context) {
                                return `عدد الموظفين: ${context.parsed.y}`;
                            }
                        }
                    },
                    datalabels: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    }
                }
            }
        });
    }

    // Initialize department table search
    function initDepartmentSearch() {
        const searchInput = document.getElementById('departmentSearch');
        const table = document.getElementById('departmentTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

        searchInput.addEventListener('keyup', function() {
            const term = searchInput.value.toLowerCase();

            for (let i = 0; i < rows.length; i++) {
                const deptName = rows[i].getElementsByTagName('td')[0].textContent.toLowerCase();
                if (deptName.indexOf(term) > -1) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        });
    }

    // Export chart as image
    function exportChart(chartId, title) {
        showLoading();
        setTimeout(() => {
            const canvas = document.getElementById(chartId);
            const image = canvas.toDataURL('image/png');

            // Create temporary link and trigger download
            const link = document.createElement('a');
            link.href = image;
            link.download = title + '.png';
            link.click();
            hideLoading();
        }, 500);
    }

    // Show loading overlay
    function showLoading() {
        document.getElementById('loadingOverlay').classList.add('active');
    }

    // Hide loading overlay
    function hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('active');
    }

    // Animate counters
    function animateCounters() {
        const counters = document.querySelectorAll('.animated-counter');

        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const duration = 2000; // 2 seconds
            const increment = target / (duration / 16); // 60fps
            let current = 0;

            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };

            // Start animation with delay based on counter position
            const delay = Array.from(counters).indexOf(counter) * 200;
            setTimeout(updateCounter, delay);
        });
    }

    // Scroll to top function
    function scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // Export table data as Excel
    function exportTableData() {
        showLoading();

        // Simulate export process
        setTimeout(() => {
            // In a real implementation, you would send data to server
            // For now, we'll just show a success message
            alert('سيتم تصدير البيانات قريباً...');
            hideLoading();
        }, 1500);
    }

    // Add intersection observer for animations
    function initScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1
        });

        // Observe all cards
        document.querySelectorAll('.dashboard-card, .chart-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });
    }

    // Initialize scroll animations after DOM load
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(initScrollAnimations, 1200);
    });
</script>
{% endblock %}
