-- <PERSON><PERSON>B dump 10.19  Distrib 10.4.32-MariaD<PERSON>, for Win64 (AMD64)
--
-- Host: localhost    Database: hr_system_db
-- ------------------------------------------------------
-- Server version	10.4.32-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `accounts_user`
--

DROP TABLE IF EXISTS `accounts_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accounts_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `password` varchar(128) NOT NULL,
  `last_login` datetime(6) DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `username` varchar(150) NOT NULL,
  `first_name` varchar(150) NOT NULL,
  `last_name` varchar(150) NOT NULL,
  `email` varchar(254) NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `date_joined` datetime(6) NOT NULL,
  `is_admin` tinyint(1) NOT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `address` longtext DEFAULT NULL,
  `is_full_admin` tinyint(1) NOT NULL,
  `login_count` int(10) unsigned NOT NULL CHECK (`login_count` >= 0),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `accounts_user`
--

LOCK TABLES `accounts_user` WRITE;
/*!40000 ALTER TABLE `accounts_user` DISABLE KEYS */;
INSERT INTO `accounts_user` VALUES (1,'pbkdf2_sha256$600000$nUlqSY9KJeuCKALiPgFHnG$Yalm0FlmQvg3Q6ekFsCUh0ILrVFlkJfKp+TdKYdvtEc=','2025-07-28 06:16:45.176007',1,'admin','','','<EMAIL>',1,1,'2025-07-28 06:09:32.160000',1,NULL,NULL,1,1);
/*!40000 ALTER TABLE `accounts_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `accounts_user_groups`
--

DROP TABLE IF EXISTS `accounts_user_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accounts_user_groups` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `group_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `accounts_user_groups_user_id_group_id_59c0b32f_uniq` (`user_id`,`group_id`),
  KEY `accounts_user_groups_group_id_bd11a704_fk_auth_group_id` (`group_id`),
  CONSTRAINT `accounts_user_groups_group_id_bd11a704_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`),
  CONSTRAINT `accounts_user_groups_user_id_52b62117_fk_accounts_user_id` FOREIGN KEY (`user_id`) REFERENCES `accounts_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `accounts_user_groups`
--

LOCK TABLES `accounts_user_groups` WRITE;
/*!40000 ALTER TABLE `accounts_user_groups` DISABLE KEYS */;
/*!40000 ALTER TABLE `accounts_user_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `accounts_user_user_permissions`
--

DROP TABLE IF EXISTS `accounts_user_user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accounts_user_user_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `accounts_user_user_permi_user_id_permission_id_2ab516c2_uniq` (`user_id`,`permission_id`),
  KEY `accounts_user_user_p_permission_id_113bb443_fk_auth_perm` (`permission_id`),
  CONSTRAINT `accounts_user_user_p_permission_id_113bb443_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `accounts_user_user_p_user_id_e4f0a161_fk_accounts_` FOREIGN KEY (`user_id`) REFERENCES `accounts_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `accounts_user_user_permissions`
--

LOCK TABLES `accounts_user_user_permissions` WRITE;
/*!40000 ALTER TABLE `accounts_user_user_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `accounts_user_user_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `accounts_userpermission`
--

DROP TABLE IF EXISTS `accounts_userpermission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accounts_userpermission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `module_name` varchar(50) NOT NULL,
  `can_view` tinyint(1) NOT NULL,
  `can_add` tinyint(1) NOT NULL,
  `can_edit` tinyint(1) NOT NULL,
  `can_delete` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `visible_pages` longtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `accounts_userpermission_user_id_module_name_208f4001_uniq` (`user_id`,`module_name`),
  CONSTRAINT `accounts_userpermission_user_id_43f5fc10_fk_accounts_user_id` FOREIGN KEY (`user_id`) REFERENCES `accounts_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `accounts_userpermission`
--

LOCK TABLES `accounts_userpermission` WRITE;
/*!40000 ALTER TABLE `accounts_userpermission` DISABLE KEYS */;
INSERT INTO `accounts_userpermission` VALUES (1,'employees',1,1,1,1,'2025-07-28 06:19:14.529874','2025-07-28 06:19:14.529888',1,'employees:employee_list,employees:employee_create,employees:employee_detail,employees:employee_update,employees:employee_delete,employees:employee_import_export,employees:get_employee_by_ministry_number,employees:annual_report_create,employees:calculate_age,employees:add_qualification,employees:search_employee_for_qualification,employees:maternity_leaves_list,employees:add_maternity_leave,employees:maternity_leave_detail,employees:maternity_leave_update,employees:maternity_leave_delete,employees:search_employee_for_maternity,employees:export_maternity_leaves_excel,employees:retired_employees_list,employees:retired_employee_detail,employees:retired_employee_update,employees:retired_employee_delete,employees:search_employees_for_retirement,employees:external_transfers_list,employees:external_transfer_detail,employees:external_transfer_update,employees:external_transfer_delete,employees:search_employees_for_transfer,employees:internal_transfers_list,employees:internal_transfer_detail,employees:internal_transfer_update,employees:internal_transfer_delete,employees:internal_transfers_statistics,employees:internal_transfers_statistics_api,employees:export_internal_transfers_excel'),(2,'employment',1,1,1,1,'2025-07-28 06:19:14.531665','2025-07-28 06:19:14.531681',1,'employment:department_list,employment:position_list,employment:employee_position_list,employment:experience_certificate_list,employment:employee_identification_list,employment:technical_position_list,employment:actual_service_list'),(3,'leaves',1,1,1,1,'2025-07-28 06:19:14.533796','2025-07-28 06:19:14.533811',1,'leaves:unpaid_leave_list,leaves:leave_list,leaves:leave_create,leaves:leave_balance_list,leaves:leave_reports'),(4,'disciplinary',1,1,1,1,'2025-07-28 06:19:14.539476','2025-07-28 06:19:14.539512',1,'disciplinary:penalty_list,disciplinary:penalty_type_list'),(5,'directorate_leaves',1,1,1,1,'2025-07-28 06:19:14.541674','2025-07-28 06:19:14.541697',1,'directorate_leaves:whatsapp_send'),(6,'file_management',1,1,1,1,'2025-07-28 06:19:14.544073','2025-07-28 06:19:14.544114',1,'file_management:file_movement_list,file_management:file_checkout,file_management:file_return_list'),(7,'ranks',1,1,1,1,'2025-07-28 06:19:14.545924','2025-07-28 06:19:14.545945',1,'ranks:rank_type_list,ranks:employee_rank_create,ranks:employee_rank_list'),(8,'performance',1,1,1,1,'2025-07-28 06:19:14.547974','2025-07-28 06:19:14.548006',1,'performance:performance_list'),(9,'reports',1,1,1,1,'2025-07-28 06:19:14.550553','2025-07-28 06:19:14.550572',1,'reports:report_dashboard'),(10,'accounts',1,1,1,1,'2025-07-28 06:19:14.554075','2025-07-28 06:19:14.554135',1,'accounts:user_list'),(11,'backup',1,1,1,1,'2025-07-28 06:19:14.557676','2025-07-28 06:19:14.557733',1,'backup:backup_list'),(12,'system_logs',1,1,1,1,'2025-07-28 06:19:14.560184','2025-07-28 06:19:14.560202',1,'system_logs:system_log_list,system_logs:system_error_list,system_logs:system_error_detail'),(13,'announcements',1,1,1,1,'2025-07-28 06:19:14.562266','2025-07-28 06:19:14.562281',1,'announcements:announcements_list,announcements:announcement_create,announcements:announcement_detail,announcements:announcement_update,announcements:announcement_delete,announcements:announcement_public_view,announcements:announcement_toggle_status,announcements:announcement_click_tracking,announcements:get_homepage_announcements'),(14,'notifications',1,1,1,1,'2025-07-28 06:19:14.563973','2025-07-28 06:19:14.563987',1,'notifications:notification_list'),(15,'home',1,1,1,1,'2025-07-28 06:19:14.565563','2025-07-28 06:19:14.565577',1,'home:home,home:analytics_dashboard,home:important_links,home:internal_transfer,home:search_employee,home:search_transfer_request,home:internal_transfer_success,home:internal_transfer_edit,home:about,home:search,home:internal_transfer_list,home:internal_transfer_detail,home:internal_transfer_delete,home:toggle_internal_transfer,home:enable_internal_transfer,home:disable_internal_transfer,home:delete_all_internal_transfers,home:export_internal_transfers,home:print_transfer_letters,home:print_single_transfer_letter,home:print_transfer_summary,home:admin_internal_transfer_create,home:technical_transfer,home:technical_transfer_search,home:technical_transfer_list,home:technical_transfer_print,home:technical_transfer_delete,home:approved_forms_list,home:approved_forms_admin,home:approved_form_create,home:approved_form_update,home:approved_form_delete,home:approved_form_download,home:leave_balance_inquiry,home:get_employee_leave_balance,home:employee_inquiry,home:get_employee_details,home:important_links_admin,home:important_link_add,home:important_link_update,home:important_link_delete,home:employee_transfer_management,home:search_employee_for_transfer,home:add_employee_transfer,home:update_employee_transfer,home:delete_employee_transfer,home:print_transfer_letter');
/*!40000 ALTER TABLE `accounts_userpermission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `announcements_announcement`
--

DROP TABLE IF EXISTS `announcements_announcement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `announcements_announcement` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `content` longtext NOT NULL,
  `announcement_type` varchar(20) NOT NULL,
  `priority` varchar(20) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `show_on_homepage` tinyint(1) NOT NULL,
  `start_date` datetime(6) NOT NULL,
  `end_date` datetime(6) DEFAULT NULL,
  `link_url` varchar(200) DEFAULT NULL,
  `link_text` varchar(100) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `views_count` int(10) unsigned NOT NULL CHECK (`views_count` >= 0),
  `clicks_count` int(10) unsigned NOT NULL CHECK (`clicks_count` >= 0),
  `created_by_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `announcements_announ_created_by_id_79d7d337_fk_accounts_` (`created_by_id`),
  KEY `announcemen_is_acti_fe68a4_idx` (`is_active`,`show_on_homepage`),
  KEY `announcemen_start_d_c54642_idx` (`start_date`,`end_date`),
  KEY `announcemen_priorit_603a2b_idx` (`priority`,`announcement_type`),
  CONSTRAINT `announcements_announ_created_by_id_79d7d337_fk_accounts_` FOREIGN KEY (`created_by_id`) REFERENCES `accounts_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `announcements_announcement`
--

LOCK TABLES `announcements_announcement` WRITE;
/*!40000 ALTER TABLE `announcements_announcement` DISABLE KEYS */;
INSERT INTO `announcements_announcement` VALUES (1,'إعلان ترحيبي - اختبار النظام','مرحباً بكم في نظام إدارة الإعلانات الجديد! هذا إعلان اختبار لتأكد من عمل النظام بشكل صحيح.','info','medium',1,1,'2025-06-17 08:10:47.440000','2025-07-17 08:10:47.440000','https://example.com','اقرأ المزيد','2025-06-17 08:10:47.441000','2025-07-17 05:33:10.265000',4,1,1),(2,'اهلا وسهلا بكم في منصة شؤون الموظفين','ترحيب','info','high',1,1,'2025-06-17 08:23:45.000000',NULL,NULL,NULL,'2025-06-17 08:24:55.920000','2025-07-17 05:27:05.377000',9,2,1),(4,'تحذير مهم - عاجل','هذا تحذير عاجل لجميع المستخدمين. الرجاء قراءة المحتوى بعناية.','warning','urgent',1,1,'2025-06-17 08:34:25.000000','2025-07-17 08:34:25.000000',NULL,NULL,'2025-06-17 08:34:25.110000','2025-07-17 05:33:11.554000',5,7,1),(5,'إعلان نجاح العملية','تم إنجاز العملية بنجاح. شكراً لتعاونكم.','success','low',1,0,'2025-06-17 08:34:25.114000','2025-07-17 08:34:25.114000',NULL,NULL,'2025-06-17 08:34:25.115000','2025-07-17 05:27:04.586000',0,0,1);
/*!40000 ALTER TABLE `announcements_announcement` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_group`
--

DROP TABLE IF EXISTS `auth_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `auth_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_group`
--

LOCK TABLES `auth_group` WRITE;
/*!40000 ALTER TABLE `auth_group` DISABLE KEYS */;
/*!40000 ALTER TABLE `auth_group` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_group_permissions`
--

DROP TABLE IF EXISTS `auth_group_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `auth_group_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_group_permissions_group_id_permission_id_0cd325b0_uniq` (`group_id`,`permission_id`),
  KEY `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` (`permission_id`),
  CONSTRAINT `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `auth_group_permissions_group_id_b120cbf9_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_group_permissions`
--

LOCK TABLES `auth_group_permissions` WRITE;
/*!40000 ALTER TABLE `auth_group_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `auth_group_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_permission`
--

DROP TABLE IF EXISTS `auth_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `auth_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `content_type_id` int(11) NOT NULL,
  `codename` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_permission_content_type_id_codename_01ab375a_uniq` (`content_type_id`,`codename`),
  CONSTRAINT `auth_permission_content_type_id_2f476e4b_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=237 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_permission`
--

LOCK TABLES `auth_permission` WRITE;
/*!40000 ALTER TABLE `auth_permission` DISABLE KEYS */;
INSERT INTO `auth_permission` VALUES (1,'Can add log entry',1,'add_logentry'),(2,'Can change log entry',1,'change_logentry'),(3,'Can delete log entry',1,'delete_logentry'),(4,'Can view log entry',1,'view_logentry'),(5,'Can add permission',2,'add_permission'),(6,'Can change permission',2,'change_permission'),(7,'Can delete permission',2,'delete_permission'),(8,'Can view permission',2,'view_permission'),(9,'Can add group',3,'add_group'),(10,'Can change group',3,'change_group'),(11,'Can delete group',3,'delete_group'),(12,'Can view group',3,'view_group'),(13,'Can add content type',4,'add_contenttype'),(14,'Can change content type',4,'change_contenttype'),(15,'Can delete content type',4,'delete_contenttype'),(16,'Can view content type',4,'view_contenttype'),(17,'Can add Employee',5,'add_employee'),(18,'Can change Employee',5,'change_employee'),(19,'Can delete Employee',5,'delete_employee'),(20,'Can view Employee',5,'view_employee'),(21,'Can add Penalty',6,'add_penalty'),(22,'Can change Penalty',6,'change_penalty'),(23,'Can delete Penalty',6,'delete_penalty'),(24,'Can view Penalty',6,'view_penalty'),(25,'Can add Annual Report',7,'add_annualreport'),(26,'Can change Annual Report',7,'change_annualreport'),(27,'Can delete Annual Report',7,'delete_annualreport'),(28,'Can view Annual Report',7,'view_annualreport'),(29,'Can add User',8,'add_user'),(30,'Can change User',8,'change_user'),(31,'Can delete User',8,'delete_user'),(32,'Can view User',8,'view_user'),(33,'Can add User Permission',9,'add_userpermission'),(34,'Can change User Permission',9,'change_userpermission'),(35,'Can delete User Permission',9,'delete_userpermission'),(36,'Can view User Permission',9,'view_userpermission'),(37,'Can add Backup',10,'add_backup'),(38,'Can change Backup',10,'change_backup'),(39,'Can delete Backup',10,'delete_backup'),(40,'Can view Backup',10,'view_backup'),(41,'Can add نوع العقوبة',11,'add_penaltytype'),(42,'Can change نوع العقوبة',11,'change_penaltytype'),(43,'Can delete نوع العقوبة',11,'delete_penaltytype'),(44,'Can view نوع العقوبة',11,'view_penaltytype'),(45,'Can add عقوبة',12,'add_penalty'),(46,'Can change عقوبة',12,'change_penalty'),(47,'Can delete عقوبة',12,'delete_penalty'),(48,'Can view عقوبة',12,'view_penalty'),(49,'Can add إجازة بدون راتب',13,'add_unpaidleave'),(50,'Can change إجازة بدون راتب',13,'change_unpaidleave'),(51,'Can delete إجازة بدون راتب',13,'delete_unpaidleave'),(52,'Can view إجازة بدون راتب',13,'view_unpaidleave'),(53,'Can add إعلان',14,'add_announcement'),(54,'Can change إعلان',14,'change_announcement'),(55,'Can delete إعلان',14,'delete_announcement'),(56,'Can view إعلان',14,'view_announcement'),(57,'Can add Retired Employee',15,'add_retiredemployee'),(58,'Can change Retired Employee',15,'change_retiredemployee'),(59,'Can delete Retired Employee',15,'delete_retiredemployee'),(60,'Can view Retired Employee',15,'view_retiredemployee'),(61,'Can add External Transfer',16,'add_externaltransfer'),(62,'Can change External Transfer',16,'change_externaltransfer'),(63,'Can delete External Transfer',16,'delete_externaltransfer'),(64,'Can view External Transfer',16,'view_externaltransfer'),(65,'Can add Maternity Leave',17,'add_maternityleave'),(66,'Can change Maternity Leave',17,'change_maternityleave'),(67,'Can delete Maternity Leave',17,'delete_maternityleave'),(68,'Can view Maternity Leave',17,'view_maternityleave'),(69,'Can add Internal Transfer',18,'add_internaltransfer'),(70,'Can change Internal Transfer',18,'change_internaltransfer'),(71,'Can delete Internal Transfer',18,'delete_internaltransfer'),(72,'Can view Internal Transfer',18,'view_internaltransfer'),(73,'Can add Department',19,'add_department'),(74,'Can change Department',19,'change_department'),(75,'Can delete Department',19,'delete_department'),(76,'Can view Department',19,'view_department'),(77,'Can add Employment Status',20,'add_employmentstatus'),(78,'Can change Employment Status',20,'change_employmentstatus'),(79,'Can delete Employment Status',20,'delete_employmentstatus'),(80,'Can view Employment Status',20,'view_employmentstatus'),(81,'Can add Position',21,'add_position'),(82,'Can change Position',21,'change_position'),(83,'Can delete Position',21,'delete_position'),(84,'Can view Position',21,'view_position'),(85,'Can add Employment',22,'add_employment'),(86,'Can change Employment',22,'change_employment'),(87,'Can delete Employment',22,'delete_employment'),(88,'Can view Employment',22,'view_employment'),(89,'Can add الموقف الفني',23,'add_technicalposition'),(90,'Can change الموقف الفني',23,'change_technicalposition'),(91,'Can delete الموقف الفني',23,'delete_technicalposition'),(92,'Can view الموقف الفني',23,'view_technicalposition'),(93,'Can add المسمى الوظيفي للموظف',24,'add_employeeposition'),(94,'Can change المسمى الوظيفي للموظف',24,'change_employeeposition'),(95,'Can delete المسمى الوظيفي للموظف',24,'delete_employeeposition'),(96,'Can view المسمى الوظيفي للموظف',24,'view_employeeposition'),(97,'Can add بيانات تعريفية للموظف',25,'add_employeeidentification'),(98,'Can change بيانات تعريفية للموظف',25,'change_employeeidentification'),(99,'Can delete بيانات تعريفية للموظف',25,'delete_employeeidentification'),(100,'Can view بيانات تعريفية للموظف',25,'view_employeeidentification'),(101,'Can add Appointment Type',26,'add_appointmenttype'),(102,'Can change Appointment Type',26,'change_appointmenttype'),(103,'Can delete Appointment Type',26,'delete_appointmenttype'),(104,'Can view Appointment Type',26,'view_appointmenttype'),(105,'Can add موظف زائد',27,'add_excessemployee'),(106,'Can change موظف زائد',27,'change_excessemployee'),(107,'Can delete موظف زائد',27,'delete_excessemployee'),(108,'Can view موظف زائد',27,'view_excessemployee'),(109,'Can add حالة مرضية',28,'add_medicalcondition'),(110,'Can change حالة مرضية',28,'change_medicalcondition'),(111,'Can delete حالة مرضية',28,'delete_medicalcondition'),(112,'Can view حالة مرضية',28,'view_medicalcondition'),(113,'Can add اسم حالة مرضية',29,'add_medicalconditionname'),(114,'Can change اسم حالة مرضية',29,'change_medicalconditionname'),(115,'Can delete اسم حالة مرضية',29,'delete_medicalconditionname'),(116,'Can view اسم حالة مرضية',29,'view_medicalconditionname'),(117,'Can add حقل BTEC',30,'add_btecfield'),(118,'Can change حقل BTEC',30,'change_btecfield'),(119,'Can delete حقل BTEC',30,'delete_btecfield'),(120,'Can view حقل BTEC',30,'view_btecfield'),(121,'Can add معلم BTEC',31,'add_btecteacher'),(122,'Can change معلم BTEC',31,'change_btecteacher'),(123,'Can delete معلم BTEC',31,'delete_btecteacher'),(124,'Can view معلم BTEC',31,'view_btecteacher'),(125,'Can add علاوات الموظف',32,'add_employeeallowance'),(126,'Can change علاوات الموظف',32,'change_employeeallowance'),(127,'Can delete علاوات الموظف',32,'delete_employeeallowance'),(128,'Can view علاوات الموظف',32,'view_employeeallowance'),(129,'Can add وظيفة BTEC',33,'add_btecjob'),(130,'Can change وظيفة BTEC',33,'change_btecjob'),(131,'Can delete وظيفة BTEC',33,'delete_btecjob'),(132,'Can view وظيفة BTEC',33,'view_btecjob'),(133,'Can add عدم صرف',34,'add_nonpayment'),(134,'Can change عدم صرف',34,'change_nonpayment'),(135,'Can delete عدم صرف',34,'delete_nonpayment'),(136,'Can view عدم صرف',34,'view_nonpayment'),(137,'Can add مدرسة صفرية',35,'add_zeroschool'),(138,'Can change مدرسة صفرية',35,'change_zeroschool'),(139,'Can delete مدرسة صفرية',35,'delete_zeroschool'),(140,'Can view مدرسة صفرية',35,'view_zeroschool'),(141,'Can add Service Purchase',36,'add_servicepurchase'),(142,'Can change Service Purchase',36,'change_servicepurchase'),(143,'Can delete Service Purchase',36,'delete_servicepurchase'),(144,'Can view Service Purchase',36,'view_servicepurchase'),(145,'Can add حركة الملف',37,'add_filemovement'),(146,'Can change حركة الملف',37,'change_filemovement'),(147,'Can delete حركة الملف',37,'delete_filemovement'),(148,'Can view حركة الملف',37,'view_filemovement'),(149,'Can add ملف',38,'add_file'),(150,'Can change ملف',38,'change_file'),(151,'Can delete ملف',38,'delete_file'),(152,'Can view ملف',38,'view_file'),(153,'Can add طلب نقل داخلي',39,'add_internaltransfer'),(154,'Can change طلب نقل داخلي',39,'change_internaltransfer'),(155,'Can delete طلب نقل داخلي',39,'delete_internaltransfer'),(156,'Can view طلب نقل داخلي',39,'view_internaltransfer'),(157,'Can add إعدادات النظام',40,'add_systemsettings'),(158,'Can change إعدادات النظام',40,'change_systemsettings'),(159,'Can delete إعدادات النظام',40,'delete_systemsettings'),(160,'Can view إعدادات النظام',40,'view_systemsettings'),(161,'Can add نموذج معتمد',41,'add_approvedform'),(162,'Can change نموذج معتمد',41,'change_approvedform'),(163,'Can delete نموذج معتمد',41,'delete_approvedform'),(164,'Can view نموذج معتمد',41,'view_approvedform'),(165,'Can add نقل فني',42,'add_technicaltransfer'),(166,'Can change نقل فني',42,'change_technicaltransfer'),(167,'Can delete نقل فني',42,'delete_technicaltransfer'),(168,'Can view نقل فني',42,'view_technicaltransfer'),(169,'Can add رابط مهم',43,'add_importantlink'),(170,'Can change رابط مهم',43,'change_importantlink'),(171,'Can delete رابط مهم',43,'delete_importantlink'),(172,'Can view رابط مهم',43,'view_importantlink'),(173,'Can add session',44,'add_session'),(174,'Can change session',44,'change_session'),(175,'Can delete session',44,'delete_session'),(176,'Can view session',44,'view_session'),(177,'Can add Leave Type',45,'add_leavetype'),(178,'Can change Leave Type',45,'change_leavetype'),(179,'Can delete Leave Type',45,'delete_leavetype'),(180,'Can view Leave Type',45,'view_leavetype'),(181,'Can add Departure',46,'add_departure'),(182,'Can change Departure',46,'change_departure'),(183,'Can delete Departure',46,'delete_departure'),(184,'Can view Departure',46,'view_departure'),(185,'Can add Leave',47,'add_leave'),(186,'Can change Leave',47,'change_leave'),(187,'Can delete Leave',47,'delete_leave'),(188,'Can view Leave',47,'view_leave'),(189,'Can add Leave Balance',48,'add_leavebalance'),(190,'Can change Leave Balance',48,'change_leavebalance'),(191,'Can delete Leave Balance',48,'delete_leavebalance'),(192,'Can view Leave Balance',48,'view_leavebalance'),(193,'Can add Performance Evaluation',49,'add_performanceevaluation'),(194,'Can change Performance Evaluation',49,'change_performanceevaluation'),(195,'Can delete Performance Evaluation',49,'delete_performanceevaluation'),(196,'Can view Performance Evaluation',49,'view_performanceevaluation'),(197,'Can add Report',50,'add_report'),(198,'Can change Report',50,'change_report'),(199,'Can delete Report',50,'delete_report'),(200,'Can view Report',50,'view_report'),(201,'Can add Rank Type',51,'add_ranktype'),(202,'Can change Rank Type',51,'change_ranktype'),(203,'Can delete Rank Type',51,'delete_ranktype'),(204,'Can view Rank Type',51,'view_ranktype'),(205,'Can add Employee Rank',52,'add_employeerank'),(206,'Can change Employee Rank',52,'change_employeerank'),(207,'Can delete Employee Rank',52,'delete_employeerank'),(208,'Can view Employee Rank',52,'view_employeerank'),(209,'Can add Course',53,'add_course'),(210,'Can change Course',53,'change_course'),(211,'Can delete Course',53,'delete_course'),(212,'Can view Course',53,'view_course'),(213,'Can add Employee Course',54,'add_employeecourse'),(214,'Can change Employee Course',54,'change_employeecourse'),(215,'Can delete Employee Course',54,'delete_employeecourse'),(216,'Can view Employee Course',54,'view_employeecourse'),(217,'Can add System Log',55,'add_systemlog'),(218,'Can change System Log',55,'change_systemlog'),(219,'Can delete System Log',55,'delete_systemlog'),(220,'Can view System Log',55,'view_systemlog'),(221,'Can add خطأ النظام',56,'add_systemerror'),(222,'Can change خطأ النظام',56,'change_systemerror'),(223,'Can delete خطأ النظام',56,'delete_systemerror'),(224,'Can view خطأ النظام',56,'view_systemerror'),(225,'Can add نقل موظف',57,'add_employeetransfer'),(226,'Can change نقل موظف',57,'change_employeetransfer'),(227,'Can delete نقل موظف',57,'delete_employeetransfer'),(228,'Can view نقل موظف',57,'view_employeetransfer'),(229,'Can add إشعار',58,'add_notification'),(230,'Can change إشعار',58,'change_notification'),(231,'Can delete إشعار',58,'delete_notification'),(232,'Can view إشعار',58,'view_notification'),(233,'Can add حالة قراءة الإشعار',59,'add_notificationreadstatus'),(234,'Can change حالة قراءة الإشعار',59,'change_notificationreadstatus'),(235,'Can delete حالة قراءة الإشعار',59,'delete_notificationreadstatus'),(236,'Can view حالة قراءة الإشعار',59,'view_notificationreadstatus');
/*!40000 ALTER TABLE `auth_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `backup_backup`
--

DROP TABLE IF EXISTS `backup_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `backup_backup` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` longtext DEFAULT NULL,
  `file` varchar(100) NOT NULL,
  `size` int(10) unsigned NOT NULL CHECK (`size` >= 0),
  `created_at` datetime(6) NOT NULL,
  `created_by` varchar(100) NOT NULL,
  `is_auto` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `backup_backup`
--

LOCK TABLES `backup_backup` WRITE;
/*!40000 ALTER TABLE `backup_backup` DISABLE KEYS */;
/*!40000 ALTER TABLE `backup_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `disciplinary_penalty`
--

DROP TABLE IF EXISTS `disciplinary_penalty`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `disciplinary_penalty` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `description` longtext NOT NULL,
  `decision_number` varchar(50) DEFAULT NULL,
  `decision_date` date DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  `penalty_type_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `disciplinary_penalty_employee_id_7510274f_fk_employees` (`employee_id`),
  KEY `disciplinary_penalty_penalty_type_id_ad6053ba_fk_disciplin` (`penalty_type_id`),
  CONSTRAINT `disciplinary_penalty_employee_id_7510274f_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`),
  CONSTRAINT `disciplinary_penalty_penalty_type_id_ad6053ba_fk_disciplin` FOREIGN KEY (`penalty_type_id`) REFERENCES `disciplinary_penaltytype` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `disciplinary_penalty`
--

LOCK TABLES `disciplinary_penalty` WRITE;
/*!40000 ALTER TABLE `disciplinary_penalty` DISABLE KEYS */;
/*!40000 ALTER TABLE `disciplinary_penalty` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `disciplinary_penaltytype`
--

DROP TABLE IF EXISTS `disciplinary_penaltytype`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `disciplinary_penaltytype` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `disciplinary_penaltytype`
--

LOCK TABLES `disciplinary_penaltytype` WRITE;
/*!40000 ALTER TABLE `disciplinary_penaltytype` DISABLE KEYS */;
/*!40000 ALTER TABLE `disciplinary_penaltytype` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `disciplinary_unpaidleave`
--

DROP TABLE IF EXISTS `disciplinary_unpaidleave`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `disciplinary_unpaidleave` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `reason` longtext DEFAULT NULL,
  `decision_number` varchar(50) DEFAULT NULL,
  `decision_date` date DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `disciplinary_unpaidl_employee_id_5b68956a_fk_employees` (`employee_id`),
  CONSTRAINT `disciplinary_unpaidl_employee_id_5b68956a_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `disciplinary_unpaidleave`
--

LOCK TABLES `disciplinary_unpaidleave` WRITE;
/*!40000 ALTER TABLE `disciplinary_unpaidleave` DISABLE KEYS */;
/*!40000 ALTER TABLE `disciplinary_unpaidleave` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_admin_log`
--

DROP TABLE IF EXISTS `django_admin_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_admin_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action_time` datetime(6) NOT NULL,
  `object_id` longtext DEFAULT NULL,
  `object_repr` varchar(200) NOT NULL,
  `action_flag` smallint(5) unsigned NOT NULL CHECK (`action_flag` >= 0),
  `change_message` longtext NOT NULL,
  `content_type_id` int(11) DEFAULT NULL,
  `user_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `django_admin_log_content_type_id_c4bce8eb_fk_django_co` (`content_type_id`),
  KEY `django_admin_log_user_id_c564eba6_fk_accounts_user_id` (`user_id`),
  CONSTRAINT `django_admin_log_content_type_id_c4bce8eb_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`),
  CONSTRAINT `django_admin_log_user_id_c564eba6_fk_accounts_user_id` FOREIGN KEY (`user_id`) REFERENCES `accounts_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_admin_log`
--

LOCK TABLES `django_admin_log` WRITE;
/*!40000 ALTER TABLE `django_admin_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `django_admin_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_content_type`
--

DROP TABLE IF EXISTS `django_content_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_content_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_label` varchar(100) NOT NULL,
  `model` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `django_content_type_app_label_model_76bd3d3b_uniq` (`app_label`,`model`)
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_content_type`
--

LOCK TABLES `django_content_type` WRITE;
/*!40000 ALTER TABLE `django_content_type` DISABLE KEYS */;
INSERT INTO `django_content_type` VALUES (8,'accounts','user'),(9,'accounts','userpermission'),(1,'admin','logentry'),(14,'announcements','announcement'),(3,'auth','group'),(2,'auth','permission'),(10,'backup','backup'),(4,'contenttypes','contenttype'),(12,'disciplinary','penalty'),(11,'disciplinary','penaltytype'),(13,'disciplinary','unpaidleave'),(7,'employees','annualreport'),(5,'employees','employee'),(16,'employees','externaltransfer'),(18,'employees','internaltransfer'),(17,'employees','maternityleave'),(6,'employees','penalty'),(15,'employees','retiredemployee'),(26,'employment','appointmenttype'),(30,'employment','btecfield'),(33,'employment','btecjob'),(31,'employment','btecteacher'),(19,'employment','department'),(32,'employment','employeeallowance'),(25,'employment','employeeidentification'),(24,'employment','employeeposition'),(22,'employment','employment'),(20,'employment','employmentstatus'),(27,'employment','excessemployee'),(28,'employment','medicalcondition'),(29,'employment','medicalconditionname'),(34,'employment','nonpayment'),(21,'employment','position'),(36,'employment','servicepurchase'),(23,'employment','technicalposition'),(35,'employment','zeroschool'),(38,'file_management','file'),(37,'file_management','filemovement'),(41,'home','approvedform'),(57,'home','employeetransfer'),(43,'home','importantlink'),(39,'home','internaltransfer'),(40,'home','systemsettings'),(42,'home','technicaltransfer'),(46,'leaves','departure'),(47,'leaves','leave'),(48,'leaves','leavebalance'),(45,'leaves','leavetype'),(58,'notifications','notification'),(59,'notifications','notificationreadstatus'),(49,'performance','performanceevaluation'),(53,'ranks','course'),(54,'ranks','employeecourse'),(52,'ranks','employeerank'),(51,'ranks','ranktype'),(50,'reports','report'),(44,'sessions','session'),(56,'system_logs','systemerror'),(55,'system_logs','systemlog');
/*!40000 ALTER TABLE `django_content_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_migrations`
--

DROP TABLE IF EXISTS `django_migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_migrations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `applied` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_migrations`
--

LOCK TABLES `django_migrations` WRITE;
/*!40000 ALTER TABLE `django_migrations` DISABLE KEYS */;
INSERT INTO `django_migrations` VALUES (1,'contenttypes','0001_initial','2025-07-28 06:05:46.401157'),(2,'contenttypes','0002_remove_content_type_name','2025-07-28 06:05:46.471231'),(3,'auth','0001_initial','2025-07-28 06:05:46.640010'),(4,'auth','0002_alter_permission_name_max_length','2025-07-28 06:05:46.678756'),(5,'auth','0003_alter_user_email_max_length','2025-07-28 06:05:46.683379'),(6,'auth','0004_alter_user_username_opts','2025-07-28 06:05:46.688296'),(7,'auth','0005_alter_user_last_login_null','2025-07-28 06:05:46.693476'),(8,'auth','0006_require_contenttypes_0002','2025-07-28 06:05:46.695420'),(9,'auth','0007_alter_validators_add_error_messages','2025-07-28 06:05:46.699102'),(10,'auth','0008_alter_user_username_max_length','2025-07-28 06:05:46.703661'),(11,'auth','0009_alter_user_last_name_max_length','2025-07-28 06:05:46.708564'),(12,'auth','0010_alter_group_name_max_length','2025-07-28 06:05:46.716504'),(13,'auth','0011_update_proxy_permissions','2025-07-28 06:05:46.720836'),(14,'auth','0012_alter_user_first_name_max_length','2025-07-28 06:05:46.725595'),(15,'accounts','0001_initial','2025-07-28 06:05:46.974992'),(16,'accounts','0002_userpermission_visible_pages','2025-07-28 06:05:47.027961'),(17,'accounts','0003_user_is_full_admin','2025-07-28 06:05:47.040653'),(18,'accounts','0004_user_login_count','2025-07-28 06:05:47.051306'),(19,'admin','0001_initial','2025-07-28 06:05:47.131801'),(20,'admin','0002_logentry_remove_auto_add','2025-07-28 06:05:47.137508'),(21,'admin','0003_logentry_add_action_flag_choices','2025-07-28 06:05:47.143376'),(22,'announcements','0001_initial','2025-07-28 06:05:47.211370'),(23,'backup','0001_initial','2025-07-28 06:05:47.275523'),(24,'employees','0001_initial','2025-07-28 06:05:47.410637'),(25,'disciplinary','0001_initial','2025-07-28 06:05:47.532568'),(26,'disciplinary','0002_unpaidleave','2025-07-28 06:05:47.604919'),(27,'employees','0002_employee_gender_alter_employee_school','2025-07-28 06:05:47.621578'),(28,'disciplinary','0002_alter_penalty_options_alter_penaltytype_options_and_more','2025-07-28 06:06:35.679734'),(29,'disciplinary','0003_merge_20250415_1005','2025-07-28 06:06:50.196356'),(30,'employees','0003_retiredemployee','2025-07-28 06:06:50.258795'),(31,'employees','0004_externaltransfer','2025-07-28 06:06:50.326933'),(32,'employees','0005_alter_employee_options_employee_masters_degree_and_more','2025-07-28 06:06:50.357669'),(33,'employees','0006_maternityleave','2025-07-28 06:06:50.412219'),(34,'employees','0007_internaltransfer','2025-07-28 06:06:50.507295'),(35,'employment','0001_initial','2025-07-28 06:06:50.689489'),(36,'employment','0002_technicalposition','2025-07-28 06:06:50.698089'),(37,'employment','0003_technicalposition_department','2025-07-28 06:06:50.739574'),(38,'employment','0004_department_school_gender_department_school_type_and_more','2025-07-28 06:06:50.762866'),(39,'employment','0005_employeeposition','2025-07-28 06:06:50.840409'),(40,'employment','0006_employeeposition_school_level','2025-07-28 06:06:50.853199'),(41,'employment','0007_employeeidentification','2025-07-28 06:06:50.960497'),(42,'employment','0008_appointmenttype_employment_appointment_type','2025-07-28 06:06:51.024368'),(43,'employment','0009_alter_technicalposition_notes','2025-07-28 06:06:51.062196'),(44,'employment','0010_excessemployee','2025-07-28 06:06:51.192366'),(45,'employment','0011_medicalcondition','2025-07-28 06:06:51.264867'),(46,'employment','0012_medicalconditionname_alter_medicalcondition_options_and_more','2025-07-28 06:06:51.426790'),(47,'employment','0013_remove_medicalcondition_condition_type_and_more','2025-07-28 06:06:51.473893'),(48,'employment','0014_alter_excessemployee_current_department_and_more','2025-07-28 06:06:51.813854'),(49,'employment','0015_department_directorate_type','2025-07-28 06:06:51.822424'),(50,'employment','0016_department_highest_grade_department_lowest_grade','2025-07-28 06:06:51.843947'),(51,'employment','0017_alter_department_highest_grade_and_more','2025-07-28 06:06:51.851033'),(52,'employment','0018_update_kindergarten_grades','2025-07-28 06:06:51.868447'),(53,'employment','0019_btecfield_btecteacher','2025-07-28 06:06:52.038347'),(54,'employment','0020_employeeallowance','2025-07-28 06:06:52.106065'),(55,'employment','0021_add_btec_job_step1','2025-07-28 06:06:52.151607'),(56,'employment','0022_add_job_to_btec_teacher','2025-07-28 06:06:52.415940'),(57,'employment','0023_nonpayment','2025-07-28 06:06:52.493772'),(58,'employment','0024_zeroschool','2025-07-28 06:06:52.532926'),(59,'employment','0025_alter_zeroschool_specialization','2025-07-28 06:06:52.537860'),(60,'employment','0026_department_school_national_id_and_more','2025-07-28 06:06:52.554446'),(61,'employment','0027_servicepurchase','2025-07-28 06:06:52.615936'),(62,'employment','0028_servicepurchase_target_school','2025-07-28 06:06:52.642535'),(63,'employment','0029_remove_servicepurchase_purchase_amount_and_more','2025-07-28 06:06:52.730012'),(64,'file_management','0001_initial','2025-07-28 06:06:52.791512'),(65,'file_management','0002_file_filemovement_file','2025-07-28 06:06:52.922999'),(66,'file_management','0003_alter_filemovement_file','2025-07-28 06:06:53.094799'),(67,'home','0001_initial','2025-07-28 06:06:53.109943'),(68,'home','0002_remove_internaltransfer_requested_department_and_more','2025-07-28 06:06:53.208349'),(69,'home','0003_internaltransfer_actual_service','2025-07-28 06:06:53.215354'),(70,'home','0004_systemsettings','2025-07-28 06:06:53.225170'),(71,'home','0005_approvedform','2025-07-28 06:06:53.236676'),(72,'home','0006_technicaltransfer','2025-07-28 06:06:53.259534'),(73,'home','0007_importantlink','2025-07-28 06:06:53.269480'),(74,'home','0008_populate_important_links','2025-07-28 06:07:07.342777'),(75,'home','0009_importantlink_image','2025-07-28 06:07:18.881395'),(76,'home','0010_employeetransfer','2025-07-28 06:07:19.003370'),(77,'leaves','0001_initial','2025-07-28 06:07:19.294480'),(78,'leaves','0002_alter_departure_departure_type_and_more','2025-07-28 06:07:19.341425'),(79,'leaves','0003_alter_leavetype_name','2025-07-28 06:07:19.347424'),(80,'leaves','0004_alter_leavetype_name','2025-07-28 06:07:19.353522'),(81,'leaves','0005_add_casual_leave_type','2025-07-28 06:07:19.390803'),(82,'notifications','0001_initial','2025-07-28 06:07:19.459025'),(83,'notifications','0002_notificationreadstatus','2025-07-28 06:07:19.563465'),(84,'performance','0001_initial','2025-07-28 06:07:19.655308'),(85,'ranks','0001_initial','2025-07-28 06:07:19.758512'),(86,'ranks','0002_courses','2025-07-28 06:07:19.907622'),(87,'ranks','0003_course_hours','2025-07-28 06:07:19.916828'),(88,'reports','0001_initial','2025-07-28 06:07:19.930586'),(89,'reports','0002_alter_report_report_type','2025-07-28 06:07:19.934841'),(90,'reports','0003_alter_report_report_type','2025-07-28 06:07:19.941572'),(91,'reports','0004_alter_report_report_type','2025-07-28 06:07:19.946189'),(92,'sessions','0001_initial','2025-07-28 06:07:19.969205'),(93,'system_logs','0001_initial','2025-07-28 06:07:20.018813'),(94,'system_logs','0002_alter_systemlog_action_alter_systemlog_module','2025-07-28 06:07:20.034998'),(95,'system_logs','0003_systemlog_operating_system','2025-07-28 06:07:20.049411'),(96,'system_logs','0004_systemerror','2025-07-28 06:07:20.168627'),(97,'home','0011_internaltransfer_new_department','2025-07-28 06:22:21.494306');
/*!40000 ALTER TABLE `django_migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_session`
--

DROP TABLE IF EXISTS `django_session`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_session` (
  `session_key` varchar(40) NOT NULL,
  `session_data` longtext NOT NULL,
  `expire_date` datetime(6) NOT NULL,
  PRIMARY KEY (`session_key`),
  KEY `django_session_expire_date_a5c62663` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_session`
--

LOCK TABLES `django_session` WRITE;
/*!40000 ALTER TABLE `django_session` DISABLE KEYS */;
INSERT INTO `django_session` VALUES ('cxi2q4z8uka4rp61xf2sbz3vryo6ho2m','.eJyNV8Fy2yAQ_RefOxmnTjKxjr33CzodBsHapkZAACXxdPrvXZCEhITkXGJJ77HsLrtvyd8doa2_kNaBJYLvqt3j7tv0W03ZFVQA-B-qzvqBaeWtqB8C5aFH3cNPzUH-6LmZgQt1F1zNGH-p4XA41I9P_Hja7w8v34_PT7SmpwPfA31-YeyV16dnuj_uH-sTIK8-1N-PR-DH10dAo9Hcu3CilkAMPYPbVb920BipbwCuGp6IFM4jv4AwC9RDGePgqZBlrDV8Y52ENUw0RltP4DP8ZJQz4OeBVt9IIxR6bW9EtU0NNuNSpVoqiYVorBADo5K1Er8SzEq-lHPyhovFSTDqhVYZ6oBadhn9OGm7wW5wA6uEvxEJ9B3cMs9htxlry0Ip5XNKIfNLK4sDKAWWluVHFY9m7rXDI2OQe2bBCws8GS3EP6eUAlxwChEW7NwL0cUYu4UNqFn9f4bosIS8pcqdwBZ8X3CKDbEglTqjYOlr7g8LMqZQ951fcErOL0kF50uWFs4XXHIeG8Z5wdzXmYQaUSrGwqK8HMMBVxwMtT485vmIoNFOhAYuQKmotjifBqwAxVAywfpOD2DLmOD4mnSjwPTALgphubUvZT4oHQr9u2Bpv64lq1YZKnjf9Dm0_i2pZfa1ppKqxQYd1slsOEYuHBNGCkXtrTKAJxL0oVtSxPzNwEiwwLy2MW-d_Y8L9Y4ag_HFKXkSOMgaqlC0Y_Tdu36H6aEWSewC7KrbVRx1oLUpwVhFVxQV_Jt52H1OJxjxlK4S2K_D0sBmbUICq8lzMtvlr-rHFcf5X2tqQ8CUMd0qxOIo7_nhDtGaqvsZPrqb89AQqc8oFel5AwVrtb2LJ1XAkYqesJiyOGDHt8HIOmXM0gbnC1uN-rNpp5efDY5paykY3o3gY5vo9fmM5REkqHV3YkSL16BA7CrUecEN95eLbiDcw0gGIVXpUQpcNX0bkhuWVuHP8IwFLG9RFKcFE6HuEkVjS2BRps9zlRyA2WiZfU66buGthdGZpfC7ljFw6_sR4CItp3XXjpOt1hdOk7AxulbxviIi3h_pajZQmuotHIXsDiFuR6iUhcGUdlmbXAPBWBHqL2UAvF-gDgsNPZmRViy4tmlQe1P-Od6gl_uPrdolKw2heZRLhOSnWCBMj7EAR4c38PwccS5YlH4eLkKNy2zPoBhqEZtFm2NJbArYhiuE6w8lNU3tmE9Qod5aMR5D9m9NxkyVkm4MGyu7FhhbL1eAPAM5iNiKcMwSMAPzDCRH0mGNE3ZFZuYX2L4qJ5f4Odj5s473jbeKF1tq9_vff3_vFTk:1ugHKk:Uy2ULDoR36lfBxSDXRCEM3opgjt3lRyFnBFIjdL6BXc','2025-08-11 06:27:38.765305');
/*!40000 ALTER TABLE `django_session` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees_annualreport`
--

DROP TABLE IF EXISTS `employees_annualreport`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employees_annualreport` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `year` int(10) unsigned NOT NULL CHECK (`year` >= 0),
  `score` decimal(5,2) NOT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employees_annualreport_employee_id_year_00fe4746_uniq` (`employee_id`,`year`),
  CONSTRAINT `employees_annualrepo_employee_id_a5013c7f_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees_annualreport`
--

LOCK TABLES `employees_annualreport` WRITE;
/*!40000 ALTER TABLE `employees_annualreport` DISABLE KEYS */;
/*!40000 ALTER TABLE `employees_annualreport` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees_employee`
--

DROP TABLE IF EXISTS `employees_employee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employees_employee` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ministry_number` varchar(20) NOT NULL,
  `national_id` varchar(20) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `qualification` varchar(255) NOT NULL,
  `specialization` varchar(255) NOT NULL,
  `hire_date` date NOT NULL,
  `school` varchar(255) NOT NULL,
  `birth_date` date NOT NULL,
  `address` longtext NOT NULL,
  `phone_number` varchar(20) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `gender` varchar(10) NOT NULL,
  `masters_degree` varchar(255) DEFAULT NULL,
  `phd_degree` varchar(255) DEFAULT NULL,
  `post_graduate_diploma` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ministry_number` (`ministry_number`),
  UNIQUE KEY `national_id` (`national_id`)
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees_employee`
--

LOCK TABLES `employees_employee` WRITE;
/*!40000 ALTER TABLE `employees_employee` DISABLE KEYS */;
INSERT INTO `employees_employee` VALUES (1,'178421','9881040020','احمد خليل عبد الرحمن العمري','بكالوريس','نظم معومات حاسوبية','2012-08-26','شؤون الموظفين','1988-09-17','المفرق - حي الزهور','0778986898','2025-04-15 08:11:18.387000','2025-07-17 11:09:45.314000','male','ماجستير ادارة تربوية',NULL,NULL),(33,'111929','9761049979','اجود فهد عيط الكعيبر','بكالوريس','اللغة العربية','2003-01-01','ابو بكر الصديق الاساسيه للبنين','1987-10-08','المفرق','0779945325','2025-06-18 10:43:10.132000','2025-06-19 10:15:46.493000','male',NULL,NULL,NULL),(34,'176368','9801054084','احمد عبد الجليل احمد شدايده','بكالوريس','رياضيات','2003-01-02','ابو بكر الصديق الاساسيه للبنين','1987-10-09','المفرق','0796162780','2025-06-18 10:43:10.153000','2025-06-19 10:17:59.903000','male',NULL,NULL,NULL),(35,'141164','9781053626','احمد عبد اللطيف اسعد سعاده','بكالوريس','معلم صف','2003-01-03','ابو بكر الصديق الاساسيه للبنين','1987-10-10','المفرق','776692892','2025-06-18 10:43:10.175000','2025-06-22 15:49:14.531000','male',NULL,NULL,NULL),(36,'172496','9841050317','احمد مفلح قبلان العليمات','بكالوريس','اللغة العربية','2003-01-04','ابو بكر الصديق الاساسيه للبنين','1987-10-11','المفرق','776983414','2025-06-18 10:43:10.197000','2025-07-20 11:16:13.681000','male',NULL,NULL,NULL),(37,'182471','9911045812','احمد نواف احمد ارقيبات','بكالوريس','مرشد','2003-01-05','ابو بكر الصديق الاساسيه للبنين','1987-10-12','المفرق','796180163','2025-06-18 10:43:10.218000','2025-07-20 11:17:04.617000','male',NULL,NULL,NULL),(38,'138570','9821000239','انس اكرم الشيخ احمد احمد','بكالوريس','معلم صف','2003-01-06','ابو بكر الصديق الاساسيه للبنين','1987-10-13','المفرق','779154009','2025-06-18 10:43:10.239000','2025-07-20 11:14:31.147000','male',NULL,NULL,NULL),(39,'161982','9831059704','ايمن عقله علي الحراحشه','بكالوريس','حاسوب','2003-01-07','ابو بكر الصديق الاساسيه للبنين','1987-10-14','المفرق','772200969','2025-06-18 10:43:10.263000','2025-07-20 11:15:59.468000','male',NULL,NULL,NULL),(40,'185386','9911018782','جمال عبد الله ندى الدحيم','بكالوريس','جغرافيا','2003-01-08','ابو بكر الصديق الاساسيه للبنين','1987-10-15','المفرق','779646108','2025-06-18 10:43:10.282000','2025-07-20 11:17:18.064000','male',NULL,NULL,NULL),(41,'180563','9871044037','حازم فايز جميل اشبيلات','بكالوريس','شريعة','2003-01-09','ابو بكر الصديق الاساسيه للبنين','1987-10-16','المفرق','776110422','2025-06-18 10:43:10.303000','2025-07-20 11:16:54.630000','male',NULL,NULL,NULL),(42,'162041','9851048847','خير الله احمد حصاد المشاقبة','بكالوريس','معلم صف','2003-01-10','ابو بكر الصديق الاساسيه للبنين','1987-10-17','المفرق','772495635','2025-06-18 10:43:10.321000','2025-07-20 11:16:06.780000','male',NULL,NULL,NULL),(43,'102910','9731047552','شريف سويلم ارشيد العموش','بكالوريس','اللغة العربية','2003-01-11','ابو بكر الصديق الاساسيه للبنين','1987-10-18','المفرق','0776450525','2025-06-18 10:43:10.340000','2025-07-01 10:24:13.137000','male',NULL,NULL,NULL),(44,'155322','9841053684','عامر شاهر خليف الشمري','بكالوريس','شريعة','2003-01-12','ابو بكر الصديق الاساسيه للبنين','1987-10-19','المفرق','777215321','2025-06-18 10:43:10.358000','2025-07-20 11:15:17.730000','male',NULL,NULL,NULL),(45,'121383','9701044877','عامر محمد مقبل الخزاعله','بكالوريس','علوم الجيولوجيا','2003-01-13','ابو بكر الصديق الاساسيه للبنين','1987-10-20','المفرق','777722740','2025-06-18 10:43:10.379000','2025-07-20 11:14:08.267000','male',NULL,NULL,NULL),(46,'172897','9881038948','عبد اللطيف احمد عبد اللطيف عليمات','بكالوريس','اللغة العربية','2003-01-14','ابو بكر الصديق الاساسيه للبنين','1987-10-21','المفرق','776136503','2025-06-18 10:43:10.398000','2025-07-20 11:16:20.067000','male',NULL,NULL,NULL),(47,'153014','9791054126','عبد الله احمد عبد اللطيف عليمات','بكالوريس','جغرافيا','2003-01-15','ابو بكر الصديق الاساسيه للبنين','1987-10-22','المفرق','772003714','2025-06-18 10:43:10.417000','2025-07-20 11:15:00.900000','male',NULL,NULL,NULL),(48,'159164','9831051077','عبدالله محمود علي السرديه','بكالوريس','رياضيات عامه','2003-01-16','ابو بكر الصديق الاساسيه للبنين','1987-10-23','المفرق','775856421','2025-06-18 10:43:10.436000','2025-07-20 11:15:41.078000','male',NULL,NULL,NULL),(49,'161203','9801012369','عدنان عقله علي الحراحشه','بكالوريس','اللغة العربية','2003-01-17','ابو بكر الصديق الاساسيه للبنين','1987-10-24','المفرق','780075543','2025-06-18 10:43:10.459000','2025-07-20 11:15:49.698000','male',NULL,NULL,NULL),(50,'179342','9911018076','علي ابراهيم حسين عليمات','بكالوريس','اللغة العربية','2003-01-18','ابو بكر الصديق الاساسيه للبنين','1987-10-25','المفرق','772457125','2025-06-18 10:43:10.478000','2025-07-20 11:16:45.946000','male',NULL,NULL,NULL),(51,'144584','9821054821','علي اسماعيل فارس عليوي','بكالوريس','فيزياء','2003-01-19','ابو بكر الصديق الاساسيه للبنين','1987-10-26','المفرق','788680239','2025-06-18 10:43:10.501000','2025-07-20 11:14:45.399000','male',NULL,NULL,NULL),(52,'182736','9881040290','علي محمد علي العرقان','بكالوريس','رياضيات','2003-01-20','ابو بكر الصديق الاساسيه للبنين','1987-10-27','المفرق','798135353','2025-06-18 10:43:10.526000','2025-07-20 11:17:11.430000','male',NULL,NULL,NULL),(53,'124401','9691046763','عمار صالح عطيه مناصره','بكالوريس','تربية ابتدائية','2003-01-21','ابو بكر الصديق الاساسيه للبنين','1987-10-28','المفرق','779266273','2025-06-18 10:43:10.548000','2025-07-20 11:14:20.800000','male',NULL,NULL,NULL),(54,'94216','9731048228','فالح عوض محمد المزاوده','بكالوريس','اللغة الانجليزية','2003-01-22','ابو بكر الصديق الاساسيه للبنين','1987-10-29','المفرق','772046374','2025-06-18 10:43:10.572000','2025-06-22 15:55:28.870000','male',NULL,NULL,NULL),(55,'104281','9761010723','كامل خميس سالم ابو جراد','بكالوريس','معلم صف','2003-01-23','ابو بكر الصديق الاساسيه للبنين','1987-10-30','المفرق','787606856','2025-06-18 10:43:10.590000','2025-07-01 10:24:47.031000','male',NULL,NULL,NULL),(56,'177648','9781053405','ماهر مصطفى محمد عليمات','بكالوريس','شريعة','2003-01-24','ابو بكر الصديق الاساسيه للبنين','1987-10-31','المفرق','781054582','2025-06-18 10:43:10.613000','2025-07-20 11:16:28.963000','male',NULL,NULL,NULL),(57,'146241','9811054268','محمد احمد عليان الخزاعله','بكالوريس','اللغة الانجليزية','2003-01-25','ابو بكر الصديق الاساسيه للبنين','1987-11-01','المفرق','790373759','2025-06-18 10:43:10.633000','2025-07-20 11:14:52.759000','male',NULL,NULL,NULL),(58,'201633','9941027453','محمد جميل يونس الزغارنه','بكالوريس','العلوم','2003-01-26','ابو بكر الصديق الاساسيه للبنين','1987-11-02','المفرق','775263993','2025-06-18 10:43:10.655000','2025-07-20 11:17:24.982000','male',NULL,NULL,NULL),(59,'155988','9841051732','محمد عبد الحميد عبده شعبان','بكالوريس','نظم تكيف','2003-01-27','ابو بكر الصديق الاساسيه للبنين','1987-11-03','المفرق','776040453','2025-06-18 10:43:10.678000','2025-07-20 11:15:25.146000','male',NULL,NULL,NULL),(60,'143450','9821025569','محمد علي محمد المغربي','بكالوريس','رياضيات','2003-01-28','ابو بكر الصديق الاساسيه للبنين','1987-11-04','المفرق','788158106','2025-06-18 10:43:10.701000','2025-07-20 11:14:38.697000','male',NULL,NULL,NULL),(61,'206244','9861045853','محمد مجد محمد سليمان ابو عويضه','بكالوريس','رياضة','2003-01-29','ابو بكر الصديق الاساسيه للبنين','1987-11-05','المفرق','779293030','2025-06-18 10:43:10.727000','2025-07-20 11:17:41.146000','male',NULL,NULL,NULL),(62,'105176','9761049287','محمود درويش العلي الشرعه','بكالوريس','اللغة الانجليزية','2003-01-30','ابو بكر الصديق الاساسيه للبنين','1987-11-06','المفرق','785011133','2025-06-18 10:43:10.753000','2025-06-22 16:10:04.042000','male',NULL,NULL,NULL),(63,'178053','9861007843','معتصم امين علي الخزاعله','بكالوريس','رياضة','2003-01-31','ابو بكر الصديق الاساسيه للبنين','1987-11-07','المفرق','775705537','2025-06-18 10:43:10.774000','2025-07-20 11:16:38.548000','male',NULL,NULL,NULL),(64,'206254','9881050536','موفق سليمان راشد الشديفات','بكالوريس','لغة انجليزية','2003-02-01','ابو بكر الصديق الاساسيه للبنين','1987-11-08','المفرق','772383887','2025-06-18 10:43:10.794000','2025-07-20 11:17:48.353000','male',NULL,NULL,NULL),(65,'155280','9801054000','وصفي حسني خليف السلامه','بكالوريس','عربي','2003-02-02','ابو بكر الصديق الاساسيه للبنين','1987-11-09','المفرق','776692892','2025-06-18 10:43:10.813000','2025-07-20 11:15:09.946000','male',NULL,NULL,NULL),(66,'204602','9861046312','وليد احمد عوده العسيلي','بكالوريس','لغة انجليزية','2003-02-03','ابو بكر الصديق الاساسيه للبنين','1987-11-10','المفرق','789606157','2025-06-18 10:43:10.838000','2025-07-20 11:17:34.785000','male',NULL,NULL,NULL),(67,'103042','9711039586','ياسر فائق سليمان العبد اللطيف','بكالوريس','رياضيات عامه','2003-02-04','ابو بكر الصديق الاساسيه للبنين','1987-11-11','المفرق','0797542321','2025-06-18 10:43:10.858000','2025-07-01 10:20:39.479000','male',NULL,NULL,NULL),(68,'157382','9861046477','يوسف احمد سليمان السعود','بكالوريس','تربية خاصة','2003-02-05','ابو بكر الصديق الاساسيه للبنين','1987-11-12','المفرق','798481436','2025-06-18 10:43:10.887000','2025-07-20 11:15:33.014000','male',NULL,NULL,NULL),(69,'3203537','9851023034','عبد الله محمد مهنا الحراحشه','دون الثانوية','لا يوجد','2003-02-06','ابو بكر الصديق الاساسيه للبنين','1987-11-13','المفرق','779753283','2025-06-18 10:43:10.906000','2025-07-20 11:17:55.362000','male',NULL,NULL,NULL),(70,'3208244','9831051352','ماجد عايد عبد الله الخوالده','دون الثانوية','لا يوجد','2003-02-07','ابو بكر الصديق الاساسيه للبنين','1987-11-14','المفرق','779753283','2025-06-18 10:43:10.928000','2025-07-20 11:18:04.928000','male',NULL,NULL,NULL);
/*!40000 ALTER TABLE `employees_employee` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees_externaltransfer`
--

DROP TABLE IF EXISTS `employees_externaltransfer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employees_externaltransfer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `transfer_date` date NOT NULL,
  `destination_directorate` varchar(200) NOT NULL,
  `transfer_reason` varchar(200) NOT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_id` (`employee_id`),
  CONSTRAINT `employees_externaltr_employee_id_74cace6f_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees_externaltransfer`
--

LOCK TABLES `employees_externaltransfer` WRITE;
/*!40000 ALTER TABLE `employees_externaltransfer` DISABLE KEYS */;
/*!40000 ALTER TABLE `employees_externaltransfer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees_internaltransfer`
--

DROP TABLE IF EXISTS `employees_internaltransfer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employees_internaltransfer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `previous_department` varchar(255) NOT NULL,
  `new_department` varchar(255) NOT NULL,
  `transfer_date` date NOT NULL,
  `start_date` date NOT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `employees_internaltr_employee_id_c446f1b0_fk_employees` (`employee_id`),
  CONSTRAINT `employees_internaltr_employee_id_c446f1b0_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees_internaltransfer`
--

LOCK TABLES `employees_internaltransfer` WRITE;
/*!40000 ALTER TABLE `employees_internaltransfer` DISABLE KEYS */;
/*!40000 ALTER TABLE `employees_internaltransfer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees_maternityleave`
--

DROP TABLE IF EXISTS `employees_maternityleave`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employees_maternityleave` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `employees_maternityl_employee_id_9ca1d523_fk_employees` (`employee_id`),
  CONSTRAINT `employees_maternityl_employee_id_9ca1d523_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees_maternityleave`
--

LOCK TABLES `employees_maternityleave` WRITE;
/*!40000 ALTER TABLE `employees_maternityleave` DISABLE KEYS */;
/*!40000 ALTER TABLE `employees_maternityleave` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees_penalty`
--

DROP TABLE IF EXISTS `employees_penalty`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employees_penalty` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `description` longtext NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `employees_penalty_employee_id_d0f94af0_fk_employees_employee_id` (`employee_id`),
  CONSTRAINT `employees_penalty_employee_id_d0f94af0_fk_employees_employee_id` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees_penalty`
--

LOCK TABLES `employees_penalty` WRITE;
/*!40000 ALTER TABLE `employees_penalty` DISABLE KEYS */;
/*!40000 ALTER TABLE `employees_penalty` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees_retiredemployee`
--

DROP TABLE IF EXISTS `employees_retiredemployee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employees_retiredemployee` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `retirement_date` date NOT NULL,
  `retirement_reason` varchar(255) NOT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_id` (`employee_id`),
  CONSTRAINT `employees_retiredemp_employee_id_239da6be_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees_retiredemployee`
--

LOCK TABLES `employees_retiredemployee` WRITE;
/*!40000 ALTER TABLE `employees_retiredemployee` DISABLE KEYS */;
/*!40000 ALTER TABLE `employees_retiredemployee` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_appointmenttype`
--

DROP TABLE IF EXISTS `employment_appointmenttype`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_appointmenttype` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_appointmenttype`
--

LOCK TABLES `employment_appointmenttype` WRITE;
/*!40000 ALTER TABLE `employment_appointmenttype` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_appointmenttype` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_btecfield`
--

DROP TABLE IF EXISTS `employment_btecfield`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_btecfield` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_btecfield`
--

LOCK TABLES `employment_btecfield` WRITE;
/*!40000 ALTER TABLE `employment_btecfield` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_btecfield` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_btecjob`
--

DROP TABLE IF EXISTS `employment_btecjob`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_btecjob` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_btecjob`
--

LOCK TABLES `employment_btecjob` WRITE;
/*!40000 ALTER TABLE `employment_btecjob` DISABLE KEYS */;
INSERT INTO `employment_btecjob` VALUES (1,'معلم','وظيفة معلم افتراضية','2025-07-28 06:06:52.147493','2025-07-28 06:06:52.147510');
/*!40000 ALTER TABLE `employment_btecjob` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_btecteacher`
--

DROP TABLE IF EXISTS `employment_btecteacher`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_btecteacher` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  `field_id` bigint(20) NOT NULL,
  `job_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employment_btecteacher_employee_id_field_id_job_id_5f20d1d1_uniq` (`employee_id`,`field_id`,`job_id`),
  KEY `employment_btecteach_field_id_2727eaa1_fk_employmen` (`field_id`),
  KEY `employment_btecteacher_employee_id_7c9741ff` (`employee_id`),
  KEY `employment_btecteacher_job_id_85462896_fk_employment_btecjob_id` (`job_id`),
  CONSTRAINT `employment_btecteach_employee_id_7c9741ff_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`),
  CONSTRAINT `employment_btecteach_field_id_2727eaa1_fk_employmen` FOREIGN KEY (`field_id`) REFERENCES `employment_btecfield` (`id`),
  CONSTRAINT `employment_btecteacher_job_id_85462896_fk_employment_btecjob_id` FOREIGN KEY (`job_id`) REFERENCES `employment_btecjob` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_btecteacher`
--

LOCK TABLES `employment_btecteacher` WRITE;
/*!40000 ALTER TABLE `employment_btecteacher` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_btecteacher` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_department`
--

DROP TABLE IF EXISTS `employment_department`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_department` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `school_gender` varchar(20) DEFAULT NULL,
  `school_type` varchar(20) DEFAULT NULL,
  `workplace` varchar(20) NOT NULL,
  `directorate_type` varchar(20) DEFAULT NULL,
  `highest_grade` varchar(20) DEFAULT NULL,
  `lowest_grade` varchar(20) DEFAULT NULL,
  `school_national_id` varchar(50) DEFAULT NULL,
  `school_ownership` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_department`
--

LOCK TABLES `employment_department` WRITE;
/*!40000 ALTER TABLE `employment_department` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_department` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_employeeallowance`
--

DROP TABLE IF EXISTS `employment_employeeallowance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_employeeallowance` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `education_allowance` varchar(3) NOT NULL,
  `adjustment_allowance` varchar(3) NOT NULL,
  `transportation_allowance` varchar(3) NOT NULL,
  `supervisory_allowance` varchar(3) NOT NULL,
  `technical_allowance` varchar(3) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_id` (`employee_id`),
  CONSTRAINT `employment_employeea_employee_id_43f1c503_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_employeeallowance`
--

LOCK TABLES `employment_employeeallowance` WRITE;
/*!40000 ALTER TABLE `employment_employeeallowance` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_employeeallowance` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_employeeidentification`
--

DROP TABLE IF EXISTS `employment_employeeidentification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_employeeidentification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ministry_number` varchar(20) NOT NULL,
  `national_id` varchar(20) NOT NULL,
  `id_number` varchar(20) NOT NULL,
  `birth_day` int(11) NOT NULL,
  `birth_month` int(11) NOT NULL,
  `birth_year` int(11) NOT NULL,
  `address` longtext NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `employment_employeei_employee_id_3134fa6c_fk_employees` (`employee_id`),
  CONSTRAINT `employment_employeei_employee_id_3134fa6c_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_employeeidentification`
--

LOCK TABLES `employment_employeeidentification` WRITE;
/*!40000 ALTER TABLE `employment_employeeidentification` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_employeeidentification` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_employeeposition`
--

DROP TABLE IF EXISTS `employment_employeeposition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_employeeposition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `date_obtained` date NOT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  `position_id` bigint(20) NOT NULL,
  `school_level` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `employment_employeep_employee_id_afcf52bf_fk_employees` (`employee_id`),
  KEY `employment_employeep_position_id_76e6084f_fk_employmen` (`position_id`),
  CONSTRAINT `employment_employeep_employee_id_afcf52bf_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`),
  CONSTRAINT `employment_employeep_position_id_76e6084f_fk_employmen` FOREIGN KEY (`position_id`) REFERENCES `employment_position` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_employeeposition`
--

LOCK TABLES `employment_employeeposition` WRITE;
/*!40000 ALTER TABLE `employment_employeeposition` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_employeeposition` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_employment`
--

DROP TABLE IF EXISTS `employment_employment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_employment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `is_current` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `department_id` bigint(20) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  `status_id` bigint(20) NOT NULL,
  `position_id` bigint(20) NOT NULL,
  `appointment_type_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `employment_employmen_department_id_2ac2427d_fk_employmen` (`department_id`),
  KEY `employment_employmen_employee_id_2c8f0053_fk_employees` (`employee_id`),
  KEY `employment_employmen_status_id_51017b43_fk_employmen` (`status_id`),
  KEY `employment_employmen_position_id_36b44a13_fk_employmen` (`position_id`),
  KEY `employment_employmen_appointment_type_id_039e8bd8_fk_employmen` (`appointment_type_id`),
  CONSTRAINT `employment_employmen_appointment_type_id_039e8bd8_fk_employmen` FOREIGN KEY (`appointment_type_id`) REFERENCES `employment_appointmenttype` (`id`),
  CONSTRAINT `employment_employmen_department_id_2ac2427d_fk_employmen` FOREIGN KEY (`department_id`) REFERENCES `employment_department` (`id`),
  CONSTRAINT `employment_employmen_employee_id_2c8f0053_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`),
  CONSTRAINT `employment_employmen_position_id_36b44a13_fk_employmen` FOREIGN KEY (`position_id`) REFERENCES `employment_position` (`id`),
  CONSTRAINT `employment_employmen_status_id_51017b43_fk_employmen` FOREIGN KEY (`status_id`) REFERENCES `employment_employmentstatus` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_employment`
--

LOCK TABLES `employment_employment` WRITE;
/*!40000 ALTER TABLE `employment_employment` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_employment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_employmentstatus`
--

DROP TABLE IF EXISTS `employment_employmentstatus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_employmentstatus` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `description` longtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_employmentstatus`
--

LOCK TABLES `employment_employmentstatus` WRITE;
/*!40000 ALTER TABLE `employment_employmentstatus` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_employmentstatus` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_excessemployee`
--

DROP TABLE IF EXISTS `employment_excessemployee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_excessemployee` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `reason` longtext NOT NULL,
  `status` varchar(20) NOT NULL,
  `resolution_notes` longtext DEFAULT NULL,
  `resolution_date` date DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `current_department_id` bigint(20) DEFAULT NULL,
  `employee_id` bigint(20) NOT NULL,
  `position_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `employment_excessemp_employee_id_060b4ccb_fk_employees` (`employee_id`),
  KEY `employment_excessemp_current_department_i_56145c45_fk_employmen` (`current_department_id`),
  KEY `employment_excessemp_position_id_0e5c2f32_fk_employmen` (`position_id`),
  CONSTRAINT `employment_excessemp_current_department_i_56145c45_fk_employmen` FOREIGN KEY (`current_department_id`) REFERENCES `employment_department` (`id`),
  CONSTRAINT `employment_excessemp_employee_id_060b4ccb_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`),
  CONSTRAINT `employment_excessemp_position_id_0e5c2f32_fk_employmen` FOREIGN KEY (`position_id`) REFERENCES `employment_position` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_excessemployee`
--

LOCK TABLES `employment_excessemployee` WRITE;
/*!40000 ALTER TABLE `employment_excessemployee` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_excessemployee` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_medicalcondition`
--

DROP TABLE IF EXISTS `employment_medicalcondition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_medicalcondition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `description` longtext NOT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  `medical_report_date` date NOT NULL,
  `condition_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `employment_medicalco_employee_id_bdb3001f_fk_employees` (`employee_id`),
  KEY `employment_medicalco_condition_id_296019a2_fk_employmen` (`condition_id`),
  CONSTRAINT `employment_medicalco_condition_id_296019a2_fk_employmen` FOREIGN KEY (`condition_id`) REFERENCES `employment_medicalconditionname` (`id`),
  CONSTRAINT `employment_medicalco_employee_id_bdb3001f_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_medicalcondition`
--

LOCK TABLES `employment_medicalcondition` WRITE;
/*!40000 ALTER TABLE `employment_medicalcondition` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_medicalcondition` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_medicalconditionname`
--

DROP TABLE IF EXISTS `employment_medicalconditionname`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_medicalconditionname` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_medicalconditionname`
--

LOCK TABLES `employment_medicalconditionname` WRITE;
/*!40000 ALTER TABLE `employment_medicalconditionname` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_medicalconditionname` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_nonpayment`
--

DROP TABLE IF EXISTS `employment_nonpayment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_nonpayment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `days_count` int(10) unsigned NOT NULL CHECK (`days_count` >= 0),
  `notes` longtext DEFAULT NULL,
  `is_transferred` tinyint(1) NOT NULL,
  `transfer_date` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employment_nonpayment_employee_id_date_05d1b36c_uniq` (`employee_id`,`date`),
  CONSTRAINT `employment_nonpaymen_employee_id_f19da72e_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_nonpayment`
--

LOCK TABLES `employment_nonpayment` WRITE;
/*!40000 ALTER TABLE `employment_nonpayment` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_nonpayment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_position`
--

DROP TABLE IF EXISTS `employment_position`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_position` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_position`
--

LOCK TABLES `employment_position` WRITE;
/*!40000 ALTER TABLE `employment_position` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_position` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_servicepurchase`
--

DROP TABLE IF EXISTS `employment_servicepurchase`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_servicepurchase` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `notes` longtext DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  `target_school` varchar(200) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `employment_servicepu_employee_id_c157dfde_fk_employees` (`employee_id`),
  CONSTRAINT `employment_servicepu_employee_id_c157dfde_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_servicepurchase`
--

LOCK TABLES `employment_servicepurchase` WRITE;
/*!40000 ALTER TABLE `employment_servicepurchase` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_servicepurchase` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_technicalposition`
--

DROP TABLE IF EXISTS `employment_technicalposition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_technicalposition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `specialization` varchar(255) NOT NULL,
  `vacancies` int(10) unsigned NOT NULL CHECK (`vacancies` >= 0),
  `gender` varchar(10) NOT NULL,
  `notes` longtext NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `department_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `employment_technical_department_id_941c9ee0_fk_employmen` (`department_id`),
  CONSTRAINT `employment_technical_department_id_941c9ee0_fk_employmen` FOREIGN KEY (`department_id`) REFERENCES `employment_department` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_technicalposition`
--

LOCK TABLES `employment_technicalposition` WRITE;
/*!40000 ALTER TABLE `employment_technicalposition` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_technicalposition` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_zeroschool`
--

DROP TABLE IF EXISTS `employment_zeroschool`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employment_zeroschool` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `school_name` varchar(255) NOT NULL,
  `specialization` varchar(20) NOT NULL,
  `vacancies_count` int(10) unsigned NOT NULL CHECK (`vacancies_count` >= 0),
  `justification` longtext NOT NULL,
  `actions` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employment_zeroschool_school_name_specialization_06b8aed7_uniq` (`school_name`,`specialization`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_zeroschool`
--

LOCK TABLES `employment_zeroschool` WRITE;
/*!40000 ALTER TABLE `employment_zeroschool` DISABLE KEYS */;
/*!40000 ALTER TABLE `employment_zeroschool` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `file_management_file`
--

DROP TABLE IF EXISTS `file_management_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `file_management_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `file_number` varchar(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `status` varchar(20) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `file_number` (`file_number`),
  KEY `file_management_file_employee_id_4df74bf4_fk_employees` (`employee_id`),
  CONSTRAINT `file_management_file_employee_id_4df74bf4_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `file_management_file`
--

LOCK TABLES `file_management_file` WRITE;
/*!40000 ALTER TABLE `file_management_file` DISABLE KEYS */;
/*!40000 ALTER TABLE `file_management_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `file_management_filemovement`
--

DROP TABLE IF EXISTS `file_management_filemovement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `file_management_filemovement` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `checkout_date` date NOT NULL,
  `return_date` date DEFAULT NULL,
  `action_taken` longtext DEFAULT NULL,
  `status` varchar(10) NOT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  `file_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `file_management_file_employee_id_4a548774_fk_employees` (`employee_id`),
  KEY `file_management_file_file_id_8e328cbf_fk_file_mana` (`file_id`),
  CONSTRAINT `file_management_file_employee_id_4a548774_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`),
  CONSTRAINT `file_management_file_file_id_8e328cbf_fk_file_mana` FOREIGN KEY (`file_id`) REFERENCES `file_management_file` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `file_management_filemovement`
--

LOCK TABLES `file_management_filemovement` WRITE;
/*!40000 ALTER TABLE `file_management_filemovement` DISABLE KEYS */;
/*!40000 ALTER TABLE `file_management_filemovement` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `home_approvedform`
--

DROP TABLE IF EXISTS `home_approvedform`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `home_approvedform` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `description` longtext DEFAULT NULL,
  `file` varchar(100) NOT NULL,
  `file_type` varchar(50) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `order` int(10) unsigned NOT NULL CHECK (`order` >= 0),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `home_approvedform`
--

LOCK TABLES `home_approvedform` WRITE;
/*!40000 ALTER TABLE `home_approvedform` DISABLE KEYS */;
/*!40000 ALTER TABLE `home_approvedform` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `home_employeetransfer`
--

DROP TABLE IF EXISTS `home_employeetransfer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `home_employeetransfer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ministry_number` varchar(20) NOT NULL,
  `employee_name` varchar(100) NOT NULL,
  `specialization` varchar(100) DEFAULT NULL,
  `actual_service` varchar(50) DEFAULT NULL,
  `current_department` varchar(100) NOT NULL,
  `transfer_type` varchar(30) NOT NULL,
  `endorsement` varchar(50) NOT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `new_department_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `home_employeetransfe_new_department_id_aeae3a93_fk_employmen` (`new_department_id`),
  CONSTRAINT `home_employeetransfe_new_department_id_aeae3a93_fk_employmen` FOREIGN KEY (`new_department_id`) REFERENCES `employment_department` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `home_employeetransfer`
--

LOCK TABLES `home_employeetransfer` WRITE;
/*!40000 ALTER TABLE `home_employeetransfer` DISABLE KEYS */;
/*!40000 ALTER TABLE `home_employeetransfer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `home_importantlink`
--

DROP TABLE IF EXISTS `home_importantlink`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `home_importantlink` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `url` varchar(200) NOT NULL,
  `description` longtext DEFAULT NULL,
  `favicon_url` varchar(200) DEFAULT NULL,
  `order` int(10) unsigned NOT NULL CHECK (`order` >= 0),
  `is_active` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `image` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `home_importantlink`
--

LOCK TABLES `home_importantlink` WRITE;
/*!40000 ALTER TABLE `home_importantlink` DISABLE KEYS */;
/*!40000 ALTER TABLE `home_importantlink` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `home_internaltransfer`
--

DROP TABLE IF EXISTS `home_internaltransfer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `home_internaltransfer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `employee_name` varchar(100) NOT NULL,
  `employee_id` varchar(20) NOT NULL,
  `current_department` varchar(100) NOT NULL,
  `reason` longtext NOT NULL,
  `phone_number` varchar(20) NOT NULL,
  `email` varchar(254) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `status` varchar(20) NOT NULL,
  `notes` longtext DEFAULT NULL,
  `address` longtext DEFAULT NULL,
  `edit_token` varchar(50) DEFAULT NULL,
  `first_choice` varchar(100) NOT NULL,
  `gender` varchar(10) NOT NULL,
  `hire_date` date DEFAULT NULL,
  `last_position` varchar(100) DEFAULT NULL,
  `last_rank` varchar(100) DEFAULT NULL,
  `ministry_number` varchar(20) NOT NULL,
  `qualification` varchar(100) DEFAULT NULL,
  `second_choice` varchar(100) DEFAULT NULL,
  `specialization` varchar(100) DEFAULT NULL,
  `third_choice` varchar(100) DEFAULT NULL,
  `updated_at` datetime(6) NOT NULL,
  `actual_service` varchar(50) DEFAULT NULL,
  `new_department_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `home_internaltransfe_new_department_id_34d891e3_fk_employmen` (`new_department_id`),
  CONSTRAINT `home_internaltransfe_new_department_id_34d891e3_fk_employmen` FOREIGN KEY (`new_department_id`) REFERENCES `employment_department` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `home_internaltransfer`
--

LOCK TABLES `home_internaltransfer` WRITE;
/*!40000 ALTER TABLE `home_internaltransfer` DISABLE KEYS */;
/*!40000 ALTER TABLE `home_internaltransfer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `home_systemsettings`
--

DROP TABLE IF EXISTS `home_systemsettings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `home_systemsettings` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `internal_transfer_enabled` tinyint(1) NOT NULL,
  `internal_transfer_message` longtext NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `home_systemsettings`
--

LOCK TABLES `home_systemsettings` WRITE;
/*!40000 ALTER TABLE `home_systemsettings` DISABLE KEYS */;
INSERT INTO `home_systemsettings` VALUES (1,1,'لقد انتهت فترة تقديم طلبات النقل الداخلي');
/*!40000 ALTER TABLE `home_systemsettings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `home_technicaltransfer`
--

DROP TABLE IF EXISTS `home_technicaltransfer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `home_technicaltransfer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ministry_number` varchar(20) NOT NULL,
  `employee_name` varchar(100) NOT NULL,
  `current_department` varchar(100) NOT NULL,
  `new_department` varchar(100) NOT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `home_technicaltransfer`
--

LOCK TABLES `home_technicaltransfer` WRITE;
/*!40000 ALTER TABLE `home_technicaltransfer` DISABLE KEYS */;
/*!40000 ALTER TABLE `home_technicaltransfer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `leaves_departure`
--

DROP TABLE IF EXISTS `leaves_departure`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `leaves_departure` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `departure_type` varchar(20) NOT NULL,
  `date` date NOT NULL,
  `time_from` time(6) NOT NULL,
  `time_to` time(6) NOT NULL,
  `reason` longtext DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `leaves_departure_employee_id_699a95f6_fk_employees_employee_id` (`employee_id`),
  CONSTRAINT `leaves_departure_employee_id_699a95f6_fk_employees_employee_id` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `leaves_departure`
--

LOCK TABLES `leaves_departure` WRITE;
/*!40000 ALTER TABLE `leaves_departure` DISABLE KEYS */;
/*!40000 ALTER TABLE `leaves_departure` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `leaves_leave`
--

DROP TABLE IF EXISTS `leaves_leave`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `leaves_leave` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `days_count` int(10) unsigned NOT NULL CHECK (`days_count` >= 0),
  `reason` longtext DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  `leave_type_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `leaves_leave_employee_id_4949fe61_fk_employees_employee_id` (`employee_id`),
  KEY `leaves_leave_leave_type_id_6f5b6968_fk_leaves_leavetype_id` (`leave_type_id`),
  CONSTRAINT `leaves_leave_employee_id_4949fe61_fk_employees_employee_id` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`),
  CONSTRAINT `leaves_leave_leave_type_id_6f5b6968_fk_leaves_leavetype_id` FOREIGN KEY (`leave_type_id`) REFERENCES `leaves_leavetype` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `leaves_leave`
--

LOCK TABLES `leaves_leave` WRITE;
/*!40000 ALTER TABLE `leaves_leave` DISABLE KEYS */;
/*!40000 ALTER TABLE `leaves_leave` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `leaves_leavebalance`
--

DROP TABLE IF EXISTS `leaves_leavebalance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `leaves_leavebalance` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `year` int(10) unsigned NOT NULL CHECK (`year` >= 0),
  `initial_balance` int(10) unsigned NOT NULL CHECK (`initial_balance` >= 0),
  `used_balance` int(10) unsigned NOT NULL CHECK (`used_balance` >= 0),
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  `leave_type_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `leaves_leavebalance_employee_id_leave_type_id_year_b4300508_uniq` (`employee_id`,`leave_type_id`,`year`),
  KEY `leaves_leavebalance_leave_type_id_23644b98_fk_leaves_le` (`leave_type_id`),
  CONSTRAINT `leaves_leavebalance_employee_id_22e425a2_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`),
  CONSTRAINT `leaves_leavebalance_leave_type_id_23644b98_fk_leaves_le` FOREIGN KEY (`leave_type_id`) REFERENCES `leaves_leavetype` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `leaves_leavebalance`
--

LOCK TABLES `leaves_leavebalance` WRITE;
/*!40000 ALTER TABLE `leaves_leavebalance` DISABLE KEYS */;
/*!40000 ALTER TABLE `leaves_leavebalance` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `leaves_leavetype`
--

DROP TABLE IF EXISTS `leaves_leavetype`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `leaves_leavetype` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` longtext DEFAULT NULL,
  `max_days_per_year` int(10) unsigned NOT NULL CHECK (`max_days_per_year` >= 0),
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `leaves_leavetype`
--

LOCK TABLES `leaves_leavetype` WRITE;
/*!40000 ALTER TABLE `leaves_leavetype` DISABLE KEYS */;
INSERT INTO `leaves_leavetype` VALUES (1,'casual','إجازة عرضية للموظفين',7,'2025-07-28 06:07:19.386977','2025-07-28 06:07:19.387045');
/*!40000 ALTER TABLE `leaves_leavetype` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications_notification`
--

DROP TABLE IF EXISTS `notifications_notification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications_notification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `message` longtext NOT NULL,
  `notification_type` varchar(20) NOT NULL,
  `icon` varchar(50) NOT NULL,
  `is_read` tinyint(1) NOT NULL,
  `is_global` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `read_at` datetime(6) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_notification_user_id_b5e8c0ff_fk_accounts_user_id` (`user_id`),
  CONSTRAINT `notifications_notification_user_id_b5e8c0ff_fk_accounts_user_id` FOREIGN KEY (`user_id`) REFERENCES `accounts_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications_notification`
--

LOCK TABLES `notifications_notification` WRITE;
/*!40000 ALTER TABLE `notifications_notification` DISABLE KEYS */;
INSERT INTO `notifications_notification` VALUES (1,'مستخدم جديد','تم إنشاء حساب مستخدم جديد: admin','success','fa-user-plus',0,0,'2025-07-28 06:09:32.395673',NULL,1);
/*!40000 ALTER TABLE `notifications_notification` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications_notificationreadstatus`
--

DROP TABLE IF EXISTS `notifications_notificationreadstatus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications_notificationreadstatus` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `is_read` tinyint(1) NOT NULL,
  `read_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `notification_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `notifications_notificati_notification_id_user_id_c394b5ec_uniq` (`notification_id`,`user_id`),
  KEY `notifications_notifi_user_id_15d127ff_fk_accounts_` (`user_id`),
  CONSTRAINT `notifications_notifi_notification_id_affd4582_fk_notificat` FOREIGN KEY (`notification_id`) REFERENCES `notifications_notification` (`id`),
  CONSTRAINT `notifications_notifi_user_id_15d127ff_fk_accounts_` FOREIGN KEY (`user_id`) REFERENCES `accounts_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications_notificationreadstatus`
--

LOCK TABLES `notifications_notificationreadstatus` WRITE;
/*!40000 ALTER TABLE `notifications_notificationreadstatus` DISABLE KEYS */;
/*!40000 ALTER TABLE `notifications_notificationreadstatus` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `performance_performanceevaluation`
--

DROP TABLE IF EXISTS `performance_performanceevaluation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `performance_performanceevaluation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `year` int(10) unsigned NOT NULL CHECK (`year` >= 0),
  `score` decimal(5,2) NOT NULL,
  `max_score` decimal(5,2) NOT NULL,
  `evaluator` varchar(255) DEFAULT NULL,
  `comments` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `performance_performanceevaluation_employee_id_year_e9c2758d_uniq` (`employee_id`,`year`),
  CONSTRAINT `performance_performa_employee_id_a9240027_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `performance_performanceevaluation`
--

LOCK TABLES `performance_performanceevaluation` WRITE;
/*!40000 ALTER TABLE `performance_performanceevaluation` DISABLE KEYS */;
/*!40000 ALTER TABLE `performance_performanceevaluation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ranks_course`
--

DROP TABLE IF EXISTS `ranks_course`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ranks_course` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `hours` int(10) unsigned NOT NULL CHECK (`hours` >= 0),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ranks_course`
--

LOCK TABLES `ranks_course` WRITE;
/*!40000 ALTER TABLE `ranks_course` DISABLE KEYS */;
INSERT INTO `ranks_course` VALUES (1,'إدارة المشاريع','دورة في إدارة المشاريع وتخطيط الأعمال','2025-07-23 15:13:26.337220','2025-07-23 13:13:47.221534',120),(5,'الحاسوب الأساسي','دورة في أساسيات استخدام الحاسوب','2025-07-23 15:15:45.000000','2025-07-23 13:13:58.995754',80),(6,'اللغة الإنجليزية','دورة في تعلم اللغة الإنجليزية','2025-07-23 15:15:45.000000','2025-07-23 15:15:45.000000',0),(9,'انتل جديد','ئءئاءئلا','2025-07-23 13:02:24.289059','2025-07-23 13:02:24.289108',0);
/*!40000 ALTER TABLE `ranks_course` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ranks_employeecourse`
--

DROP TABLE IF EXISTS `ranks_employeecourse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ranks_employeecourse` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `hours` int(10) unsigned NOT NULL CHECK (`hours` >= 0),
  `completion_date` date NOT NULL,
  `certificate_number` varchar(100) DEFAULT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `course_id` bigint(20) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ranks_employeecourse_employee_id_course_id_co_b2a105c0_uniq` (`employee_id`,`course_id`,`completion_date`),
  KEY `ranks_employeecourse_course_id_abb36db8_fk_ranks_course_id` (`course_id`),
  CONSTRAINT `ranks_employeecourse_course_id_abb36db8_fk_ranks_course_id` FOREIGN KEY (`course_id`) REFERENCES `ranks_course` (`id`),
  CONSTRAINT `ranks_employeecourse_employee_id_6b383a6d_fk_employees` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ranks_employeecourse`
--

LOCK TABLES `ranks_employeecourse` WRITE;
/*!40000 ALTER TABLE `ranks_employeecourse` DISABLE KEYS */;
INSERT INTO `ranks_employeecourse` VALUES (2,50,'2025-07-01','','','2025-07-23 12:25:51.737584','2025-07-23 12:25:51.737599',5,1);
/*!40000 ALTER TABLE `ranks_employeecourse` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ranks_employeerank`
--

DROP TABLE IF EXISTS `ranks_employeerank`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ranks_employeerank` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `date_obtained` date NOT NULL,
  `notes` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `employee_id` bigint(20) NOT NULL,
  `rank_type_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ranks_employeerank_employee_id_c0e3334b_fk_employees_employee_id` (`employee_id`),
  KEY `ranks_employeerank_rank_type_id_7c614e40_fk_ranks_ranktype_id` (`rank_type_id`),
  CONSTRAINT `ranks_employeerank_employee_id_c0e3334b_fk_employees_employee_id` FOREIGN KEY (`employee_id`) REFERENCES `employees_employee` (`id`),
  CONSTRAINT `ranks_employeerank_rank_type_id_7c614e40_fk_ranks_ranktype_id` FOREIGN KEY (`rank_type_id`) REFERENCES `ranks_ranktype` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ranks_employeerank`
--

LOCK TABLES `ranks_employeerank` WRITE;
/*!40000 ALTER TABLE `ranks_employeerank` DISABLE KEYS */;
/*!40000 ALTER TABLE `ranks_employeerank` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ranks_ranktype`
--

DROP TABLE IF EXISTS `ranks_ranktype`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ranks_ranktype` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` longtext DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ranks_ranktype`
--

LOCK TABLES `ranks_ranktype` WRITE;
/*!40000 ALTER TABLE `ranks_ranktype` DISABLE KEYS */;
/*!40000 ALTER TABLE `ranks_ranktype` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reports_report`
--

DROP TABLE IF EXISTS `reports_report`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reports_report` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `report_type` varchar(20) NOT NULL,
  `parameters` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`parameters`)),
  `file` varchar(100) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reports_report`
--

LOCK TABLES `reports_report` WRITE;
/*!40000 ALTER TABLE `reports_report` DISABLE KEYS */;
/*!40000 ALTER TABLE `reports_report` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_logs_systemerror`
--

DROP TABLE IF EXISTS `system_logs_systemerror`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_logs_systemerror` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `timestamp` datetime(6) NOT NULL,
  `error_type` varchar(50) NOT NULL,
  `error_message` longtext NOT NULL,
  `error_description` longtext NOT NULL,
  `page_url` varchar(500) NOT NULL,
  `page_name` varchar(255) NOT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `line_number` int(11) DEFAULT NULL,
  `function_name` varchar(255) DEFAULT NULL,
  `ip_address` char(39) DEFAULT NULL,
  `user_agent` longtext DEFAULT NULL,
  `stack_trace` longtext DEFAULT NULL,
  `request_method` varchar(10) DEFAULT NULL,
  `request_data` longtext DEFAULT NULL,
  `module` varchar(50) DEFAULT NULL,
  `severity` varchar(20) NOT NULL,
  `status` varchar(20) NOT NULL,
  `resolution_notes` longtext DEFAULT NULL,
  `resolved_at` datetime(6) DEFAULT NULL,
  `occurrence_count` int(11) NOT NULL,
  `last_occurrence` datetime(6) NOT NULL,
  `resolved_by_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `system_logs_systemer_resolved_by_id_3331d5d9_fk_accounts_` (`resolved_by_id`),
  KEY `system_logs_systemerror_user_id_6ce8b6d7_fk_accounts_user_id` (`user_id`),
  KEY `system_logs_timesta_7754f5_idx` (`timestamp`),
  KEY `system_logs_error_t_68af51_idx` (`error_type`),
  KEY `system_logs_severit_95c37a_idx` (`severity`),
  KEY `system_logs_status_94ede9_idx` (`status`),
  KEY `system_logs_module_75775d_idx` (`module`),
  CONSTRAINT `system_logs_systemer_resolved_by_id_3331d5d9_fk_accounts_` FOREIGN KEY (`resolved_by_id`) REFERENCES `accounts_user` (`id`),
  CONSTRAINT `system_logs_systemerror_user_id_6ce8b6d7_fk_accounts_user_id` FOREIGN KEY (`user_id`) REFERENCES `accounts_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_logs_systemerror`
--

LOCK TABLES `system_logs_systemerror` WRITE;
/*!40000 ALTER TABLE `system_logs_systemerror` DISABLE KEYS */;
INSERT INTO `system_logs_systemerror` VALUES (1,'2025-07-28 06:18:12.639857','database_error','(1054, \"Unknown column \'home_internaltransfer.new_department_id\' in \'field list\'\")','خطأ في قاعدة البيانات - يحدث عند فشل عملية قاعدة البيانات','/internal-transfer-list/','internal-transfer-list','C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\core\\handlers\\base.py',197,'_get_response','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0','Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\backends\\utils.py\", line 89, in _execute\n    return self.cursor.execute(sql, params)\n           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\backends\\mysql\\base.py\", line 75, in execute\n    return self.cursor.execute(query, args)\n           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MySQLdb\\cursors.py\", line 179, in execute\n    res = self._query(mogrified_query)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MySQLdb\\cursors.py\", line 330, in _query\n    db.query(q)\n    ~~~~~~~~^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MySQLdb\\connections.py\", line 280, in query\n    _mysql.connection.query(self, query)\n    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^\nMySQLdb.OperationalError: (1054, \"Unknown column \'home_internaltransfer.new_department_id\' in \'field list\'\")\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\core\\handlers\\base.py\", line 197, in _get_response\n    response = wrapped_callback(request, *callback_args, **callback_kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\contrib\\auth\\decorators.py\", line 23, in _wrapper_view\n    return view_func(request, *args, **kwargs)\n  File \"C:\\Users\\<USER>\\OneDrive\\Desktop\\HR-SYSTEM- VSCode\\home\\views.py\", line 641, in internal_transfer_list_view\n    for transfer in transfers:\n                    ^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\models\\query.py\", line 398, in __iter__\n    self._fetch_all()\n    ~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\models\\query.py\", line 1881, in _fetch_all\n    self._result_cache = list(self._iterable_class(self))\n                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\models\\query.py\", line 91, in __iter__\n    results = compiler.execute_sql(\n        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size\n    )\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\models\\sql\\compiler.py\", line 1562, in execute_sql\n    cursor.execute(sql, params)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\backends\\utils.py\", line 102, in execute\n    return super().execute(sql, params)\n           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\backends\\utils.py\", line 67, in execute\n    return self._execute_with_wrappers(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        sql, params, many=False, executor=self._execute\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\backends\\utils.py\", line 80, in _execute_with_wrappers\n    return executor(sql, params, many, context)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\backends\\utils.py\", line 84, in _execute\n    with self.db.wrap_database_errors:\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\utils.py\", line 91, in __exit__\n    raise dj_exc_value.with_traceback(traceback) from exc_value\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\backends\\utils.py\", line 89, in _execute\n    return self.cursor.execute(sql, params)\n           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\django\\db\\backends\\mysql\\base.py\", line 75, in execute\n    return self.cursor.execute(query, args)\n           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MySQLdb\\cursors.py\", line 179, in execute\n    res = self._query(mogrified_query)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MySQLdb\\cursors.py\", line 330, in _query\n    db.query(q)\n    ~~~~~~~~^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\MySQLdb\\connections.py\", line 280, in query\n    _mysql.connection.query(self, query)\n    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^\ndjango.db.utils.OperationalError: (1054, \"Unknown column \'home_internaltransfer.new_department_id\' in \'field list\'\")\n','GET',NULL,'internal-transfer-list','critical','new',NULL,NULL,3,'2025-07-28 06:20:02.210820',NULL,1);
/*!40000 ALTER TABLE `system_logs_systemerror` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_logs_systemlog`
--

DROP TABLE IF EXISTS `system_logs_systemlog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_logs_systemlog` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `timestamp` datetime(6) NOT NULL,
  `ip_address` char(39) DEFAULT NULL,
  `module` varchar(20) NOT NULL,
  `action` varchar(20) NOT NULL,
  `page` varchar(255) NOT NULL,
  `description` longtext DEFAULT NULL,
  `object_id` varchar(50) DEFAULT NULL,
  `object_repr` varchar(255) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `operating_system` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `system_logs_systemlog_user_id_7954c042_fk_accounts_user_id` (`user_id`),
  CONSTRAINT `system_logs_systemlog_user_id_7954c042_fk_accounts_user_id` FOREIGN KEY (`user_id`) REFERENCES `accounts_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_logs_systemlog`
--

LOCK TABLES `system_logs_systemlog` WRITE;
/*!40000 ALTER TABLE `system_logs_systemlog` DISABLE KEYS */;
INSERT INTO `system_logs_systemlog` VALUES (1,'2025-07-28 06:16:45.872572','127.0.0.1','employees','view','/employees/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(2,'2025-07-28 06:16:58.288918','127.0.0.1','system','view','/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/</strong>',NULL,NULL,1,'Windows 10/11'),(3,'2025-07-28 06:16:58.495380','127.0.0.1','system','view','/announcements/api/homepage/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/announcements/api/homepage/</strong>',NULL,NULL,1,'Windows 10/11'),(4,'2025-07-28 06:16:59.927516','127.0.0.1','system','view','/dashboard/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/dashboard/</strong>',NULL,NULL,1,'Windows 10/11'),(5,'2025-07-28 06:17:04.098773','127.0.0.1','employees','view','/employees/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(6,'2025-07-28 06:17:14.185177','127.0.0.1','employees','view','/employees/ajax/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(7,'2025-07-28 06:17:14.275695','127.0.0.1','employees','view','/employees/ajax/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(8,'2025-07-28 06:17:17.318548','127.0.0.1','employees','view','/employees/ajax/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(9,'2025-07-28 06:17:17.516809','127.0.0.1','employees','view','/employees/ajax/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(10,'2025-07-28 06:17:17.750959','127.0.0.1','employees','view','/employees/ajax/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(11,'2025-07-28 06:17:19.006352','127.0.0.1','employment','view','/employment/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong> - صفحة: <strong>/employment/</strong>',NULL,NULL,1,'Windows 10/11'),(12,'2025-07-28 06:17:22.024431','127.0.0.1','employment','view','/employment/employee-identifications/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong>',NULL,NULL,1,'Windows 10/11'),(13,'2025-07-28 06:17:28.303069','127.0.0.1','employment','view','/employment/departments/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong>',NULL,NULL,1,'Windows 10/11'),(14,'2025-07-28 06:17:33.323001','127.0.0.1','employment','view','/employment/departments/import/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong>',NULL,NULL,1,'Windows 10/11'),(15,'2025-07-28 06:17:39.328492','127.0.0.1','employment','view','/employment/positions/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong> - صفحة: <strong>/employment/positions/</strong>',NULL,NULL,1,'Windows 10/11'),(16,'2025-07-28 06:17:41.366783','127.0.0.1','employment','view','/employment/employee-positions/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong>',NULL,NULL,1,'Windows 10/11'),(17,'2025-07-28 06:17:43.778163','127.0.0.1','employment','view','/employment/experience-certificates/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong> - صفحة: <strong>/employment/experience-certificates/</strong>',NULL,NULL,1,'Windows 10/11'),(18,'2025-07-28 06:17:47.014937','127.0.0.1','employment','view','/employment/experience-certificates/1/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong> - صفحة: <strong>/employment/experience-certificates/1/</strong>',NULL,NULL,1,'Windows 10/11'),(19,'2025-07-28 06:17:56.856013','127.0.0.1','employment','view','/employment/experience-certificates/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong> - صفحة: <strong>/employment/experience-certificates/</strong>',NULL,NULL,1,'Windows 10/11'),(20,'2025-07-28 06:17:58.482953','127.0.0.1','employment','view','/employment/technical-positions/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong> - صفحة: <strong>/employment/technical-positions/</strong>',NULL,NULL,1,'Windows 10/11'),(21,'2025-07-28 06:17:59.753990','127.0.0.1','employment','view','/employment/actual-service/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong> - صفحة: <strong>/employment/actual-service/</strong>',NULL,NULL,1,'Windows 10/11'),(22,'2025-07-28 06:18:07.762617','127.0.0.1','employees','view','/employees/calculate-age/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(23,'2025-07-28 06:18:12.627701','127.0.0.1','system','view','/internal-transfer-list/','قام المستخدم <strong>admin</strong> بعرض قائمة النظام',NULL,NULL,1,'Windows 10/11'),(24,'2025-07-28 06:18:19.388356','127.0.0.1','employment','view','/employment/excess-employees/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong>',NULL,NULL,1,'Windows 10/11'),(25,'2025-07-28 06:18:21.543247','127.0.0.1','employment','view','/employment/medical-conditions/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong> - صفحة: <strong>/employment/medical-conditions/</strong>',NULL,NULL,1,'Windows 10/11'),(26,'2025-07-28 06:18:22.728580','127.0.0.1','employment','view','/employment/btec/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong> - صفحة: <strong>/employment/btec/</strong>',NULL,NULL,1,'Windows 10/11'),(27,'2025-07-28 06:18:24.274635','127.0.0.1','system','view','/internal-transfer-list/','قام المستخدم <strong>admin</strong> بعرض قائمة النظام',NULL,NULL,1,'Windows 10/11'),(28,'2025-07-28 06:18:42.082671','127.0.0.1','accounts','view','/accounts/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>المستخدمين</strong> - صفحة: <strong>/accounts/</strong>',NULL,NULL,1,'Windows 10/11'),(29,'2025-07-28 06:18:46.761698','127.0.0.1','accounts','view','/accounts/1/edit/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>المستخدمين</strong> - صفحة: <strong>/accounts/1/edit/</strong>',NULL,NULL,1,'Windows 10/11'),(30,'2025-07-28 06:18:50.283952','127.0.0.1','accounts','view','/accounts/user/1/permissions/page/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>المستخدمين</strong> - صفحة: <strong>/accounts/user/1/permissions/page/</strong>',NULL,NULL,1,'Windows 10/11'),(31,'2025-07-28 06:19:14.525758','127.0.0.1','accounts','other','/accounts/user/1/permissions/save/','قام المستخدم <strong>admin</strong> بتنفيذ إجراء <strong>أخرى</strong> في قسم <strong>المستخدمين</strong> - صفحة: <strong>/accounts/user/1/permissions/save/</strong>',NULL,NULL,1,'Windows 10/11'),(32,'2025-07-28 06:19:17.929751','127.0.0.1','accounts','view','/accounts/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>المستخدمين</strong> - صفحة: <strong>/accounts/</strong>',NULL,NULL,1,'Windows 10/11'),(33,'2025-07-28 06:19:23.710449','127.0.0.1','files','view','/files/movements/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الملفات</strong> - صفحة: <strong>/files/movements/</strong>',NULL,NULL,1,'Windows 10/11'),(34,'2025-07-28 06:19:25.360766','127.0.0.1','employees','view','/employees/maternity-leaves/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(35,'2025-07-28 06:19:30.209233','127.0.0.1','leaves','view','/leaves/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الإجازات</strong>',NULL,NULL,1,'Windows 10/11'),(36,'2025-07-28 06:19:31.689379','127.0.0.1','leaves','view','/leaves/add/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الإجازات</strong>',NULL,NULL,1,'Windows 10/11'),(37,'2025-07-28 06:19:33.162955','127.0.0.1','leaves','view','/leaves/balance/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الإجازات</strong>',NULL,NULL,1,'Windows 10/11'),(38,'2025-07-28 06:19:34.344800','127.0.0.1','leaves','view','/leaves/departures/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الإجازات</strong>',NULL,NULL,1,'Windows 10/11'),(39,'2025-07-28 06:19:36.795168','127.0.0.1','leaves','view','/leaves/reports/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الإجازات</strong>',NULL,NULL,1,'Windows 10/11'),(40,'2025-07-28 06:19:44.034530','127.0.0.1','ranks','view','/ranks/employee-courses/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الرتب</strong>',NULL,NULL,1,'Windows 10/11'),(41,'2025-07-28 06:19:46.896600','127.0.0.1','ranks','view','/ranks/allowances/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الرتب</strong> - صفحة: <strong>/ranks/allowances/</strong>',NULL,NULL,1,'Windows 10/11'),(42,'2025-07-28 06:19:50.808278','127.0.0.1','ranks','view','/ranks/employee-courses/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الرتب</strong>',NULL,NULL,1,'Windows 10/11'),(43,'2025-07-28 06:19:51.961858','127.0.0.1','ranks','view','/ranks/courses/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الرتب</strong> - صفحة: <strong>/ranks/courses/</strong>',NULL,NULL,1,'Windows 10/11'),(44,'2025-07-28 06:20:00.059036','127.0.0.1','employment','view','/employment/departments/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong>',NULL,NULL,1,'Windows 10/11'),(45,'2025-07-28 06:20:02.203428','127.0.0.1','system','view','/internal-transfer-list/','قام المستخدم <strong>admin</strong> بعرض قائمة النظام',NULL,NULL,1,'Windows 10/11'),(46,'2025-07-28 06:22:25.155047','127.0.0.1','system','view','/internal-transfer-list/','قام المستخدم <strong>admin</strong> بعرض قائمة النظام',NULL,NULL,1,'Windows 10/11'),(47,'2025-07-28 06:22:29.788235','127.0.0.1','system','view','/internal-transfer-print-letters/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/internal-transfer-print-letters/</strong>',NULL,NULL,1,'Windows 10/11'),(48,'2025-07-28 06:22:33.307164','127.0.0.1','system','view','/internal-transfer-print-summary/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/internal-transfer-print-summary/</strong>',NULL,NULL,1,'Windows 10/11'),(49,'2025-07-28 06:22:34.731716','127.0.0.1','system','view','/internal-transfer-print-letters/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/internal-transfer-print-letters/</strong>',NULL,NULL,1,'Windows 10/11'),(50,'2025-07-28 06:22:35.805620','127.0.0.1','system','view','/employee-transfer-management/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong>',NULL,NULL,1,'Windows 10/11'),(51,'2025-07-28 06:22:37.676682','127.0.0.1','system','view','/add-employee-transfer/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong>',NULL,NULL,1,'Windows 10/11'),(52,'2025-07-28 06:22:43.183571','127.0.0.1','system','other','/search-employee-for-transfer/','قام المستخدم <strong>admin</strong> بتنفيذ إجراء <strong>أخرى</strong> في قسم <strong>النظام</strong>',NULL,NULL,1,'Windows 10/11'),(53,'2025-07-28 06:22:47.882857','127.0.0.1','system','view','/employee-transfer-management/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong>',NULL,NULL,1,'Windows 10/11'),(54,'2025-07-28 06:22:49.159299','127.0.0.1','system','view','/internal-transfer-print-letters/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/internal-transfer-print-letters/</strong>',NULL,NULL,1,'Windows 10/11'),(55,'2025-07-28 06:22:51.975453','127.0.0.1','system','view','/internal-transfer-list/','قام المستخدم <strong>admin</strong> بعرض قائمة النظام',NULL,NULL,1,'Windows 10/11'),(56,'2025-07-28 06:22:59.701196','127.0.0.1','reports','view','/reports/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>تقارير النظام</strong> - صفحة: <strong>/reports/</strong>',NULL,NULL,1,'Windows 10/11'),(57,'2025-07-28 06:23:01.279958','127.0.0.1','system','view','/announcements/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/announcements/</strong>',NULL,NULL,1,'Windows 10/11'),(58,'2025-07-28 06:23:03.164382','127.0.0.1','system','view','/important-links-admin/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/important-links-admin/</strong>',NULL,NULL,1,'Windows 10/11'),(59,'2025-07-28 06:23:05.716113','127.0.0.1','system','view','/approved-forms/admin/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/approved-forms/admin/</strong>',NULL,NULL,1,'Windows 10/11'),(60,'2025-07-28 06:23:09.892657','127.0.0.1','performance','view','/performance/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>التقارير السنوية</strong> - صفحة: <strong>/performance/</strong>',NULL,NULL,1,'Windows 10/11'),(61,'2025-07-28 06:23:37.914869','127.0.0.1','accounts','view','/accounts/change-password/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>المستخدمين</strong> - صفحة: <strong>/accounts/change-password/</strong>',NULL,NULL,1,'Windows 10/11'),(62,'2025-07-28 06:23:41.514537','127.0.0.1','system','view','/analytics-dashboard/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/analytics-dashboard/</strong>',NULL,NULL,1,'Windows 10/11'),(63,'2025-07-28 06:23:45.875465','127.0.0.1','system','view','/dashboard/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>النظام</strong> - صفحة: <strong>/dashboard/</strong>',NULL,NULL,1,'Windows 10/11'),(64,'2025-07-28 06:23:49.995736','127.0.0.1','employment','view','/employment/departments/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong>',NULL,NULL,1,'Windows 10/11'),(65,'2025-07-28 06:23:55.666477','127.0.0.1','employment','view','/employment/departments/import/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong>',NULL,NULL,1,'Windows 10/11'),(66,'2025-07-28 06:26:40.748922','127.0.0.1','system','view','/internal-transfer-list/','قام المستخدم <strong>admin</strong> بعرض قائمة النظام',NULL,NULL,1,'Windows 10/11'),(67,'2025-07-28 06:27:05.872180','127.0.0.1','employees','view','/employees/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(68,'2025-07-28 06:27:11.799904','127.0.0.1','employment','view','/employment/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong> - صفحة: <strong>/employment/</strong>',NULL,NULL,1,'Windows 10/11'),(69,'2025-07-28 06:27:17.044963','127.0.0.1','employment','view','/employment/departments/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong>',NULL,NULL,1,'Windows 10/11'),(70,'2025-07-28 06:27:19.990198','127.0.0.1','employees','view','/employees/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(71,'2025-07-28 06:27:22.276486','127.0.0.1','employees','view','/employees/internal-transfers/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(72,'2025-07-28 06:27:24.747052','127.0.0.1','employees','view','/employees/external-transfers/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(73,'2025-07-28 06:27:28.631181','127.0.0.1','employees','view','/employees/import-export/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(74,'2025-07-28 06:27:35.502450','127.0.0.1','employees','view','/employees/add-qualification/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الموظفين</strong>',NULL,NULL,1,'Windows 10/11'),(75,'2025-07-28 06:27:38.744368','127.0.0.1','employment','view','/employment/service-purchase/','قام المستخدم <strong>admin</strong> بعرض صفحة في قسم <strong>الكادر</strong> - صفحة: <strong>/employment/service-purchase/</strong>',NULL,NULL,1,'Windows 10/11');
/*!40000 ALTER TABLE `system_logs_systemlog` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-28  9:27:47
