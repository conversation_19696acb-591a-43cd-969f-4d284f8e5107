{% extends 'base.html' %}
{% load static %}

{% block title %}تعديل الدورة - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تعديل الدورة</h2>
    <a href="{% url 'ranks:course_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة لأسماء الدورات
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">تعديل بيانات الدورة</h6>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">
                            <i class="fas fa-graduation-cap me-1"></i>اسم الدورة
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="{{ form.hours.id_for_label }}" class="form-label">
                            <i class="fas fa-clock me-1"></i>عدد الساعات
                        </label>
                        {{ form.hours }}
                        {% if form.hours.errors %}
                            <div class="text-danger">
                                {% for error in form.hours.errors %}
                                    <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-calendar me-1"></i>آخر تحديث
                        </label>
                        <input type="text" class="form-control" value="{{ course.updated_at|date:'d/m/Y H:i' }}" readonly>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">
                            <i class="fas fa-align-left me-1"></i>وصف الدورة
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small><i class="fas fa-exclamation-triangle me-1"></i>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ التعديلات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add Bootstrap classes to form fields
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(element) {
        element.classList.add('form-control');
    });
});
</script>
{% endblock %}
