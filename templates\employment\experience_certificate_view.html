{% extends 'base.html' %}
{% load static %}

{% block body_class %}{% endblock %}

{% block title %}شهادة الخبرة - {{ employee.full_name }}{% endblock %}

{% block footer %}
<!-- تم إزالة تذييل الصفحة -->
{% endblock %}

{% block extra_css %}
<style>
    body {
        font-family: 'Traditional Arabic', Arial, sans-serif;
        padding: 20px;
    }

    /* Estilos específicos para los encabezados de tabla en esta página */
    .certificate-table th {
        background-color: #ffffff !important;
        color: #000000 !important;
    }

    @page {
        margin: 0.5cm 0.5cm 0.5cm 0.5cm; /* Top, Right, Bottom, Left */
        size: A4 portrait;
    }

    .button-container {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin: 20px 0;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
    }

    .btn-primary {
        background-color: #007bff;
        color: white;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }
    @media print {
        .no-print {
            display: none !important;
        }
        .print-only {
            display: block !important;
        }
        body {
            font-family: 'Traditional Arabic', Arial, sans-serif;
            font-size: 12pt;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        /* Hide header and footer */
        header, .navbar, .topbar, #wrapper #content-wrapper #content .navbar-nav,
        footer, .sticky-footer, .site-footer, #footer {
            display: none !important;
        }
        /* Hide any other header or footer elements */
        body::before, body::after {
            display: none !important;
        }
        .certificate-container {
            width: 100%;
            max-height: 29.7cm; /* A4 height */
            margin: 0 auto;
            padding: 0;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }
        .certificate {
            border: none;
            box-shadow: none;
            padding: 0;
            margin: 0;
            page-break-inside: avoid;
            page-break-after: auto;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }
        .certificate-header {
            margin-bottom: 0;
            padding: 0 0.3cm;
        }
        .certificate-content {
            flex: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 0 0.1cm;
            margin-top: -0.2cm;
        }
        .certificate-footer {
            margin-top: 0.2cm;
            padding: 0 0.3cm;
        }
        .certificate h1 {
            font-size: 14pt;
            margin-bottom: 0.1cm;
            text-align: center;
        }
        .certificate h2 {
            font-size: 12pt;
            margin-bottom: 0.1cm;
            text-align: center;
        }
        .certificate p {
            font-size: 10pt;
            margin-bottom: 0.05cm;
            line-height: 1.1;
            text-align: right;
        }
        .certificate-table {
            font-size: 12pt;
            margin: 0;
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        .certificate-table th, .certificate-table td {
            padding: 0.1cm;
            line-height: 1.2;
            border: 1px solid #000;
            text-align: center;
            vertical-align: middle;
            font-size: 12pt;
        }
        .certificate-table th {
            font-size: 12pt;
            font-weight: bold;
            background-color: #ffffff !important;
            color: #000000 !important;
        }
        @page {
            size: A4 portrait;
            margin: 0.5cm 0.5cm 0.5cm 0.5cm; /* Top, Right, Bottom, Left */
            orphans: 0;
            widows: 0;
        }
    }

    .certificate-container {
        width: 100%;
        margin: 0 auto;
        padding: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .certificate {
        background-color: white;
        padding: 20px;
        border: 1px solid #ddd;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        text-align: center;
        direction: rtl;
        font-family: 'Traditional Arabic', Arial, sans-serif;
        line-height: 1.5;
        display: flex;
        flex-direction: column;
        max-height: 29.7cm; /* A4 height */
        width: 21cm; /* A4 width */
        margin: 0 auto;
        box-sizing: border-box;
        overflow: hidden;
    }

    .certificate h1 {
        font-size: 22px;
        margin-bottom: 10px;
    }

    .certificate h2 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .certificate p {
        font-size: 16px;
        text-align: right;
        margin-bottom: 8px;
    }

    .certificate-table {
        width: 100%;
        border-collapse: collapse;
        margin: 5px 0;
        font-size: 12px;
        table-layout: fixed;
        flex: 1;
        max-height: 400px;
    }

    .certificate-content {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .certificate-table th, .certificate-table td {
        border: 0.5px solid #000;
        padding: 1px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 0.9;
        font-size: 11px;
    }

    .certificate-table th {
        background-color: #ffffff;
        color: #000000;
    }

    .certificate-header {
        margin-bottom: 15px;
        padding: 0 20px;
    }

    .certificate-footer {
        margin-top: 15px;
        padding: 0 20px;
    }

    .certificate-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0 10px;
        min-height: 400px;
    }

    .print-only {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="certificate-header">
        <br><br>
        <h2 style="text-align: center">وزارة التربية والتعليم</h2>
        <div style="text-align: center; margin: 10px auto;">
            <img src="{% static 'img/logos/moe-logo.jpg' %}" alt="شعار وزارة التربية والتعليم" style="width: 80px; height: 80px; margin: 0 auto; display: block;">
        </div>

        <h3 style="text-align: center">مديرية التربية والتعليم / لواء قصبة المفرق</h3><br>
        <h4 style="text-align: center; font-weight: bold; margin-bottom: 15px;">لمن يهمه الأمر</h4><br>
    </div>

    <div class="certificate-body">

            <p style="text-align: right; font-size: 14pt; font-weight: bold; margin-bottom: 10px;">السلام عليكم ورحمة الله وبركاته:</p>

            <p style="text-align: right; font-size: 14pt; margin-bottom: 5px;">تشير سجلات هذه المديرية بأن السيد / {{ employee.full_name }}</p>
            <p style="text-align: right; font-size: 14pt; margin-bottom: 5px;">الرقم الوطني: {{ employee.national_id }}</p>
            <p style="text-align: right; font-size: 14pt; margin-bottom: 5px;">المؤهل العلمي والتخصص:
                {{ employee.qualification }} / {{ employee.specialization }}
                {% if employee.post_graduate_diploma %} + {{ employee.post_graduate_diploma }}{% endif %}
                {% if employee.masters_degree %} + {{ employee.masters_degree }}{% endif %}
                {% if employee.phd_degree %} + {{ employee.phd_degree }}{% endif %}
            </p>
            <p style="text-align: right; font-size: 14pt; margin-bottom: 5px;">يعمل في ملاك وزارة التربية والتعليم اعتبارا من تاريخ {{ employee.hire_date|date:"Y-m-d" }}</p>

            <p style="text-align: right; font-size: 14pt; margin-bottom: 0; padding-bottom: 0;">ولا يزال على رأس عمله حتى تاريخه وعلى النحو التالي:</p>
       <br>
            <table class="certificate-table" style="max-width:95%; width: 95%; border: 1px solid #000; margin-top: 0; margin-bottom: 0;">
            <thead style="background-color: #ffffff !important; color: #000000 !important;">
                <tr>
                    <th rowspan="3" style="width: 5%; border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">الرقم</th>
                    <th rowspan="3" style="width: 20%; border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">الوظيفة</th>
                    <th rowspan="3" style="width: 15%; border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">المبحث الذي يدرسه</th>
                    <th rowspan="3" style="width: 15%; border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">المرحلة التي يدرسها</th>
                    <th colspan="6" style="width: 45%; border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">الفترة الزمنية</th>
                </tr>
                <tr>
                    <th colspan="3" style="border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">من</th>
                    <th colspan="3" style="border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">إلى</th>
                </tr>
                <tr>
                    <th style="border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">اليوم</th>
                    <th style="border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">الشهر</th>
                    <th style="border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">السنة</th>
                    <th style="border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">اليوم</th>
                    <th style="border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">الشهر</th>
                    <th style="border: 1px solid #000; font-size: 12pt; background-color: #ffffff !important; color: #000000 !important;">السنة</th>
                </tr>
            </thead>
            <tbody>
                {% for row in certificate_rows %}
                <tr>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ row.row_num }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ row.description }}</td>
                    {% if row.type == 'position' %}
                        <td style="border: 1px solid #000; font-size: 12pt;">{{ employee.specialization }}</td>
                    {% else %}
                        <td style="border: 1px solid #000; font-size: 12pt;">-</td>
                    {% endif %}
                    {% if row.is_teacher and row.school_level %}
                        {% if row.school_level == 'primary' %}
                            <td style="border: 1px solid #000; font-size: 12pt;">اساسي</td>
                        {% elif row.school_level == 'secondary' %}
                            <td style="border: 1px solid #000; font-size: 12pt;">ثانوي</td>
                        {% elif row.school_level == 'both' %}
                            <td style="border: 1px solid #000; font-size: 12pt;">اساسي + ثانوي</td>
                        {% else %}
                            <td style="border: 1px solid #000; font-size: 12pt;">-</td>
                        {% endif %}
                    {% else %}
                        <td style="border: 1px solid #000; font-size: 12pt;">-</td>
                    {% endif %}
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ row.start_date|date:"d" }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ row.start_date|date:"m" }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ row.start_date|date:"Y" }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ row.end_date|date:"d" }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ row.end_date|date:"m" }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ row.end_date|date:"Y" }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td style="border: 1px solid #000; font-size: 12pt;">1</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ employee.get_latest_position }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ employee.specialization }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">-</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ employee.hire_date|date:"d" }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ employee.hire_date|date:"m" }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ employee.hire_date|date:"Y" }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ today|date:"d" }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ today|date:"m" }}</td>
                    <td style="border: 1px solid #000; font-size: 12pt;">{{ today|date:"Y" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="certificate-footer" style="margin-top: 5px; margin-bottom: 0; padding-bottom: 0;">
            <p style="text-align: right; font-size: 14pt; margin-bottom: 5px;">قد أعطيت هذه الوثيقة بناء على طلبه  .</p><br>

                <p style="text-align: left; font-size: 18pt; font-weight: bold; margin-bottom: 0;">مدير التربية والتعليم</p>
                <br><br><br>
                <p style="text-align: right; font-size: 12pt; margin-bottom: 0;">نسخة / الملف الشخصي</p>
           <br><br><br><br>
            <div style="position: relative; width: 100%; margin-top: 5px; margin-bottom: 0;">
                <p style="position: absolute; right: 0; font-size: 10pt; color: #666; margin: 0;">Form # QF72-1-22 rev.c</p>
            </div>
        </div>
    </div>

    <div class="button-container no-print">
        <button class="btn btn-primary" id="print-button">
            <i class="fas fa-print"></i> طباعة
        </button>
        <a href="{% url 'employment:experience_certificate_export' employee.id %}" class="btn btn-success">
            <i class="fas fa-file-word"></i> تصدير Word
        </a>
        <a href="{% url 'employment:experience_certificate_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة
        </a>
    </div>

    <script>
        // Aplicar estilos a los encabezados de la tabla
        document.addEventListener('DOMContentLoaded', function() {
            const tableHeaders = document.querySelectorAll('.certificate-table th');
            tableHeaders.forEach(function(th) {
                th.style.backgroundColor = '#ffffff';
                th.style.color = '#000000';
            });
        });

        // Add print-specific styles
        const style = document.createElement('style');
        style.textContent = `
            @media print {
                .no-print {
                    display: none !important;
                }
                body {
                    padding: 0;
                }
                /* Hide header elements */
                header, .navbar, .topbar, #wrapper #content-wrapper #content .navbar-nav {
                    display: none !important;
                }
                /* Hide footer elements */
                footer, .sticky-footer, .site-footer, #footer {
                    display: none !important;
                }
                /* Hide any other header or footer elements */
                body::before, body::after {
                    display: none !important;
                }
                /* Set page margins */
                @page {
                    margin: 0.5cm 0.5cm 0.5cm 0.5cm; /* Top, Right, Bottom, Left */
                    size: A4 portrait;
                }
                /* Ensure content fits within margins */
                .container {
                    width: 100%;
                    max-width: 100%;
                    padding: 0;
                    margin: 0;
                }
                /* Adjust table width to fit within margins */
                .certificate-table {
                    width: 100%;
                    max-width: 100%;
                    margin: 0;
                }

                /* Ensure table headers are white with black text */
                .certificate-table th {
                    background-color: #ffffff !important;
                    color: #000000 !important;
                }
            }
        `;
        document.head.appendChild(style);

        // Add event listener to print button
        document.getElementById('print-button').addEventListener('click', function() {
            // Hide elements that should not be printed
            const elementsToHide = document.querySelectorAll('.no-print');
            elementsToHide.forEach(function(element) {
                element.style.display = 'none';
            });

            // Hide header elements
            const headerElements = document.querySelectorAll('header, .navbar, .topbar, #wrapper #content-wrapper #content .navbar-nav');
            headerElements.forEach(function(element) {
                if (element) {
                    element.style.display = 'none';
                }
            });

            // Hide footer elements
            const footerElements = document.querySelectorAll('footer, .sticky-footer, .site-footer, #footer');
            footerElements.forEach(function(element) {
                if (element) {
                    element.style.display = 'none';
                }
            });

            // Print the page directly without opening the print dialog
            window.print();

            // Show elements again after printing
            setTimeout(function() {
                elementsToHide.forEach(function(element) {
                    element.style.display = '';
                });

                // Show header elements again
                headerElements.forEach(function(element) {
                    if (element) {
                        element.style.display = '';
                    }
                });

                // Show footer elements again
                footerElements.forEach(function(element) {
                    if (element) {
                        element.style.display = '';
                    }
                });
            }, 1000);
        });
    </script>
</div>
{% endblock %}
