{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة نقل موظف جديد{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .btn-outline-primary:hover {
        transform: translateY(-1px);
        transition: all 0.2s ease-in-out;
    }
    
    .btn-outline-success:hover {
        background-color: #28a745;
        border-color: #28a745;
        color: #fff;
        transform: translateY(-1px);
        transition: all 0.2s ease-in-out;
    }
    
    .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        color: #fff;
        transform: translateY(-1px);
        transition: all 0.2s ease-in-out;
    }
    
    .search-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    #searchTerm {
        transition: all 0.3s ease;
    }
    
    #searchTerm:focus {
        transform: scale(1.02);
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 36px;
        padding-left: 12px;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }
    
    .select2-dropdown {
        min-width: 350px !important;
        max-width: 500px !important;
        z-index: 9999 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">إضافة نقل موظف جديد</h6>
                    <div>
                        <a href="{% url 'home:employee_transfer_management' %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left"></i> العودة لإدارة النقل
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <div class="search-section">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-search"></i> البحث عن الموظف
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="searchTerm" class="form-label">البحث عن الموظف</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchTerm"
                                           placeholder="أدخل الرقم الوزاري أو اسم الموظف واضغط Enter">
                                    <button class="btn btn-primary" type="button" id="searchBtn">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle"></i> يمكنك الضغط على مفتاح الإدخال (Enter) للبحث السريع
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <form method="post" id="addTransferForm">
                        {% csrf_token %}
                        
                        <div id="employeeDetails" style="display: none;">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">الرقم الوزاري</label>
                                    <input type="text" class="form-control" name="ministry_number" id="ministryNumber" readonly>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">الاسم الكامل</label>
                                    <input type="text" class="form-control" name="employee_name" id="employeeName" readonly>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">التخصص</label>
                                    <input type="text" class="form-control" name="specialization" id="specialization" readonly>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">الخدمة الفعلية</label>
                                    <input type="text" class="form-control" name="actual_service" id="actualService" readonly>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">القسم الحالي</label>
                                    <input type="text" class="form-control" name="current_department" id="currentDepartment" readonly>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">القسم الجديد <span class="text-danger">*</span></label>
                                    <select class="form-control select2" name="new_department_id" id="newDepartment" required>
                                        <option value="">اختر القسم الجديد...</option>
                                        {% for department in departments %}
                                        <option value="{{ department.id }}">{{ department.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">صفة النقل <span class="text-danger">*</span></label>
                                    <select class="form-control" name="transfer_type" id="transferType" required>
                                        <option value="">اختر صفة النقل...</option>
                                        <option value="internal_transfer">النقل الداخلي</option>
                                        <option value="temporary_assignment">التكليف المؤقت</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">التنسيب <span class="text-danger">*</span></label>
                                    <select class="form-control" name="endorsement" id="endorsement" required>
                                        <option value="">اختر التنسيب...</option>
                                        <option value="admin_financial_manager">تنسيب مدير الشؤون الادارية والمالية</option>
                                        <option value="hr_committee">تنسيب لجنة الموارد البشرية</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" id="notes" rows="2" 
                                              placeholder="أدخل الملاحظات (اختياري)"></textarea>
                                </div>
                            </div>
                            
                            <div class="text-end">
                                <a href="{% url 'home:employee_transfer_management' %}" class="btn btn-outline-primary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-outline-success">
                                    <i class="fas fa-save"></i> حفظ النقل
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'ابحث عن القسم الجديد...',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "لا توجد أقسام متاحة";
            },
            searching: function() {
                return "جاري البحث عن الأقسام...";
            }
        }
    });
    
    // Search employee function
    function searchEmployee() {
        const searchTerm = $('#searchTerm').val().trim();
        if (!searchTerm) {
            alert('يرجى إدخال الرقم الوزاري أو اسم الموظف');
            $('#searchTerm').focus();
            return;
        }

        const $searchBtn = $('#searchBtn');
        const originalBtnHtml = $searchBtn.html();
        $searchBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري البحث...');
        $searchBtn.prop('disabled', true);
        $('#searchTerm').prop('disabled', true);

        $.post('{% url "home:search_employee_for_transfer" %}', {
            'search_term': searchTerm,
            'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
        }, function(response) {
            if (response.success) {
                const emp = response.employee;
                $('#ministryNumber').val(emp.ministry_number);
                $('#employeeName').val(emp.full_name);
                $('#specialization').val(emp.specialization);
                $('#actualService').val(emp.actual_service);
                $('#currentDepartment').val(emp.current_department);
                $('#employeeDetails').show();

                const successMsg = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                    '<i class="fas fa-check-circle"></i> تم العثور على الموظف: ' + emp.full_name +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                    '</div>');
                $('.card-body').prepend(successMsg);

                setTimeout(function() {
                    successMsg.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);

                setTimeout(function() {
                    $('#newDepartment').focus();
                }, 100);
            } else {
                alert(response.error);
                $('#employeeDetails').hide();
                $('#searchTerm').focus();
            }
        }).fail(function() {
            alert('حدث خطأ أثناء البحث');
            $('#searchTerm').focus();
        }).always(function() {
            $searchBtn.html(originalBtnHtml);
            $searchBtn.prop('disabled', false);
            $('#searchTerm').prop('disabled', false);
        });
    }

    // Search on button click
    $('#searchBtn').click(function() {
        searchEmployee();
    });

    // Search on Enter key
    $('#searchTerm').keydown(function(e) {
        if (e.keyCode === 13) {
            e.preventDefault();
            searchEmployee();
        }
    });
});
</script>
{% endblock %}
